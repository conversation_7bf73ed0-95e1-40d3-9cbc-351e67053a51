'use client';

import { useState, useEffect } from 'react';

export default function ScheduleCalendar({ schedules, onScheduleSelect, onCreateSchedule }) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState('month'); // month, week, day
  const [selectedDate, setSelectedDate] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Get calendar data based on current view
  const getCalendarData = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    if (view === 'month') {
      return getMonthData(year, month);
    } else if (view === 'week') {
      return getWeekData(currentDate);
    } else {
      return getDayData(currentDate);
    }
  };

  const getMonthData = (year, month) => {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days = [];
    const current = new Date(startDate);
    
    for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }
    
    return days;
  };

  const getWeekData = (date) => {
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay());
    
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      days.push(day);
    }
    
    return days;
  };

  const getDayData = (date) => {
    return [new Date(date)];
  };

  const getSchedulesForDate = (date) => {
    const dateStr = date.toDateString();
    return schedules.filter(schedule => {
      const scheduleDate = new Date(schedule.start).toDateString();
      return scheduleDate === dateStr;
    });
  };

  const navigateCalendar = (direction) => {
    const newDate = new Date(currentDate);
    
    if (view === 'month') {
      newDate.setMonth(currentDate.getMonth() + direction);
    } else if (view === 'week') {
      newDate.setDate(currentDate.getDate() + (direction * 7));
    } else {
      newDate.setDate(currentDate.getDate() + direction);
    }
    
    setCurrentDate(newDate);
  };

  const formatDateHeader = () => {
    if (view === 'month') {
      return currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    } else if (view === 'week') {
      const weekData = getWeekData(currentDate);
      const start = weekData[0].toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const end = weekData[6].toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      return `${start} - ${end}, ${currentDate.getFullYear()}`;
    } else {
      return currentDate.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    }
  };

  const handleDateClick = (date) => {
    setSelectedDate(date);
    if (onScheduleSelect) {
      onScheduleSelect(date);
    }
  };

  const handleCreateSchedule = (date) => {
    setSelectedDate(date);
    setShowCreateModal(true);
  };

  const calendarData = getCalendarData();

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Calendar Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-medium text-gray-900">Schedule Calendar</h3>
            <div className="flex space-x-1">
              {['month', 'week', 'day'].map((viewType) => (
                <button
                  key={viewType}
                  onClick={() => setView(viewType)}
                  className={`px-3 py-1 text-sm rounded ${
                    view === viewType
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {viewType.charAt(0).toUpperCase() + viewType.slice(1)}
                </button>
              ))}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => navigateCalendar(-1)}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                ←
              </button>
              <span className="text-lg font-medium text-gray-900 min-w-[200px] text-center">
                {formatDateHeader()}
              </span>
              <button
                onClick={() => navigateCalendar(1)}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                →
              </button>
            </div>
            
            <button
              onClick={() => setCurrentDate(new Date())}
              className="px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded hover:bg-blue-50"
            >
              Today
            </button>
          </div>
        </div>
      </div>

      {/* Calendar Body */}
      <div className="p-6">
        {view === 'month' && (
          <div className="grid grid-cols-7 gap-1">
            {/* Day headers */}
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                {day}
              </div>
            ))}
            
            {/* Calendar days */}
            {calendarData.map((date, index) => {
              const daySchedules = getSchedulesForDate(date);
              const isCurrentMonth = date.getMonth() === currentDate.getMonth();
              const isToday = date.toDateString() === new Date().toDateString();
              const isSelected = selectedDate && date.toDateString() === selectedDate.toDateString();
              
              return (
                <div
                  key={index}
                  onClick={() => handleDateClick(date)}
                  className={`min-h-[100px] p-2 border border-gray-200 cursor-pointer hover:bg-gray-50 ${
                    !isCurrentMonth ? 'bg-gray-50 text-gray-400' : ''
                  } ${isToday ? 'bg-blue-50 border-blue-200' : ''} ${
                    isSelected ? 'ring-2 ring-blue-500' : ''
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <span className={`text-sm ${isToday ? 'font-bold text-blue-600' : ''}`}>
                      {date.getDate()}
                    </span>
                    {isCurrentMonth && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCreateSchedule(date);
                        }}
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        +
                      </button>
                    )}
                  </div>
                  
                  <div className="mt-1 space-y-1">
                    {daySchedules.slice(0, 3).map((schedule) => (
                      <div
                        key={schedule.id}
                        onClick={(e) => {
                          e.stopPropagation();
                          onScheduleSelect && onScheduleSelect(schedule);
                        }}
                        className={`text-xs p-1 rounded truncate ${
                          schedule.status === 'completed' ? 'bg-green-100 text-green-800' :
                          schedule.status === 'active' ? 'bg-blue-100 text-blue-800' :
                          schedule.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {schedule.title}
                      </div>
                    ))}
                    {daySchedules.length > 3 && (
                      <div className="text-xs text-gray-500">
                        +{daySchedules.length - 3} more
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {view === 'week' && (
          <div className="grid grid-cols-7 gap-1">
            {calendarData.map((date, index) => {
              const daySchedules = getSchedulesForDate(date);
              const isToday = date.toDateString() === new Date().toDateString();
              const isSelected = selectedDate && date.toDateString() === selectedDate.toDateString();
              
              return (
                <div key={index} className="border border-gray-200">
                  <div className={`p-2 text-center border-b ${isToday ? 'bg-blue-50' : 'bg-gray-50'}`}>
                    <div className="text-xs text-gray-500">
                      {date.toLocaleDateString('en-US', { weekday: 'short' })}
                    </div>
                    <div className={`text-lg ${isToday ? 'font-bold text-blue-600' : ''}`}>
                      {date.getDate()}
                    </div>
                  </div>
                  
                  <div className="p-2 min-h-[300px] space-y-1">
                    {daySchedules.map((schedule) => {
                      const startTime = new Date(schedule.start).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit'
                      });
                      
                      return (
                        <div
                          key={schedule.id}
                          onClick={() => onScheduleSelect && onScheduleSelect(schedule)}
                          className={`text-xs p-2 rounded cursor-pointer ${
                            schedule.status === 'completed' ? 'bg-green-100 text-green-800' :
                            schedule.status === 'active' ? 'bg-blue-100 text-blue-800' :
                            schedule.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          <div className="font-medium">{startTime}</div>
                          <div className="truncate">{schedule.title}</div>
                        </div>
                      );
                    })}
                    
                    <button
                      onClick={() => handleCreateSchedule(date)}
                      className="w-full text-xs text-blue-600 border border-dashed border-blue-300 rounded p-2 hover:bg-blue-50"
                    >
                      + Add Schedule
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {view === 'day' && (
          <div className="space-y-4">
            <div className="text-center">
              <h4 className="text-lg font-medium text-gray-900">
                {currentDate.toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </h4>
            </div>
            
            <div className="border border-gray-200 rounded">
              {/* Time slots */}
              {Array.from({ length: 24 }, (_, hour) => {
                const timeSlot = new Date(currentDate);
                timeSlot.setHours(hour, 0, 0, 0);
                
                const hourSchedules = schedules.filter(schedule => {
                  const scheduleHour = new Date(schedule.start).getHours();
                  const scheduleDate = new Date(schedule.start).toDateString();
                  return scheduleHour === hour && scheduleDate === currentDate.toDateString();
                });
                
                return (
                  <div key={hour} className="flex border-b border-gray-100">
                    <div className="w-20 p-2 text-sm text-gray-500 border-r">
                      {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
                    </div>
                    <div className="flex-1 p-2 min-h-[60px]">
                      {hourSchedules.map((schedule) => (
                        <div
                          key={schedule.id}
                          onClick={() => onScheduleSelect && onScheduleSelect(schedule)}
                          className={`p-2 rounded mb-1 cursor-pointer ${
                            schedule.status === 'completed' ? 'bg-green-100 text-green-800' :
                            schedule.status === 'active' ? 'bg-blue-100 text-blue-800' :
                            schedule.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          <div className="font-medium">{schedule.title}</div>
                          <div className="text-xs">
                            {new Date(schedule.start).toLocaleTimeString('en-US', {
                              hour: 'numeric',
                              minute: '2-digit'
                            })} - {new Date(schedule.end).toLocaleTimeString('en-US', {
                              hour: 'numeric',
                              minute: '2-digit'
                            })}
                          </div>
                        </div>
                      ))}
                      
                      {hourSchedules.length === 0 && (
                        <button
                          onClick={() => {
                            const scheduleTime = new Date(currentDate);
                            scheduleTime.setHours(hour, 0, 0, 0);
                            handleCreateSchedule(scheduleTime);
                          }}
                          className="w-full h-full text-xs text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded"
                        >
                          + Add Schedule
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Create Schedule Modal */}
      {showCreateModal && (
        <CreateScheduleModal
          selectedDate={selectedDate}
          onClose={() => setShowCreateModal(false)}
          onCreate={onCreateSchedule}
        />
      )}
    </div>
  );
}

function CreateScheduleModal({ selectedDate, onClose, onCreate }) {
  const [formData, setFormData] = useState({
    title: '',
    start_time: '',
    srp_target: 100,
    job_type: 'sparx_reader',
    job_data: {
      school: '',
      email: '',
      password: ''
    }
  });

  useEffect(() => {
    if (selectedDate) {
      const defaultTime = new Date(selectedDate);
      defaultTime.setHours(9, 0, 0, 0); // Default to 9 AM
      setFormData(prev => ({
        ...prev,
        start_time: defaultTime.toISOString().slice(0, 16)
      }));
    }
  }, [selectedDate]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (onCreate) {
      onCreate({
        scheduled_time: formData.start_time,
        srp_target: formData.srp_target,
        job_type: formData.job_type,
        job_data: formData.job_data
      });
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Create Schedule</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Title</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Start Time</label>
            <input
              type="datetime-local"
              value={formData.start_time}
              onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">SRP Target (max 400)</label>
            <input
              type="number"
              min="1"
              max="400"
              value={formData.srp_target}
              onChange={(e) => setFormData(prev => ({ ...prev, srp_target: parseInt(e.target.value) || 1 }))}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter SRP target (1-400)"
              required
            />
            <p className="mt-1 text-sm text-gray-500">
              Browser will automatically close when this SRP target is reached.
            </p>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Job Details</label>
            <input
              type="text"
              placeholder="School"
              value={formData.job_data.school}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                job_data: { ...prev.job_data, school: e.target.value }
              }))}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
            <input
              type="email"
              placeholder="Email"
              value={formData.job_data.email}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                job_data: { ...prev.job_data, email: e.target.value }
              }))}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
            <input
              type="password"
              placeholder="Password"
              value={formData.job_data.password}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                job_data: { ...prev.job_data, password: e.target.value }
              }))}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Create Schedule
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}