"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.jsx":
/*!**********************!*\
  !*** ./app/page.jsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SparxReaderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SparxReaderPage() {\n    _s();\n    // Authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [authLoading, setAuthLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bookTitle, setBookTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [needsPlaywright, setNeedsPlaywright] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [screenshot, setScreenshot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [storyContent, setStoryContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showBookConfirmation, setShowBookConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSrp, setCurrentSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetSrp, setTargetSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showInitialSrpInput, setShowInitialSrpInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for enhanced UI\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentAnswer, setCurrentAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [questionNumber, setQuestionNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [srpEarned, setSrpEarned] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutomationRunning, setIsAutomationRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionHistory, setQuestionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [animationKey, setAnimationKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [automationComplete, setAutomationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [queuePosition, setQueuePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobId, setJobId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInQueue, setIsInQueue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginMethod, setLoginMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\"); // 'normal' or 'microsoft'\n    // Credential system states\n    const [showCredentialInput, setShowCredentialInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [credentialMode, setCredentialMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"enter\"); // 'enter' or 'key'\n    const [userSchool, setUserSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userEmail, setUserEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userPassword, setUserPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loginKey, setLoginKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [savedCredentials, setSavedCredentials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // License renewal states\n    const [showLicenseRenewal, setShowLicenseRenewal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [licenseStatus, setLicenseStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newLicenseKey, setNewLicenseKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [licenseRenewalLoading, setLicenseRenewalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check authentication on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuthentication();\n    }, []);\n    // Simulate question-solving process AFTER automation completes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated || !isAutomationRunning || !automationComplete) return;\n        if (isAutomationRunning && automationComplete) {\n            const interval = setInterval(()=>{\n                // Simulate question solving progress\n                setQuestionNumber((prev)=>{\n                    const newNum = prev + 1;\n                    setSrpEarned((prevSrp)=>prevSrp + Math.floor(Math.random() * 3) + 2);\n                    setAnimationKey((prevKey)=>prevKey + 1);\n                    // Simulate new question\n                    const sampleQuestions = [\n                        \"What was the main character's motivation in chapter 3?\",\n                        \"How did the setting influence the story's outcome?\",\n                        \"What literary device was used in the opening paragraph?\",\n                        \"Why did the protagonist make that crucial decision?\",\n                        \"What theme is most prominent throughout the narrative?\",\n                        \"How does the author develop the central conflict?\",\n                        \"What role does symbolism play in the narrative?\",\n                        \"are these questions fake and is your\"\n                    ];\n                    const sampleAnswers = [\n                        \"To find their lost family member\",\n                        \"The harsh winter created urgency\",\n                        \"Metaphor and symbolism\",\n                        \"To protect their friends\",\n                        \"The importance of friendship\",\n                        \"Through escalating tension\",\n                        \"It reinforces the main themes\",\n                        \"They grow through adversity\"\n                    ];\n                    const randomIndex = Math.floor(Math.random() * sampleQuestions.length);\n                    setCurrentQuestion(sampleQuestions[randomIndex]);\n                    setCurrentAnswer(sampleAnswers[randomIndex]);\n                    // Add to history\n                    setQuestionHistory((prev)=>[\n                            ...prev,\n                            {\n                                number: newNum,\n                                question: sampleQuestions[randomIndex],\n                                answer: sampleAnswers[randomIndex]\n                            }\n                        ]);\n                    // Stop after reaching target or max questions\n                    if (newNum >= 8 || srpEarned >= parseInt(targetSrp || 50)) {\n                        setTimeout(()=>{\n                            setIsAutomationRunning(false);\n                            setMessage(\"Target SRP reached! Automation completed successfully.\");\n                        }, 1500); // Show the last question for a bit\n                        clearInterval(interval);\n                        return newNum;\n                    }\n                    return newNum;\n                });\n            }, 2500); // Show new question every 2.5 seconds\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isAuthenticated,\n        isAutomationRunning,\n        automationComplete,\n        srpEarned,\n        targetSrp\n    ]);\n    // Authentication functions\n    const checkAuthentication = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const response = await fetch(\"/api/auth/validate\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success && data.valid) {\n                setIsAuthenticated(true);\n                setUser(data.user);\n                setLicenseStatus(data.licenseStatus);\n            } else {\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"user\");\n                router.push(\"/login\");\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        } finally{\n            setAuthLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        }\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    // Handle login method selection (doesn't start the process)\n    const handleLoginMethodSelect = (method)=>{\n        setLoginMethod(method);\n    };\n    // Check license validity before starting bot\n    const checkLicenseValidity = ()=>{\n        // Admin users don't need license validation\n        if (user && user.role === \"admin\") {\n            return {\n                valid: true\n            };\n        }\n        if (!licenseStatus) {\n            return {\n                valid: false,\n                error: \"License information not available\"\n            };\n        }\n        if (licenseStatus.license_status !== \"valid\") {\n            let errorMessage = \"Your license is not valid\";\n            switch(licenseStatus.license_status){\n                case \"expired\":\n                    errorMessage = \"Your license has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"Your license has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"Your license is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage,\n                status: licenseStatus.license_status\n            };\n        }\n        return {\n            valid: true\n        };\n    };\n    // Handle license renewal\n    const handleLicenseRenewal = async ()=>{\n        if (!newLicenseKey.trim()) {\n            setMessage(\"Please enter a valid license key\");\n            return;\n        }\n        setLicenseRenewalLoading(true);\n        setMessage(\"Renewing license...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/auth/renew-license\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    newLicenseKey: newLicenseKey.trim()\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(\"License renewed successfully! You can now start the bot.\");\n                setShowLicenseRenewal(false);\n                setNewLicenseKey(\"\");\n                // Refresh authentication to get updated license status\n                await checkAuthentication();\n            } else {\n                setMessage(data.error || \"Failed to renew license\");\n            }\n        } catch (error) {\n            setMessage(\"Error occurred while renewing license\");\n        } finally{\n            setLicenseRenewalLoading(false);\n        }\n    };\n    // Handle the actual start process\n    const handleBeginClick = async ()=>{\n        // Check authentication before starting\n        if (!isAuthenticated || !user) {\n            setMessage(\"Please login to use this feature\");\n            router.push(\"/login\");\n            return;\n        }\n        // Check license validity before proceeding\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            return;\n        }\n        // Reset all states\n        setMessage(\"\");\n        setBookTitle(\"\");\n        setNeedsPlaywright(false);\n        setScreenshot(\"\");\n        setStoryContent(\"\");\n        setShowBookConfirmation(false);\n        setCurrentSrp(\"\");\n        setTargetSrp(\"\");\n        setShowInitialSrpInput(false);\n        setAutomationComplete(false);\n        // Reset credential states\n        setUserSchool(\"\");\n        setUserEmail(\"\");\n        setUserPassword(\"\");\n        setLoginKey(\"\");\n        // Mark that user has started the process\n        setHasStarted(true);\n        // Show credential input first\n        setShowCredentialInput(true);\n        // Load user's saved credentials\n        await loadSavedCredentials();\n    };\n    // Load user's saved credentials\n    const loadSavedCredentials = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/credentials/list\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSavedCredentials(data.credentials);\n            }\n        } catch (error) {\n            console.error(\"Failed to load saved credentials:\", error);\n        }\n    };\n    // Handle credential submission\n    const handleCredentialSubmit = async ()=>{\n        if (credentialMode === \"key\") {\n            // Use saved credentials with login key\n            if (!loginKey) {\n                setMessage(\"Please enter your login key\");\n                return;\n            }\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    // Proceed with automation using saved credentials\n                    setShowCredentialInput(false);\n                    setMessage(\"Please enter how much SRP you need to earn:\");\n                    setShowInitialSrpInput(true);\n                } else {\n                    setMessage(data.error || \"Invalid login key\");\n                }\n            } catch (error) {\n                setMessage(\"Error retrieving credentials\");\n            }\n        } else {\n            // Use entered credentials\n            if (!userSchool || !userEmail || !userPassword) {\n                setMessage(\"Please enter school, email and password\");\n                return;\n            }\n            // Ask if user wants to save credentials\n            if (confirm(\"Would you like to save these credentials for future use? You will receive a secure login key.\")) {\n                try {\n                    const token = localStorage.getItem(\"token\");\n                    const response = await fetch(\"/api/credentials/save\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            \"Authorization\": \"Bearer \".concat(token)\n                        },\n                        body: JSON.stringify({\n                            loginMethod,\n                            school: userSchool,\n                            email: userEmail,\n                            password: userPassword\n                        })\n                    });\n                    const data = await response.json();\n                    if (data.success) {\n                        alert(\"Credentials saved! Your login key is: \".concat(data.loginKey, \"\\n\\nPlease save this key securely. You can use it for future logins.\"));\n                    }\n                } catch (error) {\n                    console.error(\"Failed to save credentials:\", error);\n                }\n            }\n            // Proceed with automation\n            setShowCredentialInput(false);\n            setMessage(\"Please enter how much SRP you need to earn:\");\n            setShowInitialSrpInput(true);\n        }\n    };\n    const handleSrpSubmit = async ()=>{\n        // Validate SRP input\n        if (!targetSrp || isNaN(targetSrp) || parseInt(targetSrp) <= 0) {\n            setMessage(\"Please enter a valid SRP target (positive number)\");\n            return;\n        }\n        // Check license validity again before starting automation\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            setShowInitialSrpInput(false);\n            return;\n        }\n        setLoading(true);\n        setShowInitialSrpInput(false);\n        const isNormalLogin = loginMethod === \"normal\";\n        const isMicrosoftLogin = loginMethod === \"microsoft\";\n        const isGoogleLogin = loginMethod === \"google\";\n        // Get credentials\n        let credentials = null;\n        if (credentialMode === \"key\" && loginKey) {\n            // Get credentials from login key\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    credentials = data.credentials;\n                } else {\n                    setLoading(false);\n                    setMessage(data.error || \"Failed to retrieve credentials\");\n                    return;\n                }\n            } catch (error) {\n                setLoading(false);\n                setMessage(\"Error retrieving credentials\");\n                return;\n            }\n        } else if (credentialMode === \"enter\" && userSchool && userEmail && userPassword) {\n            // Use entered credentials\n            credentials = {\n                school: userSchool,\n                email: userEmail,\n                password: userPassword,\n                loginMethod: loginMethod\n            };\n        } else {\n            setLoading(false);\n            setMessage(\"No credentials available\");\n            return;\n        }\n        if (isNormalLogin) {\n            setMessage(\"Preparing to start...\");\n        } else if (isMicrosoftLogin) {\n            setMessage(\"Starting Microsoft login automation...\");\n        } else if (isGoogleLogin) {\n            setMessage(\"Starting Google login automation...\");\n        }\n        try {\n            let apiEndpoint, requestBody;\n            if (isNormalLogin) {\n                apiEndpoint = \"/api/sparxreader/start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isMicrosoftLogin) {\n                apiEndpoint = \"/api/sparxreader/microsoft-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isGoogleLogin) {\n                apiEndpoint = \"/api/sparxreader/google-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            }\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(apiEndpoint, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Book title and SRP extracted, show confirmation dialog\n                setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                setBookTitle(data.bookTitle);\n                setCurrentSrp(data.currentSrp);\n                setShowBookConfirmation(true);\n                if (data.screenshot) {\n                    setScreenshot(data.screenshot);\n                }\n            } else {\n                setMessage(data.error || \"Failed to start\");\n                if (data.needsPlaywright) {\n                    setNeedsPlaywright(true);\n                }\n            }\n        } catch (error) {\n            setMessage(\"Error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleYesClick = async ()=>{\n        setLoading(true);\n        setShowBookConfirmation(false);\n        setIsInQueue(true);\n        setMessage(\"Adding to queue...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            // Create a queue job instead of direct automation\n            const response = await fetch(\"/api/queue/jobs\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    job_type: \"sparx_reader\",\n                    job_data: {\n                        school: selectedSchool,\n                        email: email,\n                        username: username,\n                        password: password,\n                        login_type: loginMethod,\n                        bookTitle: bookTitle\n                    },\n                    srp_target: targetSrp ? parseInt(targetSrp) : 100,\n                    priority: 0\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setJobId(data.job_id);\n                setMessage(\"Added to queue! Checking position...\");\n                // Start monitoring queue position\n                startQueueMonitoring(data.job_id);\n            } else {\n                setIsInQueue(false);\n                setMessage(data.error || \"Failed to add job to queue\");\n            }\n        } catch (error) {\n            setIsInQueue(false);\n            setMessage(\"Error occurred while adding to queue\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNoClick = async ()=>{\n        setLoading(true);\n        setMessage(\"Finding a different book with same SRP target...\");\n        try {\n            // Close current session\n            const token = localStorage.getItem(\"token\");\n            await fetch(\"/api/sparxreader/close\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            // Restart automation with existing target SRP\n            setLoading(true);\n            setShowInitialSrpInput(false);\n            setMessage(\"Preparing to start with new book...\");\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/sparxreader/start\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                        targetSrp: parseInt(targetSrp)\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                    setBookTitle(data.bookTitle);\n                    setCurrentSrp(data.currentSrp);\n                    setShowBookConfirmation(true);\n                    if (data.screenshot) {\n                        setScreenshot(data.screenshot);\n                    }\n                } else {\n                    setMessage(data.error || \"Failed to start\");\n                    if (data.needsPlaywright) {\n                        setNeedsPlaywright(true);\n                    }\n                }\n            } catch (error) {\n                setMessage(\"Error occurred while restarting\");\n            }\n        } catch (error) {\n            setMessage(\"Error closing previous session\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBackClick = ()=>{\n        setIsAutomationRunning(false);\n        setIsProcessing(false);\n        setCurrentQuestion(\"\");\n        setCurrentAnswer(\"\");\n        setQuestionNumber(0);\n        setSrpEarned(0);\n        setQuestionHistory([]);\n        setMessage(\"\");\n        setShowBookConfirmation(false);\n        setShowInitialSrpInput(false);\n        setHasStarted(false);\n        setAutomationComplete(false);\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 674,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 676,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 677,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 675,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 673,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 691,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"p-6 border-b border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto relative\",\n                            children: [\n                                (isAutomationRunning || isProcessing) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackClick,\n                                    className: \"absolute left-0 top-1/2 transform -translate-y-1/2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-all duration-200 flex items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"<\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 top-1/2 transform -translate-y-1/2 flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        user === null || user === void 0 ? void 0 : user.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"Administrator\" : \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                (user === null || user === void 0 ? void 0 : user.role) === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/admin\"),\n                                                    className: \"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Admin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/queue\"),\n                                                    className: \"px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Queue\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogout,\n                                                    className: \"px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-light text-white text-center\",\n                                    children: \"Sparx Reader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-center text-gray-400 mt-1 text-sm\",\n                                    children: \"Automated Question Solving\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 696,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 695,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-4xl mx-auto\",\n                            children: [\n                                isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-8 text-center shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-20 h-20 mx-auto mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 border-4 border-blue-500/30 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 bg-blue-500 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.5s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4\",\n                                                children: \"\\uD83D\\uDE80 Processing Automation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-6 text-lg\",\n                                                children: \"AI is solving questions in the background...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full max-w-md mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 border-4 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"Processing your request...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 15\n                                }, this),\n                                isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                    children: \"\\uD83D\\uDCCA Live Progress Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-24 h-24 mx-auto mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-24 h-24 transform -rotate-90\",\n                                                                        viewBox: \"0 0 100 100\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                className: \"text-gray-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 798,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"url(#srpGradient)\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                strokeDasharray: \"\".concat(srpEarned / parseInt(targetSrp || 1) * 251.2, \" 251.2\"),\n                                                                                className: \"transition-all duration-1000 ease-out drop-shadow-lg\",\n                                                                                strokeLinecap: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 799,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                                    id: \"srpGradient\",\n                                                                                    x1: \"0%\",\n                                                                                    y1: \"0%\",\n                                                                                    x2: \"100%\",\n                                                                                    y2: \"100%\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"0%\",\n                                                                                            stopColor: \"#10B981\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 810,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"50%\",\n                                                                                            stopColor: \"#3B82F6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 811,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"100%\",\n                                                                                            stopColor: \"#8B5CF6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 812,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 809,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 808,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xl font-bold text-white block\",\n                                                                                    children: srpEarned\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 818,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: \"SRP\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 817,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 rounded-full bg-gradient-to-r from-green-500/20 to-blue-500/20 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 823,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium mb-1\",\n                                                                children: \"SRP Earned\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-400 text-xs\",\n                                                                children: [\n                                                                    \"Target: \",\n                                                                    targetSrp\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    Math.round(srpEarned / parseInt(targetSrp || 1) * 100),\n                                                                    \"% Complete\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold text-blue-400 mb-1 animate-pulse\",\n                                                                        children: questionNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"Questions Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 836,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-700 rounded-full h-3 overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-blue-500 to-cyan-400 h-3 rounded-full transition-all duration-1000 ease-out relative\",\n                                                                            style: {\n                                                                                width: \"\".concat(Math.min(questionNumber / 10 * 100, 100), \"%\")\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-white/30 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 842,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 838,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 837,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            Math.min(Math.round(questionNumber / 10 * 100), 100),\n                                                                            \"% of estimated session\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83E\\uDDE0 AI Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mx-auto mb-3 flex items-center justify-center animate-pulse\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"System Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-700 rounded-lg p-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-green-400 mb-2\",\n                                                                                children: \"\\uD83D\\uDFE2 Active & Processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 861,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400 mb-1\",\n                                                                                children: \"Performance Metrics\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 862,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-white\",\n                                                                                children: [\n                                                                                    \"⚡ \",\n                                                                                    questionNumber > 0 ? Math.round(elapsedTime / questionNumber) : 0,\n                                                                                    \"s per question\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 863,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-400\",\n                                                                                children: [\n                                                                                    \"\\uD83C\\uDFAF \",\n                                                                                    questionNumber > 0 ? Math.round(questionNumber / elapsedTime * 60) : 0,\n                                                                                    \" questions/min\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 866,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-purple-400 mt-1\",\n                                                                                children: [\n                                                                                    \"⏱️ \",\n                                                                                    formatTime(elapsedTime),\n                                                                                    \" elapsed\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 869,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 854,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83D\\uDE80 AI Engine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 15\n                                }, this),\n                                currentQuestion && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-slide-up\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4 animate-pulse\",\n                                                        children: questionNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                                children: \"✅ Question Solved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"AI successfully processed this question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-auto flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400 font-medium\",\n                                                                children: \"COMPLETED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 885,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/60 border border-gray-600 rounded-xl p-6 mb-6 backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"Q\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-base leading-relaxed mb-3\",\n                                                                    children: currentQuestion\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 bg-green-600 text-white text-xs rounded font-medium\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                Math.floor(Math.random() * 3) + 1,\n                                                                                \" SRP\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 909,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"• Solved automatically\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 912,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 908,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentAnswer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"AI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-400 font-medium mb-2\",\n                                                                    children: \"\\uD83E\\uDD16 AI Solution:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-200 leading-relaxed\",\n                                                                    children: currentAnswer\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 920,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 17\n                                    }, this)\n                                }, animationKey, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 15\n                                }, this),\n                                !hasStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-10 max-w-lg mx-auto shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-16 h-16 mx-auto mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-bounce\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-2xl\",\n                                                                    children: \"\\uD83D\\uDE80\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 942,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-purple-400 rounded-full animate-ping\",\n                                                                style: {\n                                                                    animationDelay: \"0.5s\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3\",\n                                                        children: \"Start Automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-base\",\n                                                        children: \"\\uD83E\\uDD16 AI-powered question solving for Sparx Reader\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                        children: \"\\uD83D\\uDD10 Choose Login Method\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 955,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"normal\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"normal\" ? \"bg-gradient-to-r from-green-600 to-green-500 text-white ring-2 ring-green-400 shadow-lg shadow-green-500/25\" : \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-500 text-white shadow-lg hover:shadow-green-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDC64\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 968,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Normal Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 969,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"normal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 970,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"microsoft\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"microsoft\" ? \"bg-gradient-to-r from-blue-600 to-blue-500 text-white ring-2 ring-blue-400 shadow-lg shadow-blue-500/25\" : \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-500 text-white shadow-lg hover:shadow-blue-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83C\\uDFE2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Microsoft Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 983,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"microsoft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"google\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"google\" ? \"bg-gradient-to-r from-red-600 to-red-500 text-white ring-2 ring-red-400 shadow-lg shadow-red-500/25\" : \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-500 text-white shadow-lg hover:shadow-red-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDD0D\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Google Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"google\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 998,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: [\n                                                                \"Selected: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        loginMethod === \"normal\" && \"Normal Login\",\n                                                                        loginMethod === \"microsoft\" && \"Microsoft Login\",\n                                                                        loginMethod === \"google\" && \"Google Login\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1005,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBeginClick,\n                                                        disabled: loading,\n                                                        className: \"w-full py-3 px-6 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Begin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 938,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 15\n                                }, this),\n                                showInitialSrpInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1031,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Set SRP Target\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"How much SRP do you want to earn?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1029,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: targetSrp,\n                                                        onChange: (e)=>setTargetSrp(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleSrpSubmit(),\n                                                        placeholder: \"Enter target (e.g., 50)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center\",\n                                                        min: \"1\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 text-center\",\n                                                        children: \"Automation will stop when target is reached\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSrpSubmit,\n                                                                disabled: loading || !targetSrp,\n                                                                className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1060,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Starting...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1059,\n                                                                    columnNumber: 27\n                                                                }, this) : \"Start\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowInitialSrpInput(false),\n                                                                disabled: loading,\n                                                                className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1027,\n                                    columnNumber: 15\n                                }, this),\n                                showCredentialInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"\\uD83D\\uDD10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1084,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1083,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Login Credentials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1086,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            loginMethod === \"normal\" && \"Enter your Sparx Learning credentials\",\n                                                            loginMethod === \"microsoft\" && \"Enter your Microsoft account credentials\",\n                                                            loginMethod === \"google\" && \"Enter your Google account credentials\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1087,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex bg-gray-800 rounded-lg p-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"enter\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"enter\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Enter Credentials\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1097,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"key\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"key\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Use Login Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 19\n                                            }, this),\n                                            credentialMode === \"enter\" ? /* Enter Credentials Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: userSchool,\n                                                        onChange: (e)=>setUserSchool(e.target.value),\n                                                        placeholder: \"School name (e.g., theangmeringschool)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: userEmail,\n                                                        onChange: (e)=>setUserEmail(e.target.value),\n                                                        placeholder: \"Email address\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1131,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        value: userPassword,\n                                                        onChange: (e)=>setUserPassword(e.target.value),\n                                                        placeholder: \"Password\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDCA1 Your credentials will be encrypted and stored securely. You'll receive a login key for future use.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1145,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1122,\n                                                columnNumber: 21\n                                            }, this) : /* Use Login Key Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: loginKey,\n                                                        onChange: (e)=>setLoginKey(e.target.value),\n                                                        placeholder: \"Enter your login key (e.g., SLK-ABC12345)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center font-mono\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    savedCredentials.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mb-2\",\n                                                                children: \"Your saved login keys:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1 max-h-32 overflow-y-auto\",\n                                                                children: savedCredentials.map((cred, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setLoginKey(cred.loginKey),\n                                                                        className: \"w-full text-left px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-sm text-gray-300 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-mono text-blue-400\",\n                                                                                children: cred.loginKey\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1171,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-2 text-xs\",\n                                                                                children: [\n                                                                                    \"(\",\n                                                                                    cred.loginMethod,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1172,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1166,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1164,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1162,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDD11 Use your previously generated login key to access saved credentials.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1179,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1151,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCredentialSubmit,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1193,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1192,\n                                                            columnNumber: 25\n                                                        }, this) : \"Continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1186,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowCredentialInput(false);\n                                                            setHasStarted(false);\n                                                        },\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1080,\n                                    columnNumber: 15\n                                }, this),\n                                showBookConfirmation && bookTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-lg w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"B\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1219,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1218,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Book Found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Confirm to start automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1222,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1217,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/30 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-white mb-4\",\n                                                        children: bookTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1226,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Current SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: currentSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1230,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Target SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1233,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: targetSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1234,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1232,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleYesClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1247,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Starting...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1246,\n                                                            columnNumber: 25\n                                                        }, this) : \"Yes, Start\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleNoClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? \"Finding...\" : \"Find Different\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1252,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1239,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1216,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 15\n                                }, this),\n                                questionHistory.length > 0 && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"H\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Question History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-60 overflow-y-auto\",\n                                                children: questionHistory.slice(-5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/30 rounded p-3 border-l-4 border-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 font-medium text-sm\",\n                                                                        children: [\n                                                                            \"Q\",\n                                                                            item.number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1276,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 text-xs\",\n                                                                        children: \"Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1277,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1275,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm mb-2\",\n                                                                children: [\n                                                                    item.question.substring(0, 100),\n                                                                    \"...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1279,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: [\n                                                                    \"Answer: \",\n                                                                    item.answer\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1274,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1267,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 15\n                                }, this),\n                                message && !showInitialSrpInput && !showBookConfirmation && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded p-4 inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white\",\n                                            children: message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 1292,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1291,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1290,\n                                    columnNumber: 15\n                                }, this),\n                                needsPlaywright && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-red-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1302,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Setup Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Playwright browsers need to be installed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1301,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-sm mb-2\",\n                                                        children: \"Run this command in your terminal:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-black text-blue-400 p-2 rounded block text-sm\",\n                                                        children: \"npx playwright install chromium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1311,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-xs text-center\",\n                                                children: \"After installation, refresh this page and try again.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1316,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1300,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1299,\n                                    columnNumber: 15\n                                }, this),\n                                showLicenseRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-8 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-500 text-2xl\",\n                                                            children: \"⚠️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1328,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-medium text-white mb-2\",\n                                                        children: \"License Renewal Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            licenseStatus && licenseStatus.license_status === \"expired\" && \"Your license has expired.\",\n                                                            licenseStatus && licenseStatus.license_status === \"maxed_out\" && \"Your license has reached maximum uses.\",\n                                                            licenseStatus && licenseStatus.license_status === \"inactive\" && \"Your license is inactive.\",\n                                                            (!licenseStatus || licenseStatus.license_status === \"valid\") && \"Your license is not valid.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1332,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mt-2\",\n                                                        children: \"Please enter a new license key to continue using the bot.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1327,\n                                                columnNumber: 19\n                                            }, this),\n                                            licenseStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 border border-gray-700 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-white font-medium mb-2\",\n                                                        children: \"Current License Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1345,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Key: \",\n                                                                    licenseStatus.key_code || \"N/A\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1347,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium \".concat(licenseStatus.license_status === \"expired\" ? \"text-red-400\" : licenseStatus.license_status === \"maxed_out\" ? \"text-orange-400\" : licenseStatus.license_status === \"inactive\" ? \"text-gray-400\" : \"text-green-400\"),\n                                                                        children: [\n                                                                            licenseStatus.license_status === \"expired\" && \"Expired\",\n                                                                            licenseStatus.license_status === \"maxed_out\" && \"Max Uses Reached\",\n                                                                            licenseStatus.license_status === \"inactive\" && \"Inactive\",\n                                                                            licenseStatus.license_status === \"valid\" && \"Valid\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1348,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            licenseStatus.expires_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Expires: \",\n                                                                    new Date(licenseStatus.expires_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1360,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            licenseStatus.max_uses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Uses: \",\n                                                                    licenseStatus.current_uses || 0,\n                                                                    \"/\",\n                                                                    licenseStatus.max_uses\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1344,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-white font-medium mb-2\",\n                                                        children: \"New License Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newLicenseKey,\n                                                        onChange: (e)=>setNewLicenseKey(e.target.value),\n                                                        placeholder: \"Enter your new license key (e.g., SRX-XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXXXX)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:border-blue-500 focus:outline-none\",\n                                                        disabled: licenseRenewalLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1371,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1369,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLicenseRenewal,\n                                                        disabled: licenseRenewalLoading || !newLicenseKey.trim(),\n                                                        className: \"flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded transition-colors duration-200\",\n                                                        children: licenseRenewalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1389,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Renewing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1388,\n                                                            columnNumber: 25\n                                                        }, this) : \"Renew License\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowLicenseRenewal(false);\n                                                            setNewLicenseKey(\"\");\n                                                            setMessage(\"\");\n                                                        },\n                                                        disabled: licenseRenewalLoading,\n                                                        className: \"px-6 py-3 border border-gray-600 text-gray-400 hover:text-white hover:border-gray-500 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1396,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1381,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1326,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1325,\n                                    columnNumber: 15\n                                }, this),\n                                screenshot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"S\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Browser State\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1416,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded border border-gray-700 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: screenshot,\n                                                    alt: \"Browser screenshot\",\n                                                    className: \"w-full h-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1421,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1420,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1415,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1414,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 747,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 746,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 693,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n        lineNumber: 689,\n        columnNumber: 5\n    }, this);\n}\n_s(SparxReaderPage, \"3tPyRYBhGPqvkw99Z4exibXXewo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SparxReaderPage;\nvar _c;\n$RefreshReg$(_c, \"SparxReaderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.jsx\n"));

/***/ })

});