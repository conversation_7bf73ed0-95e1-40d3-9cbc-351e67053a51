const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const path = require('path');

class DatabaseManager {
  constructor() {
    const dbPath = path.join(process.cwd(), 'data', 'app.db');
    this.db = new Database(dbPath);
    this.initializeTables();
    this.createDefaultAdmin();
  }

  initializeTables() {
    // Users table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT DEFAULT 'user' CHECK(role IN ('user', 'admin')),
        license_key_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        last_login DATETIME,
        <PERSON>OREI<PERSON><PERSON> KEY (license_key_id) REFERENCES license_keys (id)
      )
    `);

    // License keys table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS license_keys (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key_code TEXT UNIQUE NOT NULL,
        duration_days INTEGER NOT NULL,
        max_uses INTEGER NOT NULL DEFAULT 1,
        current_uses INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_by INTEGER,
        features TEXT DEFAULT '[]',
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `);

    // User sessions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        token_hash TEXT NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        ip_address TEXT,
        user_agent TEXT,
        is_active BOOLEAN DEFAULT 1,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Activity logs table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS activity_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action TEXT NOT NULL,
        details TEXT,
        ip_address TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // System settings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_by INTEGER,
        FOREIGN KEY (updated_by) REFERENCES users (id)
      )
    `);

    // Encrypted login credentials table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS encrypted_credentials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        login_key TEXT UNIQUE NOT NULL,
        login_method TEXT NOT NULL CHECK(login_method IN ('normal', 'microsoft', 'google')),
        encrypted_school TEXT NOT NULL,
        encrypted_email TEXT NOT NULL,
        encrypted_password TEXT NOT NULL,
        encryption_iv TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // License feature settings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS license_feature_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        license_key_id INTEGER NOT NULL,
        max_accounts_per_batch INTEGER DEFAULT 0,
        priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),
        scheduling_access BOOLEAN DEFAULT 0,
        multi_user_access BOOLEAN DEFAULT 0,
        max_batches_per_day INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (license_key_id) REFERENCES license_keys (id),
        UNIQUE(license_key_id)
      )
    `);

    // Queue batches table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS queue_batches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        batch_name TEXT,
        login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft')),
        total_accounts INTEGER NOT NULL,
        processed_accounts INTEGER DEFAULT 0,
        failed_accounts INTEGER DEFAULT 0,
        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
        priority_level INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        started_at DATETIME,
        completed_at DATETIME,
        scheduled_time DATETIME,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Queue jobs table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS queue_jobs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        batch_id INTEGER,
        user_id INTEGER NOT NULL,
        job_type TEXT NOT NULL DEFAULT 'sparx_reader',
        job_data TEXT NOT NULL,
        status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),
        priority_level INTEGER DEFAULT 0,
        effective_priority INTEGER DEFAULT 0,
        scheduled_time DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        started_at DATETIME,
        completed_at DATETIME,
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3,
        FOREIGN KEY (batch_id) REFERENCES queue_batches (id),
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);

    // Queue schedules table for conflict detection
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS queue_schedules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        scheduled_time DATETIME NOT NULL,
        duration_minutes INTEGER DEFAULT 30,
        job_id INTEGER,
        batch_id INTEGER,
        status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (job_id) REFERENCES queue_jobs (id),
        FOREIGN KEY (batch_id) REFERENCES queue_batches (id)
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
      CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key_id);
      CREATE INDEX IF NOT EXISTS idx_license_keys_code ON license_keys(key_code);
      CREATE INDEX IF NOT EXISTS idx_license_keys_expires ON license_keys(expires_at);
      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);
      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);
      CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_logs(user_id);
      CREATE INDEX IF NOT EXISTS idx_activity_created ON activity_logs(created_at);
      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_user ON encrypted_credentials(user_id);
      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_key ON encrypted_credentials(login_key);
      CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id);
      CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id);
      CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status);
      CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time);
      CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id);
      CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id);
      CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status);
      CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority);
      CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time);
      CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id);
      CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time);
    `);

    // Migration: Add max_batches_per_day column if it doesn't exist
    try {
      const columns = this.db.prepare("PRAGMA table_info(license_feature_settings)").all();
      const hasMaxBatchesPerDay = columns.some(col => col.name === 'max_batches_per_day');

      if (!hasMaxBatchesPerDay) {
        console.log('Adding max_batches_per_day column to license_feature_settings...');
        this.db.exec(`ALTER TABLE license_feature_settings ADD COLUMN max_batches_per_day INTEGER DEFAULT 1`);
      }
    } catch (error) {
      console.error('Migration error for max_batches_per_day:', error);
    }

    // Migration: Add login_type column to queue_batches if it doesn't exist
    try {
      const batchColumns = this.db.prepare("PRAGMA table_info(queue_batches)").all();
      const hasLoginType = batchColumns.some(col => col.name === 'login_type');

      if (!hasLoginType) {
        console.log('Adding login_type column to queue_batches...');
        this.db.exec(`ALTER TABLE queue_batches ADD COLUMN login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft'))`);
      }
    } catch (error) {
      console.error('Migration error for login_type:', error);
    }
  }

  createDefaultAdmin() {
    const adminExists = this.db.prepare('SELECT id FROM users WHERE role = ? LIMIT 1').get('admin');
    
    if (!adminExists) {
      const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);
      const stmt = this.db.prepare(`
        INSERT INTO users (username, password_hash, role, is_active)
        VALUES (?, ?, ?, ?)
      `);
      
      stmt.run(process.env.DEFAULT_ADMIN_USERNAME, hashedPassword, 'admin', 1);
      console.log(`Default admin user created: ${process.env.DEFAULT_ADMIN_USERNAME}/[hidden]`);
    }
  }

  // User management methods
  createUser(username, password, licenseKey) {
    const transaction = this.db.transaction(() => {
      // Validate license key
      const licenseStmt = this.db.prepare(`
        SELECT id, max_uses, current_uses, expires_at, is_active 
        FROM license_keys 
        WHERE key_code = ? AND is_active = 1
      `);
      const license = licenseStmt.get(licenseKey);

      if (!license) {
        throw new Error('Invalid license key');
      }

      if (new Date(license.expires_at) < new Date()) {
        throw new Error('License key has expired');
      }

      if (license.current_uses >= license.max_uses) {
        throw new Error('License key has reached maximum uses');
      }

      // Check if username already exists
      const userExists = this.db.prepare('SELECT id FROM users WHERE username = ?').get(username);
      if (userExists) {
        throw new Error('Username already exists');
      }

      // Create user
      const hashedPassword = bcrypt.hashSync(password, 12);
      const userStmt = this.db.prepare(`
        INSERT INTO users (username, password_hash, license_key_id, role)
        VALUES (?, ?, ?, ?)
      `);
      
      const result = userStmt.run(username, hashedPassword, license.id, 'user');

      // Update license key usage
      const updateLicenseStmt = this.db.prepare(`
        UPDATE license_keys 
        SET current_uses = current_uses + 1 
        WHERE id = ?
      `);
      updateLicenseStmt.run(license.id);

      return result.lastInsertRowid;
    });

    return transaction();
  }

  authenticateUser(username, password) {
    const stmt = this.db.prepare(`
      SELECT u.*, lk.expires_at as license_expires, lk.is_active as license_active
      FROM users u
      LEFT JOIN license_keys lk ON u.license_key_id = lk.id
      WHERE u.username = ? AND u.is_active = 1
    `);
    
    const user = stmt.get(username);
    
    if (!user) {
      throw new Error('Invalid credentials');
    }

    const isValidPassword = bcrypt.compareSync(password, user.password_hash);
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Check license validity for non-admin users
    if (user.role !== 'admin') {
      if (!user.license_active || new Date(user.license_expires) < new Date()) {
        throw new Error('License has expired');
      }
    }

    // Update last login
    const updateStmt = this.db.prepare('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?');
    updateStmt.run(user.id);

    // Remove sensitive data
    delete user.password_hash;
    return user;
  }

  // License key management methods
  createLicenseKey(durationDays, maxUses = 1, features = [], createdBy = null) {
    const keyCode = this.generateLicenseKey();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + durationDays);

    const stmt = this.db.prepare(`
      INSERT INTO license_keys (key_code, duration_days, max_uses, expires_at, created_by, features)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      keyCode,
      durationDays,
      maxUses,
      expiresAt.toISOString(),
      createdBy,
      JSON.stringify(features)
    );

    return {
      id: result.lastInsertRowid,
      keyCode,
      durationDays,
      maxUses,
      expiresAt: expiresAt.toISOString(),
      features
    };
  }

  generateLicenseKey() {
    const segments = [];
    for (let i = 0; i < 4; i++) {
      segments.push(uuidv4().replace(/-/g, '').substring(0, 8).toUpperCase());
    }
    return `SRX-${segments.join('-')}`;
  }

  getLicenseKeys(limit = 50, offset = 0) {
    const stmt = this.db.prepare(`
      SELECT 
        lk.*,
        u.username as created_by_username,
        COUNT(users.id) as users_count
      FROM license_keys lk
      LEFT JOIN users u ON lk.created_by = u.id
      LEFT JOIN users ON users.license_key_id = lk.id
      GROUP BY lk.id
      ORDER BY lk.created_at DESC
      LIMIT ? OFFSET ?
    `);

    return stmt.all(limit, offset);
  }

  deactivateLicenseKey(keyId) {
    const stmt = this.db.prepare('UPDATE license_keys SET is_active = 0 WHERE id = ?');
    return stmt.run(keyId);
  }

  // Get detailed license information for a user
  getUserLicenseStatus(userId) {
    const stmt = this.db.prepare(`
      SELECT 
        u.id as user_id,
        u.username,
        lk.id as license_id,
        lk.key_code,
        lk.max_uses,
        lk.current_uses,
        lk.expires_at,
        lk.is_active,
        CASE 
          WHEN lk.expires_at <= datetime('now') THEN 'expired'
          WHEN lk.current_uses >= lk.max_uses THEN 'maxed_out'
          WHEN lk.is_active = 0 THEN 'inactive'
          ELSE 'valid'
        END as license_status
      FROM users u
      LEFT JOIN license_keys lk ON u.license_key_id = lk.id
      WHERE u.id = ?
    `);
    
    return stmt.get(userId);
  }

  // Validate a license key for renewal (check if it's valid and has available uses)
  validateLicenseForRenewal(licenseKey) {
    const stmt = this.db.prepare(`
      SELECT 
        id,
        key_code,
        max_uses,
        current_uses,
        expires_at,
        is_active,
        CASE 
          WHEN expires_at <= datetime('now') THEN 'expired'
          WHEN current_uses >= max_uses THEN 'maxed_out'
          WHEN is_active = 0 THEN 'inactive'
          ELSE 'valid'
        END as status
      FROM license_keys 
      WHERE key_code = ?
    `);
    
    const license = stmt.get(licenseKey);
    
    if (!license) {
      return { valid: false, error: 'License key not found' };
    }
    
    if (license.status !== 'valid') {
      let errorMessage = 'License key is not valid';
      switch (license.status) {
        case 'expired':
          errorMessage = 'License key has expired';
          break;
        case 'maxed_out':
          errorMessage = 'License key has reached maximum uses';
          break;
        case 'inactive':
          errorMessage = 'License key is inactive';
          break;
      }
      return { valid: false, error: errorMessage };
    }
    
    return { valid: true, license };
  }

  // Renew user's license with a new license key
  renewUserLicense(userId, newLicenseKey) {
    const transaction = this.db.transaction(() => {
      // First validate the new license key
      const validation = this.validateLicenseForRenewal(newLicenseKey);
      if (!validation.valid) {
        throw new Error(validation.error);
      }
      
      const newLicense = validation.license;
      
      // Update user's license_key_id to the new license
      const updateUserStmt = this.db.prepare(`
        UPDATE users 
        SET license_key_id = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `);
      updateUserStmt.run(newLicense.id, userId);
      
      // Increment the new license's current_uses
      const updateLicenseStmt = this.db.prepare(`
        UPDATE license_keys 
        SET current_uses = current_uses + 1 
        WHERE id = ?
      `);
      updateLicenseStmt.run(newLicense.id);
      
      // Log the renewal activity
      this.logActivity(userId, 'LICENSE_RENEWED', `License renewed with key: ${newLicenseKey}`);
      
      return {
        success: true,
        newLicenseId: newLicense.id,
        newLicenseKey: newLicenseKey,
        expiresAt: newLicense.expires_at,
        maxUses: newLicense.max_uses,
        currentUses: newLicense.current_uses + 1
      };
    });
    
    return transaction();
  }

  // Session management
  createSession(userId, tokenHash, expiresAt, ipAddress = null, userAgent = null) {
    const stmt = this.db.prepare(`
      INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)
      VALUES (?, ?, ?, ?, ?)
    `);

    return stmt.run(userId, tokenHash, expiresAt, ipAddress, userAgent);
  }

  validateSession(tokenHash) {
    const stmt = this.db.prepare(`
      SELECT s.*, u.username, u.role, u.is_active as user_active
      FROM user_sessions s
      JOIN users u ON s.user_id = u.id
      WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')
    `);

    return stmt.get(tokenHash);
  }

  invalidateSession(tokenHash) {
    const stmt = this.db.prepare('UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?');
    return stmt.run(tokenHash);
  }

  invalidateAllUserSessions(userId) {
    const stmt = this.db.prepare('UPDATE user_sessions SET is_active = 0 WHERE user_id = ?');
    return stmt.run(userId);
  }

  // Activity logging
  logActivity(userId, action, details = null, ipAddress = null) {
    const stmt = this.db.prepare(`
      INSERT INTO activity_logs (user_id, action, details, ip_address)
      VALUES (?, ?, ?, ?)
    `);

    return stmt.run(userId, action, details, ipAddress);
  }

  getActivityLogs(userId = null, limit = 100, offset = 0) {
    let query = `
      SELECT 
        al.*,
        u.username
      FROM activity_logs al
      LEFT JOIN users u ON al.user_id = u.id
    `;
    
    const params = [];
    
    if (userId) {
      query += ' WHERE al.user_id = ?';
      params.push(userId);
    }
    
    query += ' ORDER BY al.created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    const stmt = this.db.prepare(query);
    return stmt.all(...params);
  }

  // Analytics methods
  getSystemStats() {
    const stats = {};

    // Total users
    stats.totalUsers = this.db.prepare('SELECT COUNT(*) as count FROM users WHERE role = \'user\'').get().count;
    
    // Active users (logged in within last 30 days)
    stats.activeUsers = this.db.prepare(`
      SELECT COUNT(*) as count FROM users 
      WHERE role = 'user' AND last_login > datetime('now', '-30 days')
    `).get().count;

    // Total license keys
    stats.totalLicenseKeys = this.db.prepare('SELECT COUNT(*) as count FROM license_keys').get().count;
    
    // Active license keys
    stats.activeLicenseKeys = this.db.prepare(`
      SELECT COUNT(*) as count FROM license_keys 
      WHERE is_active = 1 AND expires_at > datetime('now')
    `).get().count;

    // Expired license keys
    stats.expiredLicenseKeys = this.db.prepare(`
      SELECT COUNT(*) as count FROM license_keys 
      WHERE expires_at <= datetime('now')
    `).get().count;

    // Recent activity (last 24 hours)
    stats.recentActivity = this.db.prepare(`
      SELECT COUNT(*) as count FROM activity_logs 
      WHERE created_at > datetime('now', '-1 day')
    `).get().count;

    return stats;
  }

  // User management for admin
  getUsers(limit = 50, offset = 0) {
    const stmt = this.db.prepare(`
      SELECT 
        u.id,
        u.username,
        u.role,
        u.created_at,
        u.last_login,
        u.is_active,
        lk.key_code,
        lk.expires_at as license_expires
      FROM users u
      LEFT JOIN license_keys lk ON u.license_key_id = lk.id
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `);

    return stmt.all(limit, offset);
  }

  toggleUserStatus(userId) {
    const stmt = this.db.prepare('UPDATE users SET is_active = NOT is_active WHERE id = ?');
    return stmt.run(userId);
  }

  // Cleanup methods
  cleanupExpiredSessions() {
    const stmt = this.db.prepare('DELETE FROM user_sessions WHERE expires_at <= datetime("now")');
    return stmt.run();
  }

  cleanupOldLogs(daysToKeep = 90) {
    const stmt = this.db.prepare(`
      DELETE FROM activity_logs 
      WHERE created_at <= datetime('now', '-${daysToKeep} days')
    `);
    return stmt.run();
  }

  // Encrypted credentials methods
  saveEncryptedCredentials(userId, loginMethod, school, email, password, encryptionKey) {
    const crypto = require('crypto');
    
    // Generate a unique login key
    const loginKey = 'SLK-' + crypto.randomBytes(8).toString('hex').toUpperCase();
    
    // Create encryption IV
    const iv = crypto.randomBytes(16);
    const key = crypto.scryptSync(encryptionKey, 'salt', 32);
    
    // Encrypt school, email and password
    const cipher1 = crypto.createCipheriv('aes-256-cbc', key, iv);
    const encryptedSchool = cipher1.update(school, 'utf8', 'hex') + cipher1.final('hex');
    
    const cipher2 = crypto.createCipheriv('aes-256-cbc', key, iv);
    const encryptedEmail = cipher2.update(email, 'utf8', 'hex') + cipher2.final('hex');
    
    const cipher3 = crypto.createCipheriv('aes-256-cbc', key, iv);
    const encryptedPassword = cipher3.update(password, 'utf8', 'hex') + cipher3.final('hex');
    
    const stmt = this.db.prepare(`
      INSERT INTO encrypted_credentials (user_id, login_key, login_method, encrypted_school, encrypted_email, encrypted_password, encryption_iv)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(userId, loginKey, loginMethod, encryptedSchool, encryptedEmail, encryptedPassword, iv.toString('hex'));
    
    return loginKey;
  }

  getEncryptedCredentials(loginKey, encryptionKey) {
    const stmt = this.db.prepare(`
      SELECT * FROM encrypted_credentials 
      WHERE login_key = ? AND is_active = 1
    `);
    
    const result = stmt.get(loginKey);
    if (!result) return null;
    
    const crypto = require('crypto');
    
    try {
      const key = crypto.scryptSync(encryptionKey, 'salt', 32);
      const iv = Buffer.from(result.encryption_iv, 'hex');

      // Decrypt school, email and password
      const decipher1 = crypto.createDecipheriv('aes-256-cbc', key, iv);
      const school = result.encrypted_school ? decipher1.update(result.encrypted_school, 'hex', 'utf8') + decipher1.final('utf8') : null;
      
      const decipher2 = crypto.createDecipheriv('aes-256-cbc', key, iv);
      const email = result.encrypted_email ? decipher2.update(result.encrypted_email, 'hex', 'utf8') + decipher2.final('utf8') : null;
      
      const decipher3 = crypto.createDecipheriv('aes-256-cbc', key, iv);
      const password = result.encrypted_password ? decipher3.update(result.encrypted_password, 'hex', 'utf8') + decipher3.final('utf8') : null;
      
      return {
        loginMethod: result.login_method,
        school,
        email,
        password,
        userId: result.user_id
      };
    } catch (error) {
      console.error('Failed to decrypt credentials:', error);
      return null;
    }
  }

  getUserCredentials(userId) {
    const stmt = this.db.prepare(`
      SELECT login_key, login_method, created_at FROM encrypted_credentials 
      WHERE user_id = ? AND is_active = 1
      ORDER BY created_at DESC
    `);
    
    return stmt.all(userId);
  }

  deactivateCredentials(loginKey) {
    const stmt = this.db.prepare('UPDATE encrypted_credentials SET is_active = 0 WHERE login_key = ?');
    return stmt.run(loginKey);
  }

  // License Feature Settings Methods
  setLicenseFeatures(licenseKeyId, features) {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO license_feature_settings
      (license_key_id, max_accounts_per_batch, priority_level, scheduling_access, max_batches_per_day, updated_at)
      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `);

    return stmt.run(
      licenseKeyId,
      features.max_accounts_per_batch || 0,
      features.priority_level || 0,
      features.scheduling_access ? 1 : 0,
      features.max_batches_per_day || 1
    );
  }

  getLicenseFeatures(licenseKeyId) {
    const stmt = this.db.prepare(`
      SELECT * FROM license_feature_settings WHERE license_key_id = ?
    `);

    const result = stmt.get(licenseKeyId);
    if (!result) {
      // Return default features if none set
      return {
        max_accounts_per_batch: 0,
        priority_level: 0,
        scheduling_access: false,
        max_batches_per_day: 1
      };
    }

    return {
      max_accounts_per_batch: result.max_accounts_per_batch,
      priority_level: result.priority_level,
      scheduling_access: Boolean(result.scheduling_access),
      max_batches_per_day: result.max_batches_per_day || 1
    };
  }

  getUserLicenseFeatures(userId) {
    const stmt = this.db.prepare(`
      SELECT lfs.* FROM license_feature_settings lfs
      JOIN users u ON u.license_key_id = lfs.license_key_id
      WHERE u.id = ?
    `);

    const result = stmt.get(userId);
    if (!result) {
      return {
        max_accounts_per_batch: 0,
        priority_level: 0,
        scheduling_access: false,
        max_batches_per_day: 1
      };
    }

    return {
      max_accounts_per_batch: result.max_accounts_per_batch,
      priority_level: result.priority_level,
      scheduling_access: Boolean(result.scheduling_access),
      max_batches_per_day: result.max_batches_per_day || 1
    };
  }

  // Daily batch count check
  getUserDailyBatchCount(userId, date = null) {
    const targetDate = date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const stmt = this.db.prepare(`
      SELECT COUNT(*) as count
      FROM queue_batches
      WHERE user_id = ?
      AND DATE(created_at) = ?
    `);

    const result = stmt.get(userId, targetDate);
    return result.count;
  }

  // Queue Batch Methods
  createQueueBatch(userId, batchName, accounts, scheduledTime = null, loginType = 'normal') {
    const transaction = this.db.transaction(() => {
      // Get user's license features
      const features = this.getUserLicenseFeatures(userId);

      // Check daily batch limit
      const dailyBatchCount = this.getUserDailyBatchCount(userId);
      if (dailyBatchCount >= features.max_batches_per_day) {
        throw new Error(`Daily batch limit reached (${features.max_batches_per_day} batches per day). Please try again tomorrow.`);
      }

      // Validate batch size against license limits
      if (features.max_accounts_per_batch > 0 && accounts.length > features.max_accounts_per_batch) {
        throw new Error(`Batch size (${accounts.length}) exceeds license limit (${features.max_accounts_per_batch})`);
      }

      // Validate scheduling access
      if (scheduledTime && !features.scheduling_access) {
        throw new Error('Scheduling access not available for this license');
      }

      // Validate login type
      if (!['normal', 'google', 'microsoft'].includes(loginType)) {
        throw new Error('Invalid login type specified');
      }

      // Create batch
      const batchStmt = this.db.prepare(`
        INSERT INTO queue_batches (user_id, batch_name, login_type, total_accounts, priority_level, scheduled_time)
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      const batchResult = batchStmt.run(
        userId,
        batchName,
        loginType,
        accounts.length,
        features.priority_level,
        scheduledTime
      );
      
      const batchId = batchResult.lastInsertRowid;
      
      // Create individual jobs for each account
      const jobStmt = this.db.prepare(`
        INSERT INTO queue_jobs (batch_id, user_id, job_data, priority_level, effective_priority, scheduled_time)
        VALUES (?, ?, ?, ?, ?, ?)
      `);
      
      accounts.forEach(account => {
        const effectivePriority = this.calculateEffectivePriority(features.priority_level, scheduledTime);
        jobStmt.run(
          batchId,
          userId,
          JSON.stringify(account),
          features.priority_level,
          effectivePriority,
          scheduledTime
        );
      });
      
      // Create schedule entry if scheduled
      if (scheduledTime) {
        this.createScheduleEntry(userId, scheduledTime, null, batchId);
      }
      
      // Log activity
      this.logActivity(userId, 'BATCH_CREATED', `Created batch: ${batchName} with ${accounts.length} accounts`);
      
      return batchId;
    });
    
    return transaction();
  }

  calculateEffectivePriority(basePriority, scheduledTime) {
    let effectivePriority = basePriority;
    
    // Boost priority for scheduled jobs approaching their time
    if (scheduledTime) {
      const now = new Date();
      const scheduled = new Date(scheduledTime);
      const timeDiff = scheduled.getTime() - now.getTime();
      const hoursUntil = timeDiff / (1000 * 60 * 60);
      
      if (hoursUntil <= 1) {
        effectivePriority += 5; // High boost for jobs due within an hour
      } else if (hoursUntil <= 6) {
        effectivePriority += 2; // Medium boost for jobs due within 6 hours
      }
    }
    
    // Apply starvation prevention (boost priority for old jobs)
    // This would be implemented in a background process
    
    return Math.min(effectivePriority, 10); // Cap at maximum priority
  }

  getQueueBatches(userId = null, status = null, limit = 50, offset = 0) {
    let query = `
      SELECT qb.*, u.username
      FROM queue_batches qb
      JOIN users u ON qb.user_id = u.id
      WHERE 1=1
    `;
    const params = [];
    
    if (userId) {
      query += ' AND qb.user_id = ?';
      params.push(userId);
    }
    
    if (status) {
      query += ' AND qb.status = ?';
      params.push(status);
    }
    
    query += ' ORDER BY qb.created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);
    
    const stmt = this.db.prepare(query);
    return stmt.all(...params);
  }

  getBatchJobs(batchId) {
    const stmt = this.db.prepare(`
      SELECT * FROM queue_jobs 
      WHERE batch_id = ? 
      ORDER BY effective_priority DESC, created_at ASC
    `);
    
    return stmt.all(batchId);
  }

  updateBatchStatus(batchId, status, completedAt = null) {
    const stmt = this.db.prepare(`
      UPDATE queue_batches 
      SET status = ?, completed_at = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    
    return stmt.run(status, completedAt, batchId);
  }

  // Queue Job Methods
  getNextQueueJob() {
    const stmt = this.db.prepare(`
      SELECT qj.*, qb.batch_name, u.username
      FROM queue_jobs qj
      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id
      JOIN users u ON qj.user_id = u.id
      WHERE qj.status = 'queued' 
      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))
      ORDER BY qj.effective_priority DESC, qj.created_at ASC
      LIMIT 1
    `);
    
    return stmt.get();
  }

  updateJobStatus(jobId, status, errorMessage = null, startedAt = null, completedAt = null) {
    const stmt = this.db.prepare(`
      UPDATE queue_jobs 
      SET status = ?, error_message = ?, started_at = ?, completed_at = ?
      WHERE id = ?
    `);
    
    return stmt.run(status, errorMessage, startedAt, completedAt, jobId);
  }

  incrementJobRetry(jobId) {
    const stmt = this.db.prepare(`
      UPDATE queue_jobs 
      SET retry_count = retry_count + 1, status = 'queued'
      WHERE id = ? AND retry_count < max_retries
    `);
    
    return stmt.run(jobId);
  }

  // Scheduling Methods
  createScheduleEntry(userId, scheduledTime, jobId = null, batchId = null, durationMinutes = 30) {
    // Check for conflicts
    const conflicts = this.checkScheduleConflicts(userId, scheduledTime, durationMinutes);
    if (conflicts.length > 0) {
      throw new Error(`Schedule conflict detected at ${scheduledTime}`);
    }
    
    const stmt = this.db.prepare(`
      INSERT INTO queue_schedules (user_id, scheduled_time, duration_minutes, job_id, batch_id)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    return stmt.run(userId, scheduledTime, durationMinutes, jobId, batchId);
  }

  checkScheduleConflicts(userId, scheduledTime, durationMinutes) {
    const startTime = new Date(scheduledTime);
    const endTime = new Date(startTime.getTime() + (durationMinutes * 60 * 1000));
    
    const stmt = this.db.prepare(`
      SELECT * FROM queue_schedules
      WHERE user_id = ? 
      AND status IN ('scheduled', 'active')
      AND (
        (scheduled_time <= ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') > ?) OR
        (scheduled_time < ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') >= ?)
      )
    `);
    
    return stmt.all(userId, startTime.toISOString(), startTime.toISOString(), 
                   endTime.toISOString(), endTime.toISOString());
  }

  getUserSchedules(userId, startDate = null, endDate = null) {
    let query = `
      SELECT qs.*, qj.job_type, qb.batch_name
      FROM queue_schedules qs
      LEFT JOIN queue_jobs qj ON qs.job_id = qj.id
      LEFT JOIN queue_batches qb ON qs.batch_id = qb.id
      WHERE qs.user_id = ?
    `;
    const params = [userId];
    
    if (startDate) {
      query += ' AND qs.scheduled_time >= ?';
      params.push(startDate);
    }
    
    if (endDate) {
      query += ' AND qs.scheduled_time <= ?';
      params.push(endDate);
    }
    
    query += ' ORDER BY qs.scheduled_time ASC';
    
    const stmt = this.db.prepare(query);
    return stmt.all(...params);
  }

  // Priority Management Methods
  updateJobPriority(jobId, newPriority, adminOverride = false) {
    const stmt = this.db.prepare(`
      UPDATE queue_jobs 
      SET effective_priority = ?, priority_level = ?
      WHERE id = ?
    `);
    
    const result = stmt.run(newPriority, adminOverride ? newPriority : null, jobId);
    
    if (adminOverride) {
      this.logActivity(null, 'ADMIN_PRIORITY_OVERRIDE', `Job ${jobId} priority set to ${newPriority}`);
    }
    
    return result;
  }

  applyStarvationPrevention() {
    // Boost priority for jobs that have been waiting too long
    const stmt = this.db.prepare(`
      UPDATE queue_jobs 
      SET effective_priority = CASE 
        WHEN datetime('now') > datetime(created_at, '+2 hours') THEN MIN(effective_priority + 2, 10)
        WHEN datetime('now') > datetime(created_at, '+1 hour') THEN MIN(effective_priority + 1, 10)
        ELSE effective_priority
      END
      WHERE status = 'queued'
    `);
    
    return stmt.run();
  }

  getQueueStats() {
    const stats = {};
    
    // Total jobs by status
    const statusStmt = this.db.prepare(`
      SELECT status, COUNT(*) as count 
      FROM queue_jobs 
      GROUP BY status
    `);
    stats.jobsByStatus = statusStmt.all();
    
    // Jobs by priority level
    const priorityStmt = this.db.prepare(`
      SELECT effective_priority, COUNT(*) as count 
      FROM queue_jobs 
      WHERE status = 'queued'
      GROUP BY effective_priority
      ORDER BY effective_priority DESC
    `);
    stats.jobsByPriority = priorityStmt.all();
    
    // Average wait time
    const waitTimeStmt = this.db.prepare(`
      SELECT AVG(julianday(started_at) - julianday(created_at)) * 24 * 60 as avg_wait_minutes
      FROM queue_jobs 
      WHERE started_at IS NOT NULL
    `);
    stats.averageWaitTime = waitTimeStmt.get()?.avg_wait_minutes || 0;
    
    return stats;
  }

  close() {
    this.db.close();
  }
}

// Create data directory if it doesn't exist
const fs = require('fs');
const dataDir = path.join(process.cwd(), 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Export singleton instance
let dbInstance = null;

function getDatabase() {
  if (!dbInstance) {
    dbInstance = new DatabaseManager();
  }
  return dbInstance;
}

module.exports = { getDatabase, DatabaseManager };
