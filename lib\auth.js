const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { getDatabase } = require('./database');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const TOKEN_EXPIRY = '7d'; // 7 days

class AuthManager {
  constructor() {
    this.db = getDatabase();
  }

  // Generate JWT token
  generateToken(user) {
    const payload = {
      userId: user.id,
      username: user.username,
      role: user.role,
      iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(payload, JWT_SECRET, { expiresIn: TOKEN_EXPIRY });
  }

  // Verify JWT token
  verifyToken(token) {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  // Hash token for database storage
  hashToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  // Create session with token
  createSession(user, ipAddress = null, userAgent = null) {
    const token = this.generateToken(user);
    const tokenHash = this.hashToken(token);
    
    // Calculate expiry date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    // Store session in database
    this.db.createSession(user.id, tokenHash, expiresAt.toISOString(), ipAddress, userAgent);

    // Log activity
    this.db.logActivity(user.id, 'LOGIN', `User logged in from ${ipAddress}`, ipAddress);

    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        lastLogin: user.last_login
      },
      expiresAt: expiresAt.toISOString()
    };
  }

  // Validate session
  validateSession(token) {
    try {
      // First verify JWT
      const decoded = this.verifyToken(token);
      
      // Then check database session
      const tokenHash = this.hashToken(token);
      const session = this.db.validateSession(tokenHash);

      if (!session || !session.user_active) {
        throw new Error('Session invalid or user inactive');
      }

      return {
        userId: session.user_id,
        username: session.username,
        role: session.role,
        sessionId: session.id
      };
    } catch (error) {
      throw new Error('Invalid session');
    }
  }

  // Logout user
  logout(token, userId = null) {
    const tokenHash = this.hashToken(token);
    this.db.invalidateSession(tokenHash);

    if (userId) {
      this.db.logActivity(userId, 'LOGOUT', 'User logged out');
    }
  }

  // Logout all sessions for user
  logoutAllSessions(userId) {
    this.db.invalidateAllUserSessions(userId);
    this.db.logActivity(userId, 'LOGOUT_ALL', 'All sessions invalidated');
  }

  // Middleware for protecting routes
  requireAuth(requiredRole = null) {
    return async (req, res, next) => {
      try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return res.status(401).json({ error: 'No token provided' });
        }

        const token = authHeader.substring(7);
        const session = await this.validateSession(token);

        // Check role if required
        if (requiredRole && session.role !== requiredRole) {
          return res.status(403).json({ error: 'Insufficient permissions' });
        }

        // Add user info to request
        req.user = session;
        req.token = token;
        
        next();
      } catch (error) {
        return res.status(401).json({ error: error.message });
      }
    };
  }

  // Admin only middleware
  requireAdmin() {
    return this.requireAuth('admin');
  }

  // Extract IP address from request (Next.js compatible)
  getClientIP(req) {
    // For Next.js App Router requests
    if (req.headers && typeof req.headers.get === 'function') {
      return req.headers.get('x-forwarded-for') || 
             req.headers.get('x-real-ip') ||
             req.ip ||
             '127.0.0.1';
    }
    
    // For traditional Node.js requests
    return req.headers['x-forwarded-for'] || 
           req.connection?.remoteAddress || 
           req.socket?.remoteAddress ||
           (req.connection?.socket ? req.connection.socket.remoteAddress : null) ||
           '127.0.0.1';
  }

  // Extract user agent (Next.js compatible)
  getUserAgent(req) {
    // For Next.js App Router requests
    if (req.headers && typeof req.headers.get === 'function') {
      return req.headers.get('user-agent') || 'Unknown';
    }
    
    // For traditional Node.js requests
    return req.headers['user-agent'] || 'Unknown';
  }

  // Rate limiting helper
  checkRateLimit(identifier, maxAttempts = 5, windowMinutes = 15) {
    // This is a simple in-memory rate limiter
    // In production, you might want to use Redis or database
    if (!this.rateLimitStore) {
      this.rateLimitStore = new Map();
    }

    const now = Date.now();
    const windowMs = windowMinutes * 60 * 1000;
    const key = `rate_limit_${identifier}`;

    if (!this.rateLimitStore.has(key)) {
      this.rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return { allowed: true, remaining: maxAttempts - 1 };
    }

    const record = this.rateLimitStore.get(key);

    if (now > record.resetTime) {
      // Reset the window
      this.rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return { allowed: true, remaining: maxAttempts - 1 };
    }

    if (record.count >= maxAttempts) {
      return { 
        allowed: false, 
        remaining: 0, 
        resetTime: record.resetTime 
      };
    }

    record.count++;
    return { 
      allowed: true, 
      remaining: maxAttempts - record.count 
    };
  }

  // Clean up expired rate limit entries
  cleanupRateLimit() {
    if (!this.rateLimitStore) return;

    const now = Date.now();
    for (const [key, record] of this.rateLimitStore.entries()) {
      if (now > record.resetTime) {
        this.rateLimitStore.delete(key);
      }
    }
  }

  // Password strength validation
  validatePasswordStrength(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const errors = [];

    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }
    if (!hasUpperCase) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!hasLowerCase) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!hasNumbers) {
      errors.push('Password must contain at least one number');
    }
    if (!hasSpecialChar) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength: this.calculatePasswordStrength(password)
    };
  }

  calculatePasswordStrength(password) {
    let score = 0;
    
    // Length bonus
    score += Math.min(password.length * 2, 20);
    
    // Character variety bonus
    if (/[a-z]/.test(password)) score += 5;
    if (/[A-Z]/.test(password)) score += 5;
    if (/[0-9]/.test(password)) score += 5;
    if (/[^A-Za-z0-9]/.test(password)) score += 10;
    
    // Penalty for common patterns
    if (/(.)\1{2,}/.test(password)) score -= 10; // Repeated characters
    if (/123|abc|qwe/i.test(password)) score -= 10; // Sequential patterns

    if (score < 30) return 'weak';
    if (score < 60) return 'medium';
    return 'strong';
  }
}

// Export singleton instance
let authInstance = null;

function getAuthManager() {
  if (!authInstance) {
    authInstance = new AuthManager();
  }
  return authInstance;
}

module.exports = { getAuthManager, AuthManager };