/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/queue/page";
exports.ids = ["app/queue/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fqueue%2Fpage&page=%2Fqueue%2Fpage&appPaths=%2Fqueue%2Fpage&pagePath=private-next-app-dir%2Fqueue%2Fpage.jsx&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fqueue%2Fpage&page=%2Fqueue%2Fpage&appPaths=%2Fqueue%2Fpage&pagePath=private-next-app-dir%2Fqueue%2Fpage.jsx&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'queue',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/queue/page.jsx */ \"(rsc)/./app/queue/page.jsx\")), \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.jsx */ \"(rsc)/./app/layout.jsx\")), \"D:\\\\reader-auto-main\\\\app\\\\layout.jsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/queue/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/queue/page\",\n        pathname: \"/queue\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fqueue%2Fpage&page=%2Fqueue%2Fpage&appPaths=%2Fqueue%2Fpage&pagePath=private-next-app-dir%2Fqueue%2Fpage.jsx&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cqueue%5Cpage.jsx&server=true!":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cqueue%5Cpage.jsx&server=true! ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/queue/page.jsx */ \"(ssr)/./app/queue/page.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q3JlYWRlci1hdXRvLW1haW4lNUNhcHAlNUNxdWV1ZSU1Q3BhZ2UuanN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLz82NjVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccmVhZGVyLWF1dG8tbWFpblxcXFxhcHBcXFxccXVldWVcXFxccGFnZS5qc3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cqueue%5Cpage.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/queue/components/ScheduleCalendar.jsx":
/*!***************************************************!*\
  !*** ./app/queue/components/ScheduleCalendar.jsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScheduleCalendar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ScheduleCalendar({ schedules, onScheduleSelect, onCreateSchedule }) {\n    const [currentDate, setCurrentDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [view, setView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"month\"); // month, week, day\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get calendar data based on current view\n    const getCalendarData = ()=>{\n        const year = currentDate.getFullYear();\n        const month = currentDate.getMonth();\n        if (view === \"month\") {\n            return getMonthData(year, month);\n        } else if (view === \"week\") {\n            return getWeekData(currentDate);\n        } else {\n            return getDayData(currentDate);\n        }\n    };\n    const getMonthData = (year, month)=>{\n        const firstDay = new Date(year, month, 1);\n        const lastDay = new Date(year, month + 1, 0);\n        const startDate = new Date(firstDay);\n        startDate.setDate(startDate.getDate() - firstDay.getDay());\n        const days = [];\n        const current = new Date(startDate);\n        for(let i = 0; i < 42; i++){\n            days.push(new Date(current));\n            current.setDate(current.getDate() + 1);\n        }\n        return days;\n    };\n    const getWeekData = (date)=>{\n        const startOfWeek = new Date(date);\n        startOfWeek.setDate(date.getDate() - date.getDay());\n        const days = [];\n        for(let i = 0; i < 7; i++){\n            const day = new Date(startOfWeek);\n            day.setDate(startOfWeek.getDate() + i);\n            days.push(day);\n        }\n        return days;\n    };\n    const getDayData = (date)=>{\n        return [\n            new Date(date)\n        ];\n    };\n    const getSchedulesForDate = (date)=>{\n        const dateStr = date.toDateString();\n        return schedules.filter((schedule)=>{\n            const scheduleDate = new Date(schedule.start).toDateString();\n            return scheduleDate === dateStr;\n        });\n    };\n    const navigateCalendar = (direction)=>{\n        const newDate = new Date(currentDate);\n        if (view === \"month\") {\n            newDate.setMonth(currentDate.getMonth() + direction);\n        } else if (view === \"week\") {\n            newDate.setDate(currentDate.getDate() + direction * 7);\n        } else {\n            newDate.setDate(currentDate.getDate() + direction);\n        }\n        setCurrentDate(newDate);\n    };\n    const formatDateHeader = ()=>{\n        if (view === \"month\") {\n            return currentDate.toLocaleDateString(\"en-US\", {\n                month: \"long\",\n                year: \"numeric\"\n            });\n        } else if (view === \"week\") {\n            const weekData = getWeekData(currentDate);\n            const start = weekData[0].toLocaleDateString(\"en-US\", {\n                month: \"short\",\n                day: \"numeric\"\n            });\n            const end = weekData[6].toLocaleDateString(\"en-US\", {\n                month: \"short\",\n                day: \"numeric\"\n            });\n            return `${start} - ${end}, ${currentDate.getFullYear()}`;\n        } else {\n            return currentDate.toLocaleDateString(\"en-US\", {\n                weekday: \"long\",\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\"\n            });\n        }\n    };\n    const handleDateClick = (date)=>{\n        setSelectedDate(date);\n        if (onScheduleSelect) {\n            onScheduleSelect(date);\n        }\n    };\n    const handleCreateSchedule = (date)=>{\n        setSelectedDate(date);\n        setShowCreateModal(true);\n    };\n    const calendarData = getCalendarData();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: \"Schedule Calendar\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        \"month\",\n                                        \"week\",\n                                        \"day\"\n                                    ].map((viewType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setView(viewType),\n                                            className: `px-3 py-1 text-sm rounded ${view === viewType ? \"bg-blue-100 text-blue-700\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                            children: viewType.charAt(0).toUpperCase() + viewType.slice(1)\n                                        }, viewType, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateCalendar(-1),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                            children: \"←\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-medium text-gray-900 min-w-[200px] text-center\",\n                                            children: formatDateHeader()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateCalendar(1),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentDate(new Date()),\n                                    className: \"px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded hover:bg-blue-50\",\n                                    children: \"Today\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    view === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 gap-1\",\n                        children: [\n                            [\n                                \"Sun\",\n                                \"Mon\",\n                                \"Tue\",\n                                \"Wed\",\n                                \"Thu\",\n                                \"Fri\",\n                                \"Sat\"\n                            ].map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 text-center text-sm font-medium text-gray-500\",\n                                    children: day\n                                }, day, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this)),\n                            calendarData.map((date, index)=>{\n                                const daySchedules = getSchedulesForDate(date);\n                                const isCurrentMonth = date.getMonth() === currentDate.getMonth();\n                                const isToday = date.toDateString() === new Date().toDateString();\n                                const isSelected = selectedDate && date.toDateString() === selectedDate.toDateString();\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>handleDateClick(date),\n                                    className: `min-h-[100px] p-2 border border-gray-200 cursor-pointer hover:bg-gray-50 ${!isCurrentMonth ? \"bg-gray-50 text-gray-400\" : \"\"} ${isToday ? \"bg-blue-50 border-blue-200\" : \"\"} ${isSelected ? \"ring-2 ring-blue-500\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm ${isToday ? \"font-bold text-blue-600\" : \"\"}`,\n                                                    children: date.getDate()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleCreateSchedule(date);\n                                                    },\n                                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 space-y-1\",\n                                            children: [\n                                                daySchedules.slice(0, 3).map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            onScheduleSelect && onScheduleSelect(schedule);\n                                                        },\n                                                        className: `text-xs p-1 rounded truncate ${schedule.status === \"completed\" ? \"bg-green-100 text-green-800\" : schedule.status === \"active\" ? \"bg-blue-100 text-blue-800\" : schedule.status === \"cancelled\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                                                        children: schedule.title\n                                                    }, schedule.id, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 23\n                                                    }, this)),\n                                                daySchedules.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        daySchedules.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    view === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 gap-1\",\n                        children: calendarData.map((date, index)=>{\n                            const daySchedules = getSchedulesForDate(date);\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isSelected = selectedDate && date.toDateString() === selectedDate.toDateString();\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `p-2 text-center border-b ${isToday ? \"bg-blue-50\" : \"bg-gray-50\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: date.toLocaleDateString(\"en-US\", {\n                                                    weekday: \"short\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `text-lg ${isToday ? \"font-bold text-blue-600\" : \"\"}`,\n                                                children: date.getDate()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 min-h-[300px] space-y-1\",\n                                        children: [\n                                            daySchedules.map((schedule)=>{\n                                                const startTime = new Date(schedule.start).toLocaleTimeString(\"en-US\", {\n                                                    hour: \"numeric\",\n                                                    minute: \"2-digit\"\n                                                });\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>onScheduleSelect && onScheduleSelect(schedule),\n                                                    className: `text-xs p-2 rounded cursor-pointer ${schedule.status === \"completed\" ? \"bg-green-100 text-green-800\" : schedule.status === \"active\" ? \"bg-blue-100 text-blue-800\" : schedule.status === \"cancelled\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: startTime\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"truncate\",\n                                                            children: schedule.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, schedule.id, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 25\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleCreateSchedule(date),\n                                                className: \"w-full text-xs text-blue-600 border border-dashed border-blue-300 rounded p-2 hover:bg-blue-50\",\n                                                children: \"+ Add Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                lineNumber: 250,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    view === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: currentDate.toLocaleDateString(\"en-US\", {\n                                        weekday: \"long\",\n                                        month: \"long\",\n                                        day: \"numeric\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-200 rounded\",\n                                children: Array.from({\n                                    length: 24\n                                }, (_, hour)=>{\n                                    const timeSlot = new Date(currentDate);\n                                    timeSlot.setHours(hour, 0, 0, 0);\n                                    const hourSchedules = schedules.filter((schedule)=>{\n                                        const scheduleHour = new Date(schedule.start).getHours();\n                                        const scheduleDate = new Date(schedule.start).toDateString();\n                                        return scheduleHour === hour && scheduleDate === currentDate.toDateString();\n                                    });\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 p-2 text-sm text-gray-500 border-r\",\n                                                children: hour === 0 ? \"12 AM\" : hour < 12 ? `${hour} AM` : hour === 12 ? \"12 PM\" : `${hour - 12} PM`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-2 min-h-[60px]\",\n                                                children: [\n                                                    hourSchedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>onScheduleSelect && onScheduleSelect(schedule),\n                                                            className: `p-2 rounded mb-1 cursor-pointer ${schedule.status === \"completed\" ? \"bg-green-100 text-green-800\" : schedule.status === \"active\" ? \"bg-blue-100 text-blue-800\" : schedule.status === \"cancelled\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: schedule.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        new Date(schedule.start).toLocaleTimeString(\"en-US\", {\n                                                                            hour: \"numeric\",\n                                                                            minute: \"2-digit\"\n                                                                        }),\n                                                                        \" - \",\n                                                                        new Date(schedule.end).toLocaleTimeString(\"en-US\", {\n                                                                            hour: \"numeric\",\n                                                                            minute: \"2-digit\"\n                                                                        })\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, schedule.id, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 25\n                                                        }, this)),\n                                                    hourSchedules.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            const scheduleTime = new Date(currentDate);\n                                                            scheduleTime.setHours(hour, 0, 0, 0);\n                                                            handleCreateSchedule(scheduleTime);\n                                                        },\n                                                        className: \"w-full h-full text-xs text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded\",\n                                                        children: \"+ Add Schedule\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, hour, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            showCreateModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateScheduleModal, {\n                selectedDate: selectedDate,\n                onClose: ()=>setShowCreateModal(false),\n                onCreate: onCreateSchedule\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                lineNumber: 374,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\nfunction CreateScheduleModal({ selectedDate, onClose, onCreate }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        start_time: \"\",\n        duration: 30,\n        job_type: \"sparx_reader\",\n        job_data: {\n            school: \"\",\n            email: \"\",\n            password: \"\"\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedDate) {\n            const defaultTime = new Date(selectedDate);\n            defaultTime.setHours(9, 0, 0, 0); // Default to 9 AM\n            setFormData((prev)=>({\n                    ...prev,\n                    start_time: defaultTime.toISOString().slice(0, 16)\n                }));\n        }\n    }, [\n        selectedDate\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (onCreate) {\n            onCreate({\n                scheduled_time: formData.start_time,\n                duration_minutes: formData.duration,\n                job_type: formData.job_type,\n                job_data: formData.job_data\n            });\n        }\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"Create Schedule\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 425,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.title,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                title: e.target.value\n                                            })),\n                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Start Time\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"datetime-local\",\n                                    value: formData.start_time,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                start_time: e.target.value\n                                            })),\n                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Duration (minutes)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: formData.duration,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                duration: parseInt(e.target.value)\n                                            })),\n                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 15,\n                                            children: \"15 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 30,\n                                            children: \"30 minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 60,\n                                            children: \"1 hour\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 120,\n                                            children: \"2 hours\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 240,\n                                            children: \"4 hours\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Job Details\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"School\",\n                                    value: formData.job_data.school,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                job_data: {\n                                                    ...prev.job_data,\n                                                    school: e.target.value\n                                                }\n                                            })),\n                                    className: \"block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    placeholder: \"Email\",\n                                    value: formData.job_data.email,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                job_data: {\n                                                    ...prev.job_data,\n                                                    email: e.target.value\n                                                }\n                                            })),\n                                    className: \"block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    placeholder: \"Password\",\n                                    value: formData.job_data.password,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                job_data: {\n                                                    ...prev.job_data,\n                                                    password: e.target.value\n                                                }\n                                            })),\n                                    className: \"block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                    children: \"Create Schedule\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 509,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n            lineNumber: 423,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n        lineNumber: 422,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/queue/components/ScheduleCalendar.jsx\n");

/***/ }),

/***/ "(ssr)/./app/queue/page.jsx":
/*!****************************!*\
  !*** ./app/queue/page.jsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QueueDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ScheduleCalendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ScheduleCalendar */ \"(ssr)/./app/queue/components/ScheduleCalendar.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction QueueDashboard() {\n    const [queueStatus, setQueueStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [batches, setBatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [priorityLevels, setPriorityLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Form states\n    const [batchForm, setBatchForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        batch_name: \"\",\n        accounts: [\n            {\n                school: \"\",\n                email: \"\",\n                password: \"\",\n                login_type: \"regular\"\n            }\n        ],\n        scheduled_time: \"\",\n        priority_override: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadQueueData();\n        // Set up auto-refresh for real-time updates\n        const interval = setInterval(()=>{\n            loadQueueData();\n        }, 30000); // Refresh every 30 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    const loadQueueData = async ()=>{\n        try {\n            setLoading(true);\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const headers = {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            };\n            // Load queue status\n            const statusResponse = await fetch(\"/api/queue/status?detailed=true\", {\n                headers\n            });\n            if (statusResponse.ok) {\n                const statusData = await statusResponse.json();\n                setQueueStatus(statusData);\n            }\n            // Load batches\n            const batchResponse = await fetch(\"/api/queue/batch\", {\n                headers\n            });\n            if (batchResponse.ok) {\n                const batchData = await batchResponse.json();\n                setBatches(batchData.batches || []);\n            }\n            // Load schedules\n            const scheduleResponse = await fetch(\"/api/queue/schedule\", {\n                headers\n            });\n            if (scheduleResponse.ok) {\n                const scheduleData = await scheduleResponse.json();\n                setSchedules(scheduleData.schedules || []);\n            }\n            // Load priority levels\n            const priorityResponse = await fetch(\"/api/queue/priority-levels\", {\n                headers\n            });\n            if (priorityResponse.ok) {\n                const priorityData = await priorityResponse.json();\n                setPriorityLevels(priorityData);\n            }\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBatchSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/queue/batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(batchForm)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                alert(`Batch \"${result.batch.name}\" created successfully!`);\n                setBatchForm({\n                    batch_name: \"\",\n                    accounts: [\n                        {\n                            school: \"\",\n                            email: \"\",\n                            password: \"\"\n                        }\n                    ],\n                    scheduled_time: \"\",\n                    priority_override: \"\"\n                });\n                loadQueueData(); // Refresh data\n            } else {\n                const error = await response.json();\n                alert(`Error: ${error.error}`);\n            }\n        } catch (err) {\n            alert(`Error: ${err.message}`);\n        }\n    };\n    const addAccount = ()=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: [\n                    ...prev.accounts,\n                    {\n                        school: \"\",\n                        email: \"\",\n                        password: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeAccount = (index)=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: prev.accounts.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateAccount = (index, field, value)=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: prev.accounts.map((account, i)=>i === index ? {\n                        ...account,\n                        [field]: value\n                    } : account)\n            }));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading queue dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-xl mb-4\",\n                        children: \"⚠️ Error\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: loadQueueData,\n                        className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Queue Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Manage your batch processing and scheduling\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                queueStatus?.license_features && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-900 mb-2\",\n                            children: \"Your License Features\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: queueStatus.license_features.max_accounts_per_batch || \"Unlimited\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: \"Max Accounts per Batch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: queueStatus.license_features.priority_level\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: \"Priority Level\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: queueStatus.license_features.scheduling_access ? \"✅\" : \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: \"Scheduling Access\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                name: \"Overview\"\n                            },\n                            {\n                                id: \"batches\",\n                                name: \"Batches\"\n                            },\n                            {\n                                id: \"schedule\",\n                                name: \"Schedule\"\n                            },\n                            {\n                                id: \"create\",\n                                name: \"Create Batch\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                                children: tab.name\n                            }, tab.id, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        queueStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-500\",\n                                            children: \"Total Batches\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: queueStatus.user_queue_status.total_batches\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-500\",\n                                            children: \"Active Jobs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: queueStatus.user_queue_status.queued_jobs + queueStatus.user_queue_status.processing_jobs\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-500\",\n                                            children: \"Completed Jobs\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: queueStatus.user_queue_status.completed_jobs\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-500\",\n                                            children: \"Estimated Wait\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-600\",\n                                            children: queueStatus.estimated_wait_time.formatted\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 235,\n                            columnNumber: 15\n                        }, this),\n                        queueStatus?.queue_positions && queueStatus.queue_positions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Your Queue Positions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: queueStatus.queue_positions.map((position, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center p-3 bg-gray-50 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Job #\",\n                                                                    position.id\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"Priority: \",\n                                                                    position.effective_priority\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-blue-600\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    position.queue_position\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"in queue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, position.id, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 265,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 232,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"batches\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"Your Batches\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Batch Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Priority\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: batches.map((batch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                        children: batch.batch_name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${batch.status === \"completed\" ? \"bg-green-100 text-green-800\" : batch.status === \"processing\" ? \"bg-blue-100 text-blue-800\" : batch.status === \"failed\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                                                            children: batch.status\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: [\n                                                            batch.processed_accounts,\n                                                            \"/\",\n                                                            batch.total_accounts,\n                                                            batch.failed_accounts > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-600 ml-1\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    batch.failed_accounts,\n                                                                    \" failed)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: batch.priority_level\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: new Date(batch.created_at).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, batch.id, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"schedule\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: !queueStatus?.license_features?.scheduling_access ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCC5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"Scheduling Not Available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Your current license doesn't include scheduling access.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Please upgrade your license to use this feature.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 356,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 355,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScheduleCalendar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        schedules: schedules,\n                        onScheduleSelect: (schedule)=>{\n                            console.log(\"Selected schedule:\", schedule);\n                        // Handle schedule selection (e.g., show details modal)\n                        },\n                        onCreateSchedule: async (scheduleData)=>{\n                            try {\n                                const token = localStorage.getItem(\"token\");\n                                const response = await fetch(\"/api/queue/schedule\", {\n                                    method: \"POST\",\n                                    headers: {\n                                        \"Authorization\": `Bearer ${token}`,\n                                        \"Content-Type\": \"application/json\"\n                                    },\n                                    body: JSON.stringify(scheduleData)\n                                });\n                                if (response.ok) {\n                                    const result = await response.json();\n                                    alert(\"Schedule created successfully!\");\n                                    loadQueueData(); // Refresh data\n                                } else {\n                                    const error = await response.json();\n                                    alert(`Error: ${error.error}`);\n                                }\n                            } catch (err) {\n                                alert(`Error: ${err.message}`);\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 364,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 353,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"Create New Batch\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 402,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 401,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleBatchSubmit,\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Batch Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: batchForm.batch_name,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        batch_name: e.target.value\n                                                    })),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Accounts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        batchForm.accounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-200 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"School\",\n                                                        value: account.school,\n                                                        onChange: (e)=>updateAccount(index, \"school\", e.target.value),\n                                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        placeholder: \"Email\",\n                                                        value: account.email,\n                                                        onChange: (e)=>updateAccount(index, \"email\", e.target.value),\n                                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        placeholder: \"Password\",\n                                                        value: account.password,\n                                                        onChange: (e)=>updateAccount(index, \"password\", e.target.value),\n                                                        className: \"border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeAccount(index),\n                                                        className: \"px-3 py-2 text-red-600 border border-red-300 rounded-md hover:bg-red-50\",\n                                                        disabled: batchForm.accounts.length === 1,\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addAccount,\n                                            className: \"px-4 py-2 text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50\",\n                                            disabled: queueStatus?.license_features?.max_accounts_per_batch > 0 && batchForm.accounts.length >= queueStatus.license_features.max_accounts_per_batch,\n                                            children: \"Add Account\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this),\n                                        queueStatus?.license_features?.max_accounts_per_batch > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-1\",\n                                            children: [\n                                                \"Maximum \",\n                                                queueStatus.license_features.max_accounts_per_batch,\n                                                \" accounts per batch\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this),\n                                queueStatus?.license_features?.scheduling_access && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Schedule Time (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"datetime-local\",\n                                            value: batchForm.scheduled_time,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        scheduled_time: e.target.value\n                                                    })),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setBatchForm({\n                                                    batch_name: \"\",\n                                                    accounts: [\n                                                        {\n                                                            school: \"\",\n                                                            email: \"\",\n                                                            password: \"\"\n                                                        }\n                                                    ],\n                                                    scheduled_time: \"\",\n                                                    priority_override: \"\"\n                                                }),\n                                            className: \"px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                            children: \"Reset\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: \"Create Batch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 400,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcXVldWUvcGFnZS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0E7QUFDaUI7QUFFOUMsU0FBU0k7SUFDdEIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdOLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ08sU0FBU0MsV0FBVyxHQUFHUiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3pDLE1BQU0sQ0FBQ1MsV0FBV0MsYUFBYSxHQUFHViwrQ0FBUUEsQ0FBQyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ1csZ0JBQWdCQyxrQkFBa0IsR0FBR1osK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDYSxTQUFTQyxXQUFXLEdBQUdkLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2UsT0FBT0MsU0FBUyxHQUFHaEIsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDaUIsV0FBV0MsYUFBYSxHQUFHbEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTW1CLFNBQVNqQiwwREFBU0E7SUFFeEIsY0FBYztJQUNkLE1BQU0sQ0FBQ2tCLFdBQVdDLGFBQWEsR0FBR3JCLCtDQUFRQSxDQUFDO1FBQ3pDc0IsWUFBWTtRQUNaQyxVQUFVO1lBQUM7Z0JBQUVDLFFBQVE7Z0JBQUlDLE9BQU87Z0JBQUlDLFVBQVU7Z0JBQUlDLFlBQVk7WUFBVTtTQUFFO1FBQzFFQyxnQkFBZ0I7UUFDaEJDLG1CQUFtQjtJQUNyQjtJQUVBNUIsZ0RBQVNBLENBQUM7UUFDUjZCO1FBRUEsNENBQTRDO1FBQzVDLE1BQU1DLFdBQVdDLFlBQVk7WUFDM0JGO1FBQ0YsR0FBRyxRQUFRLDJCQUEyQjtRQUV0QyxPQUFPLElBQU1HLGNBQWNGO0lBQzdCLEdBQUcsRUFBRTtJQUVMLE1BQU1ELGdCQUFnQjtRQUNwQixJQUFJO1lBQ0ZoQixXQUFXO1lBQ1gsTUFBTW9CLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUVuQyxJQUFJLENBQUNGLE9BQU87Z0JBQ1ZmLE9BQU9rQixJQUFJLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLE1BQU1DLFVBQVU7Z0JBQ2QsaUJBQWlCLENBQUMsT0FBTyxFQUFFSixNQUFNLENBQUM7Z0JBQ2xDLGdCQUFnQjtZQUNsQjtZQUVBLG9CQUFvQjtZQUNwQixNQUFNSyxpQkFBaUIsTUFBTUMsTUFBTSxtQ0FBbUM7Z0JBQUVGO1lBQVE7WUFDaEYsSUFBSUMsZUFBZUUsRUFBRSxFQUFFO2dCQUNyQixNQUFNQyxhQUFhLE1BQU1ILGVBQWVJLElBQUk7Z0JBQzVDckMsZUFBZW9DO1lBQ2pCO1lBRUEsZUFBZTtZQUNmLE1BQU1FLGdCQUFnQixNQUFNSixNQUFNLG9CQUFvQjtnQkFBRUY7WUFBUTtZQUNoRSxJQUFJTSxjQUFjSCxFQUFFLEVBQUU7Z0JBQ3BCLE1BQU1JLFlBQVksTUFBTUQsY0FBY0QsSUFBSTtnQkFDMUNuQyxXQUFXcUMsVUFBVXRDLE9BQU8sSUFBSSxFQUFFO1lBQ3BDO1lBRUEsaUJBQWlCO1lBQ2pCLE1BQU11QyxtQkFBbUIsTUFBTU4sTUFBTSx1QkFBdUI7Z0JBQUVGO1lBQVE7WUFDdEUsSUFBSVEsaUJBQWlCTCxFQUFFLEVBQUU7Z0JBQ3ZCLE1BQU1NLGVBQWUsTUFBTUQsaUJBQWlCSCxJQUFJO2dCQUNoRGpDLGFBQWFxQyxhQUFhdEMsU0FBUyxJQUFJLEVBQUU7WUFDM0M7WUFFQSx1QkFBdUI7WUFDdkIsTUFBTXVDLG1CQUFtQixNQUFNUixNQUFNLDhCQUE4QjtnQkFBRUY7WUFBUTtZQUM3RSxJQUFJVSxpQkFBaUJQLEVBQUUsRUFBRTtnQkFDdkIsTUFBTVEsZUFBZSxNQUFNRCxpQkFBaUJMLElBQUk7Z0JBQ2hEL0Isa0JBQWtCcUM7WUFDcEI7UUFFRixFQUFFLE9BQU9DLEtBQUs7WUFDWmxDLFNBQVNrQyxJQUFJQyxPQUFPO1FBQ3RCLFNBQVU7WUFDUnJDLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXNDLG9CQUFvQixPQUFPQztRQUMvQkEsRUFBRUMsY0FBYztRQUVoQixJQUFJO1lBQ0YsTUFBTXBCLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxNQUFNbUIsV0FBVyxNQUFNZixNQUFNLG9CQUFvQjtnQkFDL0NnQixRQUFRO2dCQUNSbEIsU0FBUztvQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVKLE1BQU0sQ0FBQztvQkFDbEMsZ0JBQWdCO2dCQUNsQjtnQkFDQXVCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ3ZDO1lBQ3ZCO1lBRUEsSUFBSW1DLFNBQVNkLEVBQUUsRUFBRTtnQkFDZixNQUFNbUIsU0FBUyxNQUFNTCxTQUFTWixJQUFJO2dCQUNsQ2tCLE1BQU0sQ0FBQyxPQUFPLEVBQUVELE9BQU9FLEtBQUssQ0FBQ0MsSUFBSSxDQUFDLHVCQUF1QixDQUFDO2dCQUMxRDFDLGFBQWE7b0JBQ1hDLFlBQVk7b0JBQ1pDLFVBQVU7d0JBQUM7NEJBQUVDLFFBQVE7NEJBQUlDLE9BQU87NEJBQUlDLFVBQVU7d0JBQUc7cUJBQUU7b0JBQ25ERSxnQkFBZ0I7b0JBQ2hCQyxtQkFBbUI7Z0JBQ3JCO2dCQUNBQyxpQkFBaUIsZUFBZTtZQUNsQyxPQUFPO2dCQUNMLE1BQU1mLFFBQVEsTUFBTXdDLFNBQVNaLElBQUk7Z0JBQ2pDa0IsTUFBTSxDQUFDLE9BQU8sRUFBRTlDLE1BQU1BLEtBQUssQ0FBQyxDQUFDO1lBQy9CO1FBQ0YsRUFBRSxPQUFPbUMsS0FBSztZQUNaVyxNQUFNLENBQUMsT0FBTyxFQUFFWCxJQUFJQyxPQUFPLENBQUMsQ0FBQztRQUMvQjtJQUNGO0lBRUEsTUFBTWEsYUFBYTtRQUNqQjNDLGFBQWE0QyxDQUFBQSxPQUFTO2dCQUNwQixHQUFHQSxJQUFJO2dCQUNQMUMsVUFBVTt1QkFBSTBDLEtBQUsxQyxRQUFRO29CQUFFO3dCQUFFQyxRQUFRO3dCQUFJQyxPQUFPO3dCQUFJQyxVQUFVO29CQUFHO2lCQUFFO1lBQ3ZFO0lBQ0Y7SUFFQSxNQUFNd0MsZ0JBQWdCLENBQUNDO1FBQ3JCOUMsYUFBYTRDLENBQUFBLE9BQVM7Z0JBQ3BCLEdBQUdBLElBQUk7Z0JBQ1AxQyxVQUFVMEMsS0FBSzFDLFFBQVEsQ0FBQzZDLE1BQU0sQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxNQUFNSDtZQUNqRDtJQUNGO0lBRUEsTUFBTUksZ0JBQWdCLENBQUNKLE9BQU9LLE9BQU9DO1FBQ25DcEQsYUFBYTRDLENBQUFBLE9BQVM7Z0JBQ3BCLEdBQUdBLElBQUk7Z0JBQ1AxQyxVQUFVMEMsS0FBSzFDLFFBQVEsQ0FBQ21ELEdBQUcsQ0FBQyxDQUFDQyxTQUFTTCxJQUNwQ0EsTUFBTUgsUUFBUTt3QkFBRSxHQUFHUSxPQUFPO3dCQUFFLENBQUNILE1BQU0sRUFBRUM7b0JBQU0sSUFBSUU7WUFFbkQ7SUFDRjtJQUVBLElBQUk5RCxTQUFTO1FBQ1gscUJBQ0UsOERBQUMrRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJMUM7SUFFQSxJQUFJOUQsT0FBTztRQUNULHFCQUNFLDhEQUFDNkQ7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FBNEI7Ozs7OztrQ0FDM0MsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFpQjlEOzs7Ozs7a0NBQzlCLDhEQUFDZ0U7d0JBQ0NDLFNBQVNsRDt3QkFDVCtDLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTVQ7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0k7NEJBQUdKLFdBQVU7c0NBQW1DOzs7Ozs7c0NBQ2pELDhEQUFDQzs0QkFBRUQsV0FBVTtzQ0FBcUI7Ozs7Ozs7Ozs7OztnQkFJbkN4RSxhQUFhNkUsa0NBQ1osOERBQUNOO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ007NEJBQUdOLFdBQVU7c0NBQTJDOzs7Ozs7c0NBQ3pELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1p4RSxZQUFZNkUsZ0JBQWdCLENBQUNFLHNCQUFzQixJQUFJOzs7Ozs7c0RBRTFELDhEQUFDUjs0Q0FBSUMsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs4Q0FFekMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1p4RSxZQUFZNkUsZ0JBQWdCLENBQUNHLGNBQWM7Ozs7OztzREFFOUMsOERBQUNUOzRDQUFJQyxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7OzhDQUV6Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWnhFLFlBQVk2RSxnQkFBZ0IsQ0FBQ0ksaUJBQWlCLEdBQUcsTUFBTTs7Ozs7O3NEQUUxRCw4REFBQ1Y7NENBQUlDLFdBQVU7c0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTy9DLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ1U7d0JBQUlWLFdBQVU7a0NBQ1o7NEJBQ0M7Z0NBQUVXLElBQUk7Z0NBQVl6QixNQUFNOzRCQUFXOzRCQUNuQztnQ0FBRXlCLElBQUk7Z0NBQVd6QixNQUFNOzRCQUFVOzRCQUNqQztnQ0FBRXlCLElBQUk7Z0NBQVl6QixNQUFNOzRCQUFXOzRCQUNuQztnQ0FBRXlCLElBQUk7Z0NBQVV6QixNQUFNOzRCQUFlO3lCQUN0QyxDQUFDVyxHQUFHLENBQUMsQ0FBQ2Usb0JBQ0wsOERBQUNWO2dDQUVDQyxTQUFTLElBQU05RCxhQUFhdUUsSUFBSUQsRUFBRTtnQ0FDbENYLFdBQVcsQ0FBQyx5Q0FBeUMsRUFDbkQ1RCxjQUFjd0UsSUFBSUQsRUFBRSxHQUNoQixrQ0FDQSw2RUFDTCxDQUFDOzBDQUVEQyxJQUFJMUIsSUFBSTsrQkFSSjBCLElBQUlELEVBQUU7Ozs7Ozs7Ozs7Ozs7OztnQkFlbEJ2RSxjQUFjLDRCQUNiLDhEQUFDMkQ7b0JBQUlDLFdBQVU7O3dCQUVaeEUsNkJBQ0MsOERBQUN1RTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQW9DOzs7Ozs7c0RBQ25ELDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWnhFLFlBQVlxRixpQkFBaUIsQ0FBQ0MsYUFBYTs7Ozs7Ozs7Ozs7OzhDQUdoRCw4REFBQ2Y7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFBb0M7Ozs7OztzREFDbkQsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNaeEUsWUFBWXFGLGlCQUFpQixDQUFDRSxXQUFXLEdBQUd2RixZQUFZcUYsaUJBQWlCLENBQUNHLGVBQWU7Ozs7Ozs7Ozs7Ozs4Q0FHOUYsOERBQUNqQjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUFvQzs7Ozs7O3NEQUNuRCw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1p4RSxZQUFZcUYsaUJBQWlCLENBQUNJLGNBQWM7Ozs7Ozs7Ozs7Ozs4Q0FHakQsOERBQUNsQjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUFvQzs7Ozs7O3NEQUNuRCw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1p4RSxZQUFZMEYsbUJBQW1CLENBQUNDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFPakQzRixhQUFhNEYsbUJBQW1CNUYsWUFBWTRGLGVBQWUsQ0FBQ0MsTUFBTSxHQUFHLG1CQUNwRSw4REFBQ3RCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNNO3dDQUFHTixXQUFVO2tEQUFvQzs7Ozs7Ozs7Ozs7OENBRXBELDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1p4RSxZQUFZNEYsZUFBZSxDQUFDdkIsR0FBRyxDQUFDLENBQUN5QixVQUFVaEMsc0JBQzFDLDhEQUFDUztnREFBc0JDLFdBQVU7O2tFQUMvQiw4REFBQ0Q7OzBFQUNDLDhEQUFDQTtnRUFBSUMsV0FBVTs7b0VBQWM7b0VBQU1zQixTQUFTWCxFQUFFOzs7Ozs7OzBFQUM5Qyw4REFBQ1o7Z0VBQUlDLFdBQVU7O29FQUF3QjtvRUFBV3NCLFNBQVNDLGtCQUFrQjs7Ozs7Ozs7Ozs7OztrRUFFL0UsOERBQUN4Qjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztvRUFBMEI7b0VBQUVzQixTQUFTRSxjQUFjOzs7Ozs7OzBFQUNsRSw4REFBQ3pCO2dFQUFJQyxXQUFVOzBFQUF3Qjs7Ozs7Ozs7Ozs7OzsrQ0FQakNzQixTQUFTWCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBa0JsQ3ZFLGNBQWMsMkJBQ2IsOERBQUMyRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDTTtnQ0FBR04sV0FBVTswQ0FBb0M7Ozs7Ozs7Ozs7O3NDQUVwRCw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUN5QjtnQ0FBTXpCLFdBQVU7O2tEQUNmLDhEQUFDMEI7d0NBQU0xQixXQUFVO2tEQUNmLDRFQUFDMkI7OzhEQUNDLDhEQUFDQztvREFBRzVCLFdBQVU7OERBQWlGOzs7Ozs7OERBRy9GLDhEQUFDNEI7b0RBQUc1QixXQUFVOzhEQUFpRjs7Ozs7OzhEQUcvRiw4REFBQzRCO29EQUFHNUIsV0FBVTs4REFBaUY7Ozs7Ozs4REFHL0YsOERBQUM0QjtvREFBRzVCLFdBQVU7OERBQWlGOzs7Ozs7OERBRy9GLDhEQUFDNEI7b0RBQUc1QixXQUFVOzhEQUFpRjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS25HLDhEQUFDNkI7d0NBQU03QixXQUFVO2tEQUNkdEUsUUFBUW1FLEdBQUcsQ0FBQyxDQUFDWixzQkFDWiw4REFBQzBDOztrRUFDQyw4REFBQ0c7d0RBQUc5QixXQUFVO2tFQUNYZixNQUFNeEMsVUFBVTs7Ozs7O2tFQUVuQiw4REFBQ3FGO3dEQUFHOUIsV0FBVTtrRUFDWiw0RUFBQytCOzREQUFLL0IsV0FBVyxDQUFDLHlEQUF5RCxFQUN6RWYsTUFBTStDLE1BQU0sS0FBSyxjQUFjLGdDQUMvQi9DLE1BQU0rQyxNQUFNLEtBQUssZUFBZSw4QkFDaEMvQyxNQUFNK0MsTUFBTSxLQUFLLFdBQVcsNEJBQzVCLGdDQUNELENBQUM7c0VBQ0MvQyxNQUFNK0MsTUFBTTs7Ozs7Ozs7Ozs7a0VBR2pCLDhEQUFDRjt3REFBRzlCLFdBQVU7OzREQUNYZixNQUFNZ0Qsa0JBQWtCOzREQUFDOzREQUFFaEQsTUFBTWlELGNBQWM7NERBQy9DakQsTUFBTWtELGVBQWUsR0FBRyxtQkFDdkIsOERBQUNKO2dFQUFLL0IsV0FBVTs7b0VBQW9CO29FQUFFZixNQUFNa0QsZUFBZTtvRUFBQzs7Ozs7Ozs7Ozs7OztrRUFHaEUsOERBQUNMO3dEQUFHOUIsV0FBVTtrRUFDWGYsTUFBTXVCLGNBQWM7Ozs7OztrRUFFdkIsOERBQUNzQjt3REFBRzlCLFdBQVU7a0VBQ1gsSUFBSW9DLEtBQUtuRCxNQUFNb0QsVUFBVSxFQUFFQyxjQUFjOzs7Ozs7OytDQXhCckNyRCxNQUFNMEIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQWtDNUJ2RSxjQUFjLDRCQUNiLDhEQUFDMkQ7OEJBQ0UsQ0FBQ3ZFLGFBQWE2RSxrQkFBa0JJLGtDQUMvQiw4REFBQ1Y7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQThCOzs7Ozs7OENBQzdDLDhEQUFDTTtvQ0FBR04sV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUNDO29DQUFFRCxXQUFVOzhDQUFnQjs7Ozs7OzhDQUM3Qiw4REFBQ0M7b0NBQUVELFdBQVU7OENBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OzZDQUlqQyw4REFBQzFFLG9FQUFnQkE7d0JBQ2ZNLFdBQVdBO3dCQUNYMkcsa0JBQWtCLENBQUNDOzRCQUNqQkMsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQkY7d0JBQ2xDLHVEQUF1RDt3QkFDekQ7d0JBQ0FHLGtCQUFrQixPQUFPekU7NEJBQ3ZCLElBQUk7Z0NBQ0YsTUFBTWIsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO2dDQUNuQyxNQUFNbUIsV0FBVyxNQUFNZixNQUFNLHVCQUF1QjtvQ0FDbERnQixRQUFRO29DQUNSbEIsU0FBUzt3Q0FDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVKLE1BQU0sQ0FBQzt3Q0FDbEMsZ0JBQWdCO29DQUNsQjtvQ0FDQXVCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ1o7Z0NBQ3ZCO2dDQUVBLElBQUlRLFNBQVNkLEVBQUUsRUFBRTtvQ0FDZixNQUFNbUIsU0FBUyxNQUFNTCxTQUFTWixJQUFJO29DQUNsQ2tCLE1BQU07b0NBQ04vQixpQkFBaUIsZUFBZTtnQ0FDbEMsT0FBTztvQ0FDTCxNQUFNZixRQUFRLE1BQU13QyxTQUFTWixJQUFJO29DQUNqQ2tCLE1BQU0sQ0FBQyxPQUFPLEVBQUU5QyxNQUFNQSxLQUFLLENBQUMsQ0FBQztnQ0FDL0I7NEJBQ0YsRUFBRSxPQUFPbUMsS0FBSztnQ0FDWlcsTUFBTSxDQUFDLE9BQU8sRUFBRVgsSUFBSUMsT0FBTyxDQUFDLENBQUM7NEJBQy9CO3dCQUNGOzs7Ozs7Ozs7OztnQkFNUGxDLGNBQWMsMEJBQ2IsOERBQUMyRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDTTtnQ0FBR04sV0FBVTswQ0FBb0M7Ozs7Ozs7Ozs7O3NDQUVwRCw4REFBQzRDOzRCQUFLQyxVQUFVdEU7NEJBQW1CeUIsV0FBVTs7OENBQzNDLDhEQUFDRDs7c0RBQ0MsOERBQUMrQzs0Q0FBTTlDLFdBQVU7c0RBQTBDOzs7Ozs7c0RBQzNELDhEQUFDK0M7NENBQ0NDLE1BQUs7NENBQ0xwRCxPQUFPckQsVUFBVUUsVUFBVTs0Q0FDM0J3RyxVQUFVLENBQUN6RSxJQUFNaEMsYUFBYTRDLENBQUFBLE9BQVM7d0RBQUUsR0FBR0EsSUFBSTt3REFBRTNDLFlBQVkrQixFQUFFMEUsTUFBTSxDQUFDdEQsS0FBSztvREFBQzs0Q0FDN0VJLFdBQVU7NENBQ1ZtRCxRQUFROzs7Ozs7Ozs7Ozs7OENBSVosOERBQUNwRDs7c0RBQ0MsOERBQUMrQzs0Q0FBTTlDLFdBQVU7c0RBQStDOzs7Ozs7d0NBQy9EekQsVUFBVUcsUUFBUSxDQUFDbUQsR0FBRyxDQUFDLENBQUNDLFNBQVNSLHNCQUNoQyw4REFBQ1M7Z0RBQWdCQyxXQUFVOztrRUFDekIsOERBQUMrQzt3REFDQ0MsTUFBSzt3REFDTEksYUFBWTt3REFDWnhELE9BQU9FLFFBQVFuRCxNQUFNO3dEQUNyQnNHLFVBQVUsQ0FBQ3pFLElBQU1rQixjQUFjSixPQUFPLFVBQVVkLEVBQUUwRSxNQUFNLENBQUN0RCxLQUFLO3dEQUM5REksV0FBVTt3REFDVm1ELFFBQVE7Ozs7OztrRUFFViw4REFBQ0o7d0RBQ0NDLE1BQUs7d0RBQ0xJLGFBQVk7d0RBQ1p4RCxPQUFPRSxRQUFRbEQsS0FBSzt3REFDcEJxRyxVQUFVLENBQUN6RSxJQUFNa0IsY0FBY0osT0FBTyxTQUFTZCxFQUFFMEUsTUFBTSxDQUFDdEQsS0FBSzt3REFDN0RJLFdBQVU7d0RBQ1ZtRCxRQUFROzs7Ozs7a0VBRVYsOERBQUNKO3dEQUNDQyxNQUFLO3dEQUNMSSxhQUFZO3dEQUNaeEQsT0FBT0UsUUFBUWpELFFBQVE7d0RBQ3ZCb0csVUFBVSxDQUFDekUsSUFBTWtCLGNBQWNKLE9BQU8sWUFBWWQsRUFBRTBFLE1BQU0sQ0FBQ3RELEtBQUs7d0RBQ2hFSSxXQUFVO3dEQUNWbUQsUUFBUTs7Ozs7O2tFQUVWLDhEQUFDakQ7d0RBQ0M4QyxNQUFLO3dEQUNMN0MsU0FBUyxJQUFNZCxjQUFjQzt3REFDN0JVLFdBQVU7d0RBQ1ZxRCxVQUFVOUcsVUFBVUcsUUFBUSxDQUFDMkUsTUFBTSxLQUFLO2tFQUN6Qzs7Ozs7OzsrQ0E5Qk8vQjs7Ozs7c0RBbUNaLDhEQUFDWTs0Q0FDQzhDLE1BQUs7NENBQ0w3QyxTQUFTaEI7NENBQ1RhLFdBQVU7NENBQ1ZxRCxVQUNFN0gsYUFBYTZFLGtCQUFrQkUseUJBQXlCLEtBQ3hEaEUsVUFBVUcsUUFBUSxDQUFDMkUsTUFBTSxJQUFJN0YsWUFBWTZFLGdCQUFnQixDQUFDRSxzQkFBc0I7c0RBRW5GOzs7Ozs7d0NBR0EvRSxhQUFhNkUsa0JBQWtCRSx5QkFBeUIsbUJBQ3ZELDhEQUFDTjs0Q0FBRUQsV0FBVTs7Z0RBQTZCO2dEQUMvQnhFLFlBQVk2RSxnQkFBZ0IsQ0FBQ0Usc0JBQXNCO2dEQUFDOzs7Ozs7Ozs7Ozs7O2dDQUtsRS9FLGFBQWE2RSxrQkFBa0JJLG1DQUM5Qiw4REFBQ1Y7O3NEQUNDLDhEQUFDK0M7NENBQU05QyxXQUFVO3NEQUEwQzs7Ozs7O3NEQUMzRCw4REFBQytDOzRDQUNDQyxNQUFLOzRDQUNMcEQsT0FBT3JELFVBQVVRLGNBQWM7NENBQy9Ca0csVUFBVSxDQUFDekUsSUFBTWhDLGFBQWE0QyxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUVyQyxnQkFBZ0J5QixFQUFFMEUsTUFBTSxDQUFDdEQsS0FBSztvREFBQzs0Q0FDakZJLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FLaEIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0U7NENBQ0M4QyxNQUFLOzRDQUNMN0MsU0FBUyxJQUFNM0QsYUFBYTtvREFDMUJDLFlBQVk7b0RBQ1pDLFVBQVU7d0RBQUM7NERBQUVDLFFBQVE7NERBQUlDLE9BQU87NERBQUlDLFVBQVU7d0RBQUc7cURBQUU7b0RBQ25ERSxnQkFBZ0I7b0RBQ2hCQyxtQkFBbUI7Z0RBQ3JCOzRDQUNBZ0QsV0FBVTtzREFDWDs7Ozs7O3NEQUdELDhEQUFDRTs0Q0FDQzhDLE1BQUs7NENBQ0xoRCxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVqQiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vYXBwL3F1ZXVlL3BhZ2UuanN4P2FjYzYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCBTY2hlZHVsZUNhbGVuZGFyIGZyb20gJy4vY29tcG9uZW50cy9TY2hlZHVsZUNhbGVuZGFyJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUXVldWVEYXNoYm9hcmQoKSB7XG4gIGNvbnN0IFtxdWV1ZVN0YXR1cywgc2V0UXVldWVTdGF0dXNdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtiYXRjaGVzLCBzZXRCYXRjaGVzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3NjaGVkdWxlcywgc2V0U2NoZWR1bGVzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3ByaW9yaXR5TGV2ZWxzLCBzZXRQcmlvcml0eUxldmVsc10gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZSgnb3ZlcnZpZXcnKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgLy8gRm9ybSBzdGF0ZXNcbiAgY29uc3QgW2JhdGNoRm9ybSwgc2V0QmF0Y2hGb3JtXSA9IHVzZVN0YXRlKHtcbiAgICBiYXRjaF9uYW1lOiAnJyxcbiAgICBhY2NvdW50czogW3sgc2Nob29sOiAnJywgZW1haWw6ICcnLCBwYXNzd29yZDogJycsIGxvZ2luX3R5cGU6ICdyZWd1bGFyJyB9XSxcbiAgICBzY2hlZHVsZWRfdGltZTogJycsXG4gICAgcHJpb3JpdHlfb3ZlcnJpZGU6ICcnXG4gIH0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFF1ZXVlRGF0YSgpO1xuICAgIFxuICAgIC8vIFNldCB1cCBhdXRvLXJlZnJlc2ggZm9yIHJlYWwtdGltZSB1cGRhdGVzXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBsb2FkUXVldWVEYXRhKCk7XG4gICAgfSwgMzAwMDApOyAvLyBSZWZyZXNoIGV2ZXJ5IDMwIHNlY29uZHNcbiAgICBcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBsb2FkUXVldWVEYXRhID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgIFxuICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgaGVhZGVycyA9IHtcbiAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgfTtcblxuICAgICAgLy8gTG9hZCBxdWV1ZSBzdGF0dXNcbiAgICAgIGNvbnN0IHN0YXR1c1Jlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvcXVldWUvc3RhdHVzP2RldGFpbGVkPXRydWUnLCB7IGhlYWRlcnMgfSk7XG4gICAgICBpZiAoc3RhdHVzUmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3Qgc3RhdHVzRGF0YSA9IGF3YWl0IHN0YXR1c1Jlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgc2V0UXVldWVTdGF0dXMoc3RhdHVzRGF0YSk7XG4gICAgICB9XG5cbiAgICAgIC8vIExvYWQgYmF0Y2hlc1xuICAgICAgY29uc3QgYmF0Y2hSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3F1ZXVlL2JhdGNoJywgeyBoZWFkZXJzIH0pO1xuICAgICAgaWYgKGJhdGNoUmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgYmF0Y2hEYXRhID0gYXdhaXQgYmF0Y2hSZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldEJhdGNoZXMoYmF0Y2hEYXRhLmJhdGNoZXMgfHwgW10pO1xuICAgICAgfVxuXG4gICAgICAvLyBMb2FkIHNjaGVkdWxlc1xuICAgICAgY29uc3Qgc2NoZWR1bGVSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3F1ZXVlL3NjaGVkdWxlJywgeyBoZWFkZXJzIH0pO1xuICAgICAgaWYgKHNjaGVkdWxlUmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3Qgc2NoZWR1bGVEYXRhID0gYXdhaXQgc2NoZWR1bGVSZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldFNjaGVkdWxlcyhzY2hlZHVsZURhdGEuc2NoZWR1bGVzIHx8IFtdKTtcbiAgICAgIH1cblxuICAgICAgLy8gTG9hZCBwcmlvcml0eSBsZXZlbHNcbiAgICAgIGNvbnN0IHByaW9yaXR5UmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9xdWV1ZS9wcmlvcml0eS1sZXZlbHMnLCB7IGhlYWRlcnMgfSk7XG4gICAgICBpZiAocHJpb3JpdHlSZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBwcmlvcml0eURhdGEgPSBhd2FpdCBwcmlvcml0eVJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgc2V0UHJpb3JpdHlMZXZlbHMocHJpb3JpdHlEYXRhKTtcbiAgICAgIH1cblxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyLm1lc3NhZ2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQmF0Y2hTdWJtaXQgPSBhc3luYyAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvcXVldWUvYmF0Y2gnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGJhdGNoRm9ybSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBhbGVydChgQmF0Y2ggXCIke3Jlc3VsdC5iYXRjaC5uYW1lfVwiIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5IWApO1xuICAgICAgICBzZXRCYXRjaEZvcm0oe1xuICAgICAgICAgIGJhdGNoX25hbWU6ICcnLFxuICAgICAgICAgIGFjY291bnRzOiBbeyBzY2hvb2w6ICcnLCBlbWFpbDogJycsIHBhc3N3b3JkOiAnJyB9XSxcbiAgICAgICAgICBzY2hlZHVsZWRfdGltZTogJycsXG4gICAgICAgICAgcHJpb3JpdHlfb3ZlcnJpZGU6ICcnXG4gICAgICAgIH0pO1xuICAgICAgICBsb2FkUXVldWVEYXRhKCk7IC8vIFJlZnJlc2ggZGF0YVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgZXJyb3IgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGFsZXJ0KGBFcnJvcjogJHtlcnJvci5lcnJvcn1gKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGFsZXJ0KGBFcnJvcjogJHtlcnIubWVzc2FnZX1gKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgYWRkQWNjb3VudCA9ICgpID0+IHtcbiAgICBzZXRCYXRjaEZvcm0ocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGFjY291bnRzOiBbLi4ucHJldi5hY2NvdW50cywgeyBzY2hvb2w6ICcnLCBlbWFpbDogJycsIHBhc3N3b3JkOiAnJyB9XVxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCByZW1vdmVBY2NvdW50ID0gKGluZGV4KSA9PiB7XG4gICAgc2V0QmF0Y2hGb3JtKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBhY2NvdW50czogcHJldi5hY2NvdW50cy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KVxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVBY2NvdW50ID0gKGluZGV4LCBmaWVsZCwgdmFsdWUpID0+IHtcbiAgICBzZXRCYXRjaEZvcm0ocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGFjY291bnRzOiBwcmV2LmFjY291bnRzLm1hcCgoYWNjb3VudCwgaSkgPT4gXG4gICAgICAgIGkgPT09IGluZGV4ID8geyAuLi5hY2NvdW50LCBbZmllbGRdOiB2YWx1ZSB9IDogYWNjb3VudFxuICAgICAgKVxuICAgIH0pKTtcbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC00IHRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIHF1ZXVlIGRhc2hib2FyZC4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKGVycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIHRleHQteGwgbWItNFwiPuKaoO+4jyBFcnJvcjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj57ZXJyb3J9PC9wPlxuICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICBvbkNsaWNrPXtsb2FkUXVldWVEYXRhfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNCBweC00IHB5LTIgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkIGhvdmVyOmJnLWJsdWUtNzAwXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBSZXRyeVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5RdWV1ZSBEYXNoYm9hcmQ8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1ncmF5LTYwMFwiPk1hbmFnZSB5b3VyIGJhdGNoIHByb2Nlc3NpbmcgYW5kIHNjaGVkdWxpbmc8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBMaWNlbnNlIEZlYXR1cmVzIEJhbm5lciAqL31cbiAgICAgICAge3F1ZXVlU3RhdHVzPy5saWNlbnNlX2ZlYXR1cmVzICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtYmx1ZS05MDAgbWItMlwiPllvdXIgTGljZW5zZSBGZWF0dXJlczwvaDM+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtxdWV1ZVN0YXR1cy5saWNlbnNlX2ZlYXR1cmVzLm1heF9hY2NvdW50c19wZXJfYmF0Y2ggfHwgJ1VubGltaXRlZCd9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5NYXggQWNjb3VudHMgcGVyIEJhdGNoPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAge3F1ZXVlU3RhdHVzLmxpY2Vuc2VfZmVhdHVyZXMucHJpb3JpdHlfbGV2ZWx9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5Qcmlvcml0eSBMZXZlbDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtxdWV1ZVN0YXR1cy5saWNlbnNlX2ZlYXR1cmVzLnNjaGVkdWxpbmdfYWNjZXNzID8gJ+KchScgOiAn4p2MJ31cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMFwiPlNjaGVkdWxpbmcgQWNjZXNzPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFRhYnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LThcIj5cbiAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgIHsgaWQ6ICdvdmVydmlldycsIG5hbWU6ICdPdmVydmlldycgfSxcbiAgICAgICAgICAgICAgeyBpZDogJ2JhdGNoZXMnLCBuYW1lOiAnQmF0Y2hlcycgfSxcbiAgICAgICAgICAgICAgeyBpZDogJ3NjaGVkdWxlJywgbmFtZTogJ1NjaGVkdWxlJyB9LFxuICAgICAgICAgICAgICB7IGlkOiAnY3JlYXRlJywgbmFtZTogJ0NyZWF0ZSBCYXRjaCcgfVxuICAgICAgICAgICAgXS5tYXAoKHRhYikgPT4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXt0YWIuaWR9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKHRhYi5pZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHktMiBweC0xIGJvcmRlci1iLTIgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSB0YWIuaWRcbiAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIHRleHQtYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci10cmFuc3BhcmVudCB0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS03MDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3RhYi5uYW1lfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvbmF2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogVGFiIENvbnRlbnQgKi99XG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdvdmVydmlldycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICB7LyogUXVldWUgU3RhdHVzIENhcmRzICovfVxuICAgICAgICAgICAge3F1ZXVlU3RhdHVzICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwXCI+VG90YWwgQmF0Y2hlczwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7cXVldWVTdGF0dXMudXNlcl9xdWV1ZV9zdGF0dXMudG90YWxfYmF0Y2hlc31cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMFwiPkFjdGl2ZSBKb2JzPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtxdWV1ZVN0YXR1cy51c2VyX3F1ZXVlX3N0YXR1cy5xdWV1ZWRfam9icyArIHF1ZXVlU3RhdHVzLnVzZXJfcXVldWVfc3RhdHVzLnByb2Nlc3Npbmdfam9ic31cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMFwiPkNvbXBsZXRlZCBKb2JzPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7cXVldWVTdGF0dXMudXNlcl9xdWV1ZV9zdGF0dXMuY29tcGxldGVkX2pvYnN9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDBcIj5Fc3RpbWF0ZWQgV2FpdDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1vcmFuZ2UtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtxdWV1ZVN0YXR1cy5lc3RpbWF0ZWRfd2FpdF90aW1lLmZvcm1hdHRlZH1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBRdWV1ZSBQb3NpdGlvbnMgKi99XG4gICAgICAgICAgICB7cXVldWVTdGF0dXM/LnF1ZXVlX3Bvc2l0aW9ucyAmJiBxdWV1ZVN0YXR1cy5xdWV1ZV9wb3NpdGlvbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3dcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5Zb3VyIFF1ZXVlIFBvc2l0aW9uczwvaDM+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIHtxdWV1ZVN0YXR1cy5xdWV1ZV9wb3NpdGlvbnMubWFwKChwb3NpdGlvbiwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17cG9zaXRpb24uaWR9IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBwLTMgYmctZ3JheS01MCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+Sm9iICN7cG9zaXRpb24uaWR9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+UHJpb3JpdHk6IHtwb3NpdGlvbi5lZmZlY3RpdmVfcHJpb3JpdHl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+I3twb3NpdGlvbi5xdWV1ZV9wb3NpdGlvbn08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5pbiBxdWV1ZTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnYmF0Y2hlcycgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3dcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+WW91ciBCYXRjaGVzPC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cIm1pbi13LWZ1bGwgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIEJhdGNoIE5hbWVcbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgUHJvZ3Jlc3NcbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIFByaW9yaXR5XG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBDcmVhdGVkXG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAge2JhdGNoZXMubWFwKChiYXRjaCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8dHIga2V5PXtiYXRjaC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtiYXRjaC5iYXRjaF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmF0Y2guc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmF0Y2guc3RhdHVzID09PSAncHJvY2Vzc2luZycgPyAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICBiYXRjaC5zdGF0dXMgPT09ICdmYWlsZWQnID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2JhdGNoLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7YmF0Y2gucHJvY2Vzc2VkX2FjY291bnRzfS97YmF0Y2gudG90YWxfYWNjb3VudHN9XG4gICAgICAgICAgICAgICAgICAgICAgICB7YmF0Y2guZmFpbGVkX2FjY291bnRzID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBtbC0xXCI+KHtiYXRjaC5mYWlsZWRfYWNjb3VudHN9IGZhaWxlZCk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtiYXRjaC5wcmlvcml0eV9sZXZlbH1cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoYmF0Y2guY3JlYXRlZF9hdCkudG9Mb2NhbGVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHthY3RpdmVUYWIgPT09ICdzY2hlZHVsZScgJiYgKFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICB7IXF1ZXVlU3RhdHVzPy5saWNlbnNlX2ZlYXR1cmVzPy5zY2hlZHVsaW5nX2FjY2VzcyA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvd1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LTZ4bCBtYi00XCI+8J+ThTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+U2NoZWR1bGluZyBOb3QgQXZhaWxhYmxlPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5Zb3VyIGN1cnJlbnQgbGljZW5zZSBkb2Vzbid0IGluY2x1ZGUgc2NoZWR1bGluZyBhY2Nlc3MuPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlBsZWFzZSB1cGdyYWRlIHlvdXIgbGljZW5zZSB0byB1c2UgdGhpcyBmZWF0dXJlLjwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8U2NoZWR1bGVDYWxlbmRhclxuICAgICAgICAgICAgICAgIHNjaGVkdWxlcz17c2NoZWR1bGVzfVxuICAgICAgICAgICAgICAgIG9uU2NoZWR1bGVTZWxlY3Q9eyhzY2hlZHVsZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1NlbGVjdGVkIHNjaGVkdWxlOicsIHNjaGVkdWxlKTtcbiAgICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBzY2hlZHVsZSBzZWxlY3Rpb24gKGUuZy4sIHNob3cgZGV0YWlscyBtb2RhbClcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIG9uQ3JlYXRlU2NoZWR1bGU9e2FzeW5jIChzY2hlZHVsZURhdGEpID0+IHtcbiAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvcXVldWUvc2NoZWR1bGUnLCB7XG4gICAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHNjaGVkdWxlRGF0YSlcbiAgICAgICAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgICAgICAgICAgICAgIGFsZXJ0KCdTY2hlZHVsZSBjcmVhdGVkIHN1Y2Nlc3NmdWxseSEnKTtcbiAgICAgICAgICAgICAgICAgICAgICBsb2FkUXVldWVEYXRhKCk7IC8vIFJlZnJlc2ggZGF0YVxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgICAgICAgICAgICAgIGFsZXJ0KGBFcnJvcjogJHtlcnJvci5lcnJvcn1gKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgICAgIGFsZXJ0KGBFcnJvcjogJHtlcnIubWVzc2FnZX1gKTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7YWN0aXZlVGFiID09PSAnY3JlYXRlJyAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvd1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC02IHB5LTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5DcmVhdGUgTmV3IEJhdGNoPC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZUJhdGNoU3VibWl0fSBjbGFzc05hbWU9XCJwLTYgc3BhY2UteS02XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPkJhdGNoIE5hbWU8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2JhdGNoRm9ybS5iYXRjaF9uYW1lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRCYXRjaEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBiYXRjaF9uYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgcHgtMyBweS0yIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+QWNjb3VudHM8L2xhYmVsPlxuICAgICAgICAgICAgICAgIHtiYXRjaEZvcm0uYWNjb3VudHMubWFwKChhY2NvdW50LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC00IG1iLTQgcC00IGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTY2hvb2xcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXthY2NvdW50LnNjaG9vbH1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUFjY291bnQoaW5kZXgsICdzY2hvb2wnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMiBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2FjY291bnQuZW1haWx9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVBY2NvdW50KGluZGV4LCAnZW1haWwnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMiBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJQYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2FjY291bnQucGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVBY2NvdW50KGluZGV4LCAncGFzc3dvcmQnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMiBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVBY2NvdW50KGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC1yZWQtNjAwIGJvcmRlciBib3JkZXItcmVkLTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLXJlZC01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2JhdGNoRm9ybS5hY2NvdW50cy5sZW5ndGggPT09IDF9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICBSZW1vdmVcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2FkZEFjY291bnR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1ibHVlLTYwMCBib3JkZXIgYm9yZGVyLWJsdWUtMzAwIHJvdW5kZWQtbWQgaG92ZXI6YmctYmx1ZS01MFwiXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17XG4gICAgICAgICAgICAgICAgICAgIHF1ZXVlU3RhdHVzPy5saWNlbnNlX2ZlYXR1cmVzPy5tYXhfYWNjb3VudHNfcGVyX2JhdGNoID4gMCAmJiBcbiAgICAgICAgICAgICAgICAgICAgYmF0Y2hGb3JtLmFjY291bnRzLmxlbmd0aCA+PSBxdWV1ZVN0YXR1cy5saWNlbnNlX2ZlYXR1cmVzLm1heF9hY2NvdW50c19wZXJfYmF0Y2hcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBBZGQgQWNjb3VudFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIHtxdWV1ZVN0YXR1cz8ubGljZW5zZV9mZWF0dXJlcz8ubWF4X2FjY291bnRzX3Blcl9iYXRjaCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgTWF4aW11bSB7cXVldWVTdGF0dXMubGljZW5zZV9mZWF0dXJlcy5tYXhfYWNjb3VudHNfcGVyX2JhdGNofSBhY2NvdW50cyBwZXIgYmF0Y2hcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7cXVldWVTdGF0dXM/LmxpY2Vuc2VfZmVhdHVyZXM/LnNjaGVkdWxpbmdfYWNjZXNzICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlNjaGVkdWxlIFRpbWUgKE9wdGlvbmFsKTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGV0aW1lLWxvY2FsXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2JhdGNoRm9ybS5zY2hlZHVsZWRfdGltZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRCYXRjaEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBzY2hlZHVsZWRfdGltZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIGJsb2NrIHctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgcHgtMyBweS0yIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEJhdGNoRm9ybSh7XG4gICAgICAgICAgICAgICAgICAgIGJhdGNoX25hbWU6ICcnLFxuICAgICAgICAgICAgICAgICAgICBhY2NvdW50czogW3sgc2Nob29sOiAnJywgZW1haWw6ICcnLCBwYXNzd29yZDogJycgfV0sXG4gICAgICAgICAgICAgICAgICAgIHNjaGVkdWxlZF90aW1lOiAnJyxcbiAgICAgICAgICAgICAgICAgICAgcHJpb3JpdHlfb3ZlcnJpZGU6ICcnXG4gICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBSZXNldFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIENyZWF0ZSBCYXRjaFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsIlNjaGVkdWxlQ2FsZW5kYXIiLCJRdWV1ZURhc2hib2FyZCIsInF1ZXVlU3RhdHVzIiwic2V0UXVldWVTdGF0dXMiLCJiYXRjaGVzIiwic2V0QmF0Y2hlcyIsInNjaGVkdWxlcyIsInNldFNjaGVkdWxlcyIsInByaW9yaXR5TGV2ZWxzIiwic2V0UHJpb3JpdHlMZXZlbHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJyb3V0ZXIiLCJiYXRjaEZvcm0iLCJzZXRCYXRjaEZvcm0iLCJiYXRjaF9uYW1lIiwiYWNjb3VudHMiLCJzY2hvb2wiLCJlbWFpbCIsInBhc3N3b3JkIiwibG9naW5fdHlwZSIsInNjaGVkdWxlZF90aW1lIiwicHJpb3JpdHlfb3ZlcnJpZGUiLCJsb2FkUXVldWVEYXRhIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJwdXNoIiwiaGVhZGVycyIsInN0YXR1c1Jlc3BvbnNlIiwiZmV0Y2giLCJvayIsInN0YXR1c0RhdGEiLCJqc29uIiwiYmF0Y2hSZXNwb25zZSIsImJhdGNoRGF0YSIsInNjaGVkdWxlUmVzcG9uc2UiLCJzY2hlZHVsZURhdGEiLCJwcmlvcml0eVJlc3BvbnNlIiwicHJpb3JpdHlEYXRhIiwiZXJyIiwibWVzc2FnZSIsImhhbmRsZUJhdGNoU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwicmVzcG9uc2UiLCJtZXRob2QiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInJlc3VsdCIsImFsZXJ0IiwiYmF0Y2giLCJuYW1lIiwiYWRkQWNjb3VudCIsInByZXYiLCJyZW1vdmVBY2NvdW50IiwiaW5kZXgiLCJmaWx0ZXIiLCJfIiwiaSIsInVwZGF0ZUFjY291bnQiLCJmaWVsZCIsInZhbHVlIiwibWFwIiwiYWNjb3VudCIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwiaDEiLCJsaWNlbnNlX2ZlYXR1cmVzIiwiaDMiLCJtYXhfYWNjb3VudHNfcGVyX2JhdGNoIiwicHJpb3JpdHlfbGV2ZWwiLCJzY2hlZHVsaW5nX2FjY2VzcyIsIm5hdiIsImlkIiwidGFiIiwidXNlcl9xdWV1ZV9zdGF0dXMiLCJ0b3RhbF9iYXRjaGVzIiwicXVldWVkX2pvYnMiLCJwcm9jZXNzaW5nX2pvYnMiLCJjb21wbGV0ZWRfam9icyIsImVzdGltYXRlZF93YWl0X3RpbWUiLCJmb3JtYXR0ZWQiLCJxdWV1ZV9wb3NpdGlvbnMiLCJsZW5ndGgiLCJwb3NpdGlvbiIsImVmZmVjdGl2ZV9wcmlvcml0eSIsInF1ZXVlX3Bvc2l0aW9uIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInRkIiwic3BhbiIsInN0YXR1cyIsInByb2Nlc3NlZF9hY2NvdW50cyIsInRvdGFsX2FjY291bnRzIiwiZmFpbGVkX2FjY291bnRzIiwiRGF0ZSIsImNyZWF0ZWRfYXQiLCJ0b0xvY2FsZVN0cmluZyIsIm9uU2NoZWR1bGVTZWxlY3QiLCJzY2hlZHVsZSIsImNvbnNvbGUiLCJsb2ciLCJvbkNyZWF0ZVNjaGVkdWxlIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJpbnB1dCIsInR5cGUiLCJvbkNoYW5nZSIsInRhcmdldCIsInJlcXVpcmVkIiwicGxhY2Vob2xkZXIiLCJkaXNhYmxlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/queue/page.jsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ff337214c78b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFkZXItYXV0by8uL2FwcC9nbG9iYWxzLmNzcz9lNzExIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZmYzMzcyMTRjNzhiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.jsx":
/*!************************!*\
  !*** ./app/layout.jsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Reader Auto\",\n    description: \"Sparx Reader Automation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\layout.jsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\layout.jsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBc0I7QUFFZixNQUFNQSxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQUU7SUFDN0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vYXBwL2xheW91dC5qc3g/MGM4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdSZWFkZXIgQXV0bycsXG4gIGRlc2NyaXB0aW9uOiAnU3BhcnggUmVhZGVyIEF1dG9tYXRpb24nLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.jsx\n");

/***/ }),

/***/ "(rsc)/./app/queue/page.jsx":
/*!****************************!*\
  !*** ./app/queue/page.jsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\reader-auto-main\app\queue\page.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fqueue%2Fpage&page=%2Fqueue%2Fpage&appPaths=%2Fqueue%2Fpage&pagePath=private-next-app-dir%2Fqueue%2Fpage.jsx&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();