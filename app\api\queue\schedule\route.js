import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getWebhookManager } from '../../../../lib/webhook';
import { getAuthManager } from '../../../../lib/auth';
import QueueMiddleware from '../../../../lib/queueMiddleware';

// Middleware wrapper for Next.js API routes
function withMiddleware(handler, middlewares) {
  return async (request, context) => {
    const req = {
      ...request,
      body: await request.json().catch(() => ({})),
      user: null,
      licenseFeatures: null,
      degradedMode: false,
      url: request.url
    };
    
    const res = {
      status: (code) => ({ json: (data) => NextResponse.json(data, { status: code }) }),
      json: (data) => NextResponse.json(data)
    };

    // Authenticate user first
    try {
      const auth = getAuthManager();
      const authHeader = request.headers.get('authorization');

      if (!authHeader?.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const token = authHeader.substring(7);
      const session = auth.validateSession(token);

      if (!session) {
        return NextResponse.json({ error: 'Invalid or expired session' }, { status: 401 });
      }

      req.user = {
        id: session.userId,
        username: session.username,
        role: session.role
      };
    } catch (error) {
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }

    // Apply middlewares
    for (const middleware of middlewares) {
      try {
        let nextCalled = false;
        const next = () => { nextCalled = true; };
        
        const result = await middleware(req, res, next);
        
        if (result instanceof NextResponse) {
          return result;
        }
        
        if (!nextCalled) {
          return NextResponse.json({ error: 'Middleware blocked request' }, { status: 403 });
        }
      } catch (error) {
        console.error('Middleware error:', error);
        return NextResponse.json({ error: 'Request processing failed' }, { status: 500 });
      }
    }

    return handler(req, context);
  };
}

// GET - Get user's schedules
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const db = getDatabase();
      const url = new URL(request.url);
      const startDate = url.searchParams.get('start_date');
      const endDate = url.searchParams.get('end_date');
      const view = url.searchParams.get('view') || 'month'; // month, week, day

      // Calculate date range based on view if not provided
      let calculatedStartDate = startDate;
      let calculatedEndDate = endDate;

      if (!startDate || !endDate) {
        const now = new Date();
        switch (view) {
          case 'day':
            calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
            calculatedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1).toISOString();
            break;
          case 'week':
            const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
            calculatedStartDate = startOfWeek.toISOString();
            calculatedEndDate = new Date(startOfWeek.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();
            break;
          case 'month':
          default:
            calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
            calculatedEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString();
            break;
        }
      }

      const schedules = db.getUserSchedules(req.user.id, calculatedStartDate, calculatedEndDate);

      // Format schedules for calendar view
      const formattedSchedules = schedules.map(schedule => ({
        id: schedule.id,
        title: schedule.batch_name || `${schedule.job_type} Job`,
        start: schedule.scheduled_time,
        end: new Date(new Date(schedule.scheduled_time).getTime() + schedule.duration_minutes * 60 * 1000).toISOString(),
        status: schedule.status,
        job_type: schedule.job_type,
        batch_id: schedule.batch_id,
        job_id: schedule.job_id,
        duration_minutes: schedule.duration_minutes
      }));

      return NextResponse.json({
        schedules: formattedSchedules,
        view_info: {
          view,
          start_date: calculatedStartDate,
          end_date: calculatedEndDate,
          total_schedules: schedules.length
        },
        license_info: {
          scheduling_access: req.licenseFeatures.scheduling_access,
          priority_level: req.licenseFeatures.priority_level
        }
      });

    } catch (error) {
      console.error('Get schedules error:', error);
      return NextResponse.json({
        error: 'Failed to retrieve schedules',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures,
    QueueMiddleware.logQueueActivity
  ]);

  return handler(request);
}

// POST - Create scheduled job/batch
export async function POST(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const db = getDatabase();
      const webhook = getWebhookManager();
      const {
        scheduled_time,
        srp_target = 100,
        job_type = 'sparx_reader',
        job_data,
        batch_data,
        admin_override = false
      } = req.body;

      // Validate required fields
      if (!scheduled_time) {
        return NextResponse.json({
          error: 'scheduled_time is required'
        }, { status: 400 });
      }

      // Validate SRP target
      if (srp_target < 1 || srp_target > 400) {
        return NextResponse.json({
          error: 'SRP target must be between 1 and 400'
        }, { status: 400 });
      }

      // Validate scheduled time is in the future
      const scheduledDate = new Date(scheduled_time);
      const now = new Date();
      
      if (scheduledDate <= now && !admin_override) {
        return NextResponse.json({
          error: 'Scheduled time must be in the future'
        }, { status: 400 });
      }

      // Handle degraded mode
      if (req.degradedMode) {
        return NextResponse.json({
          error: 'Scheduling not available',
          reason: req.degradationReason,
          alternative: 'Please upgrade your license for scheduling access'
        }, { status: 403 });
      }

      let scheduleId, jobId, batchId;

      if (batch_data) {
        // Create scheduled batch
        const { batch_name, login_type, accounts } = batch_data;

        if (!batch_name || !login_type || !accounts || !Array.isArray(accounts)) {
          return NextResponse.json({
            error: 'Invalid batch data',
            details: 'batch_name, login_type, and accounts array are required for batch scheduling'
          }, { status: 400 });
        }

        // Validate login type
        if (!['normal', 'google', 'microsoft'].includes(login_type)) {
          return NextResponse.json({
            error: 'Invalid login type',
            details: 'login_type must be one of: normal, google, microsoft'
          }, { status: 400 });
        }

        // Create the batch with scheduled time
        batchId = db.createQueueBatch(
          req.user.id,
          batch_name,
          accounts,
          scheduled_time,
          login_type
        );

        // The batch creation already creates the schedule entry
        const schedules = db.getUserSchedules(req.user.id, scheduled_time, scheduled_time);
        scheduleId = schedules.find(s => s.batch_id === batchId)?.id;

        await webhook.sendBatchCreated(
          req.user.username,
          batch_name,
          accounts.length,
          scheduled_time
        );

      } else if (job_data) {
        // Create scheduled individual job
        if (!job_data.school || !job_data.email || !job_data.password) {
          return NextResponse.json({
            error: 'Invalid job data',
            details: 'school, email, and password are required for job scheduling'
          }, { status: 400 });
        }

        // Create individual job
        const jobStmt = db.db.prepare(`
          INSERT INTO queue_jobs (user_id, job_type, job_data, priority_level, effective_priority, srp_target, scheduled_time)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        const effectivePriority = db.calculateEffectivePriority(req.licenseFeatures.priority_level, scheduled_time);
        const jobResult = jobStmt.run(
          req.user.id,
          job_type,
          JSON.stringify(job_data),
          req.licenseFeatures.priority_level,
          effectivePriority,
          srp_target,
          scheduled_time
        );

        jobId = jobResult.lastInsertRowid;

        // Create schedule entry
        scheduleId = db.createScheduleEntry(req.user.id, scheduled_time, jobId, null, 30, srp_target);

      } else {
        return NextResponse.json({
          error: 'Either batch_data or job_data is required'
        }, { status: 400 });
      }

      // Log activity
      db.logActivity(
        req.user.id, 
        'SCHEDULE_CREATED', 
        `Scheduled ${batch_data ? 'batch' : 'job'} for ${scheduled_time}`
      );

      return NextResponse.json({
        success: true,
        schedule: {
          id: scheduleId,
          scheduled_time,
          srp_target,
          job_id: jobId,
          batch_id: batchId,
          status: 'scheduled'
        },
        message: `${batch_data ? 'Batch' : 'Job'} scheduled successfully`
      });

    } catch (error) {
      console.error('Schedule creation error:', error);
      
      const webhook = getWebhookManager();
      await webhook.sendQueueAlert(
        'system_error',
        'Schedule creation failed',
        {
          user: req.user?.username || 'unknown',
          error: error.message,
          scheduled_time: req.body?.scheduled_time || 'unknown'
        }
      );

      return NextResponse.json({
        error: 'Schedule creation failed',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures,
    QueueMiddleware.validateWeeklyScheduleLimit,
    QueueMiddleware.checkScheduleConflicts,
    QueueMiddleware.logQueueActivity
  ]);

  return handler(request);
}

// PATCH - Update schedule (reschedule, cancel, etc.)
export async function PATCH(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const db = getDatabase();
      const webhook = getWebhookManager();
      const { schedule_id, action, new_scheduled_time, duration_minutes } = req.body;

      if (!schedule_id || !action) {
        return NextResponse.json({
          error: 'schedule_id and action are required'
        }, { status: 400 });
      }

      // Get schedule and verify ownership
      const schedules = db.getUserSchedules(req.user.id);
      const schedule = schedules.find(s => s.id === schedule_id);

      if (!schedule && req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Schedule not found or access denied'
        }, { status: 404 });
      }

      let result;
      switch (action) {
        case 'cancel':
          const cancelStmt = db.db.prepare(`
            UPDATE queue_schedules 
            SET status = 'cancelled' 
            WHERE id = ?
          `);
          result = cancelStmt.run(schedule_id);

          // Cancel associated jobs/batches
          if (schedule.job_id) {
            db.updateJobStatus(schedule.job_id, 'cancelled');
          }
          if (schedule.batch_id) {
            db.updateBatchStatus(schedule.batch_id, 'cancelled');
          }

          await webhook.sendQueueAlert(
            'info',
            'Schedule cancelled',
            {
              schedule_id,
              user: req.user.username,
              original_time: schedule.scheduled_time
            }
          );
          break;

        case 'reschedule':
          if (!new_scheduled_time) {
            return NextResponse.json({
              error: 'new_scheduled_time is required for reschedule action'
            }, { status: 400 });
          }

          // Check for conflicts at new time
          const conflicts = db.checkScheduleConflicts(
            req.user.id, 
            new_scheduled_time, 
            duration_minutes || schedule.duration_minutes
          );

          if (conflicts.length > 0 && req.user.role !== 'admin') {
            return NextResponse.json({
              error: 'Schedule conflict at new time',
              conflicts: conflicts
            }, { status: 409 });
          }

          // Update schedule
          const rescheduleStmt = db.db.prepare(`
            UPDATE queue_schedules 
            SET scheduled_time = ?, duration_minutes = COALESCE(?, duration_minutes)
            WHERE id = ?
          `);
          result = rescheduleStmt.run(new_scheduled_time, duration_minutes, schedule_id);

          // Update associated jobs/batches
          if (schedule.job_id) {
            const updateJobStmt = db.db.prepare(`
              UPDATE queue_jobs 
              SET scheduled_time = ?, effective_priority = ?
              WHERE id = ?
            `);
            const newEffectivePriority = db.calculateEffectivePriority(
              req.licenseFeatures.priority_level, 
              new_scheduled_time
            );
            updateJobStmt.run(new_scheduled_time, newEffectivePriority, schedule.job_id);
          }

          if (schedule.batch_id) {
            const updateBatchStmt = db.db.prepare(`
              UPDATE queue_batches 
              SET scheduled_time = ?
              WHERE id = ?
            `);
            updateBatchStmt.run(new_scheduled_time, schedule.batch_id);
          }

          await webhook.sendQueueAlert(
            'info',
            'Schedule rescheduled',
            {
              schedule_id,
              user: req.user.username,
              old_time: schedule.scheduled_time,
              new_time: new_scheduled_time
            }
          );
          break;

        default:
          return NextResponse.json({
            error: 'Invalid action',
            valid_actions: ['cancel', 'reschedule']
          }, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        action,
        schedule_id,
        message: `Schedule ${action} completed successfully`
      });

    } catch (error) {
      console.error('Schedule update error:', error);
      return NextResponse.json({
        error: 'Schedule update failed',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures,
    QueueMiddleware.logQueueActivity
  ]);

  return handler(request);
}

// DELETE - Delete schedule
export async function DELETE(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const db = getDatabase();
      const url = new URL(request.url);
      const scheduleId = url.searchParams.get('schedule_id');

      if (!scheduleId) {
        return NextResponse.json({
          error: 'schedule_id parameter is required'
        }, { status: 400 });
      }

      // Verify ownership or admin privileges
      const schedules = db.getUserSchedules(req.user.id);
      const schedule = schedules.find(s => s.id === parseInt(scheduleId));

      if (!schedule && req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Schedule not found or access denied'
        }, { status: 404 });
      }

      // Delete schedule
      const deleteStmt = db.db.prepare('DELETE FROM queue_schedules WHERE id = ?');
      deleteStmt.run(scheduleId);

      // Log activity
      db.logActivity(req.user.id, 'SCHEDULE_DELETED', `Deleted schedule ${scheduleId}`);

      return NextResponse.json({
        success: true,
        message: 'Schedule deleted successfully'
      });

    } catch (error) {
      console.error('Schedule deletion error:', error);
      return NextResponse.json({
        error: 'Schedule deletion failed',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures,
    QueueMiddleware.logQueueActivity
  ]);

  return handler(request);
}