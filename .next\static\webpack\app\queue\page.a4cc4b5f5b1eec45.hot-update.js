"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/queue/page",{

/***/ "(app-pages-browser)/./app/queue/components/ScheduleCalendar.jsx":
/*!***************************************************!*\
  !*** ./app/queue/components/ScheduleCalendar.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ScheduleCalendar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nfunction ScheduleCalendar(param) {\n    let { schedules, onScheduleSelect, onCreateSchedule } = param;\n    _s();\n    const [currentDate, setCurrentDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [view, setView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"month\"); // month, week, day\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get calendar data based on current view\n    const getCalendarData = ()=>{\n        const year = currentDate.getFullYear();\n        const month = currentDate.getMonth();\n        if (view === \"month\") {\n            return getMonthData(year, month);\n        } else if (view === \"week\") {\n            return getWeekData(currentDate);\n        } else {\n            return getDayData(currentDate);\n        }\n    };\n    const getMonthData = (year, month)=>{\n        const firstDay = new Date(year, month, 1);\n        const lastDay = new Date(year, month + 1, 0);\n        const startDate = new Date(firstDay);\n        startDate.setDate(startDate.getDate() - firstDay.getDay());\n        const days = [];\n        const current = new Date(startDate);\n        for(let i = 0; i < 42; i++){\n            days.push(new Date(current));\n            current.setDate(current.getDate() + 1);\n        }\n        return days;\n    };\n    const getWeekData = (date)=>{\n        const startOfWeek = new Date(date);\n        startOfWeek.setDate(date.getDate() - date.getDay());\n        const days = [];\n        for(let i = 0; i < 7; i++){\n            const day = new Date(startOfWeek);\n            day.setDate(startOfWeek.getDate() + i);\n            days.push(day);\n        }\n        return days;\n    };\n    const getDayData = (date)=>{\n        return [\n            new Date(date)\n        ];\n    };\n    const getSchedulesForDate = (date)=>{\n        const dateStr = date.toDateString();\n        return schedules.filter((schedule)=>{\n            const scheduleDate = new Date(schedule.start).toDateString();\n            return scheduleDate === dateStr;\n        });\n    };\n    const navigateCalendar = (direction)=>{\n        const newDate = new Date(currentDate);\n        if (view === \"month\") {\n            newDate.setMonth(currentDate.getMonth() + direction);\n        } else if (view === \"week\") {\n            newDate.setDate(currentDate.getDate() + direction * 7);\n        } else {\n            newDate.setDate(currentDate.getDate() + direction);\n        }\n        setCurrentDate(newDate);\n    };\n    const formatDateHeader = ()=>{\n        if (view === \"month\") {\n            return currentDate.toLocaleDateString(\"en-US\", {\n                month: \"long\",\n                year: \"numeric\"\n            });\n        } else if (view === \"week\") {\n            const weekData = getWeekData(currentDate);\n            const start = weekData[0].toLocaleDateString(\"en-US\", {\n                month: \"short\",\n                day: \"numeric\"\n            });\n            const end = weekData[6].toLocaleDateString(\"en-US\", {\n                month: \"short\",\n                day: \"numeric\"\n            });\n            return \"\".concat(start, \" - \").concat(end, \", \").concat(currentDate.getFullYear());\n        } else {\n            return currentDate.toLocaleDateString(\"en-US\", {\n                weekday: \"long\",\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\"\n            });\n        }\n    };\n    const handleDateClick = (date)=>{\n        setSelectedDate(date);\n        if (onScheduleSelect) {\n            onScheduleSelect(date);\n        }\n    };\n    const handleCreateSchedule = (date)=>{\n        setSelectedDate(date);\n        setShowCreateModal(true);\n    };\n    const calendarData = getCalendarData();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: \"Schedule Calendar\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        \"month\",\n                                        \"week\",\n                                        \"day\"\n                                    ].map((viewType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setView(viewType),\n                                            className: \"px-3 py-1 text-sm rounded \".concat(view === viewType ? \"bg-blue-100 text-blue-700\" : \"text-gray-500 hover:text-gray-700\"),\n                                            children: viewType.charAt(0).toUpperCase() + viewType.slice(1)\n                                        }, viewType, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateCalendar(-1),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                            children: \"←\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-medium text-gray-900 min-w-[200px] text-center\",\n                                            children: formatDateHeader()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigateCalendar(1),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentDate(new Date()),\n                                    className: \"px-3 py-1 text-sm text-blue-600 border border-blue-300 rounded hover:bg-blue-50\",\n                                    children: \"Today\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    view === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 gap-1\",\n                        children: [\n                            [\n                                \"Sun\",\n                                \"Mon\",\n                                \"Tue\",\n                                \"Wed\",\n                                \"Thu\",\n                                \"Fri\",\n                                \"Sat\"\n                            ].map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 text-center text-sm font-medium text-gray-500\",\n                                    children: day\n                                }, day, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this)),\n                            calendarData.map((date, index)=>{\n                                const daySchedules = getSchedulesForDate(date);\n                                const isCurrentMonth = date.getMonth() === currentDate.getMonth();\n                                const isToday = date.toDateString() === new Date().toDateString();\n                                const isSelected = selectedDate && date.toDateString() === selectedDate.toDateString();\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>handleDateClick(date),\n                                    className: \"min-h-[100px] p-2 border border-gray-200 cursor-pointer hover:bg-gray-50 \".concat(!isCurrentMonth ? \"bg-gray-50 text-gray-400\" : \"\", \" \").concat(isToday ? \"bg-blue-50 border-blue-200\" : \"\", \" \").concat(isSelected ? \"ring-2 ring-blue-500\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm \".concat(isToday ? \"font-bold text-blue-600\" : \"\"),\n                                                    children: date.getDate()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isCurrentMonth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleCreateSchedule(date);\n                                                    },\n                                                    className: \"text-xs text-blue-600 hover:text-blue-800\",\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 space-y-1\",\n                                            children: [\n                                                daySchedules.slice(0, 3).map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            onScheduleSelect && onScheduleSelect(schedule);\n                                                        },\n                                                        className: \"text-xs p-1 rounded truncate \".concat(schedule.status === \"completed\" ? \"bg-green-100 text-green-800\" : schedule.status === \"active\" ? \"bg-blue-100 text-blue-800\" : schedule.status === \"cancelled\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                        children: schedule.title\n                                                    }, schedule.id, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 23\n                                                    }, this)),\n                                                daySchedules.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        daySchedules.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    view === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 gap-1\",\n                        children: calendarData.map((date, index)=>{\n                            const daySchedules = getSchedulesForDate(date);\n                            const isToday = date.toDateString() === new Date().toDateString();\n                            const isSelected = selectedDate && date.toDateString() === selectedDate.toDateString();\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 text-center border-b \".concat(isToday ? \"bg-blue-50\" : \"bg-gray-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: date.toLocaleDateString(\"en-US\", {\n                                                    weekday: \"short\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg \".concat(isToday ? \"font-bold text-blue-600\" : \"\"),\n                                                children: date.getDate()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 min-h-[300px] space-y-1\",\n                                        children: [\n                                            daySchedules.map((schedule)=>{\n                                                const startTime = new Date(schedule.start).toLocaleTimeString(\"en-US\", {\n                                                    hour: \"numeric\",\n                                                    minute: \"2-digit\"\n                                                });\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>onScheduleSelect && onScheduleSelect(schedule),\n                                                    className: \"text-xs p-2 rounded cursor-pointer \".concat(schedule.status === \"completed\" ? \"bg-green-100 text-green-800\" : schedule.status === \"active\" ? \"bg-blue-100 text-blue-800\" : schedule.status === \"cancelled\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: startTime\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"truncate\",\n                                                            children: schedule.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, schedule.id, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 25\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleCreateSchedule(date),\n                                                className: \"w-full text-xs text-blue-600 border border-dashed border-blue-300 rounded p-2 hover:bg-blue-50\",\n                                                children: \"+ Add Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                lineNumber: 250,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    view === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: currentDate.toLocaleDateString(\"en-US\", {\n                                        weekday: \"long\",\n                                        month: \"long\",\n                                        day: \"numeric\"\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-200 rounded\",\n                                children: Array.from({\n                                    length: 24\n                                }, (_, hour)=>{\n                                    const timeSlot = new Date(currentDate);\n                                    timeSlot.setHours(hour, 0, 0, 0);\n                                    const hourSchedules = schedules.filter((schedule)=>{\n                                        const scheduleHour = new Date(schedule.start).getHours();\n                                        const scheduleDate = new Date(schedule.start).toDateString();\n                                        return scheduleHour === hour && scheduleDate === currentDate.toDateString();\n                                    });\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 p-2 text-sm text-gray-500 border-r\",\n                                                children: hour === 0 ? \"12 AM\" : hour < 12 ? \"\".concat(hour, \" AM\") : hour === 12 ? \"12 PM\" : \"\".concat(hour - 12, \" PM\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-2 min-h-[60px]\",\n                                                children: [\n                                                    hourSchedules.map((schedule)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>onScheduleSelect && onScheduleSelect(schedule),\n                                                            className: \"p-2 rounded mb-1 cursor-pointer \".concat(schedule.status === \"completed\" ? \"bg-green-100 text-green-800\" : schedule.status === \"active\" ? \"bg-blue-100 text-blue-800\" : schedule.status === \"cancelled\" ? \"bg-red-100 text-red-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: schedule.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        new Date(schedule.start).toLocaleTimeString(\"en-US\", {\n                                                                            hour: \"numeric\",\n                                                                            minute: \"2-digit\"\n                                                                        }),\n                                                                        \" - \",\n                                                                        new Date(schedule.end).toLocaleTimeString(\"en-US\", {\n                                                                            hour: \"numeric\",\n                                                                            minute: \"2-digit\"\n                                                                        })\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, schedule.id, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 25\n                                                        }, this)),\n                                                    hourSchedules.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            const scheduleTime = new Date(currentDate);\n                                                            scheduleTime.setHours(hour, 0, 0, 0);\n                                                            handleCreateSchedule(scheduleTime);\n                                                        },\n                                                        className: \"w-full h-full text-xs text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded\",\n                                                        children: \"+ Add Schedule\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, hour, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            showCreateModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateScheduleModal, {\n                selectedDate: selectedDate,\n                onClose: ()=>setShowCreateModal(false),\n                onCreate: onCreateSchedule\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                lineNumber: 374,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(ScheduleCalendar, \"R0frDt1TiTRwn8fZpzfN3UNxvu0=\");\n_c = ScheduleCalendar;\nfunction CreateScheduleModal(param) {\n    let { selectedDate, onClose, onCreate } = param;\n    _s1();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        start_time: \"\",\n        srp_target: 100,\n        job_type: \"sparx_reader\",\n        login_type: \"normal\",\n        job_data: {\n            school: \"\",\n            email: \"\",\n            password: \"\"\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedDate) {\n            const defaultTime = new Date(selectedDate);\n            defaultTime.setHours(9, 0, 0, 0); // Default to 9 AM\n            setFormData((prev)=>({\n                    ...prev,\n                    start_time: defaultTime.toISOString().slice(0, 16)\n                }));\n        }\n    }, [\n        selectedDate\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (onCreate) {\n            onCreate({\n                scheduled_time: formData.start_time,\n                srp_target: formData.srp_target,\n                job_type: formData.job_type,\n                login_type: formData.login_type,\n                job_data: formData.job_data\n            });\n        }\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"Create Schedule\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.title,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                title: e.target.value\n                                            })),\n                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Start Time\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"datetime-local\",\n                                    value: formData.start_time,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                start_time: e.target.value\n                                            })),\n                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Login Type\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: formData.login_type,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                login_type: e.target.value\n                                            })),\n                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"normal\",\n                                            children: \"\\uD83D\\uDC64 Normal Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"google\",\n                                            children: \"\\uD83D\\uDD0D Google Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"microsoft\",\n                                            children: \"\\uD83C\\uDFE2 Microsoft Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 459,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"SRP Target (max 400)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    min: \"1\",\n                                    max: \"400\",\n                                    value: formData.srp_target,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                srp_target: parseInt(e.target.value) || 1\n                                            })),\n                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    placeholder: \"Enter SRP target (1-400)\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-sm text-gray-500\",\n                                    children: \"Browser will automatically close when this SRP target is reached.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Job Details\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"School\",\n                                    value: formData.job_data.school,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                job_data: {\n                                                    ...prev.job_data,\n                                                    school: e.target.value\n                                                }\n                                            })),\n                                    className: \"block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Email/Username\",\n                                    value: formData.job_data.email,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                job_data: {\n                                                    ...prev.job_data,\n                                                    email: e.target.value\n                                                }\n                                            })),\n                                    className: \"block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    placeholder: \"Password\",\n                                    value: formData.job_data.password,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                job_data: {\n                                                    ...prev.job_data,\n                                                    password: e.target.value\n                                                }\n                                            })),\n                                    className: \"block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                    children: \"Create Schedule\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n                    lineNumber: 436,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n            lineNumber: 425,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\components\\\\ScheduleCalendar.jsx\",\n        lineNumber: 424,\n        columnNumber: 5\n    }, this);\n}\n_s1(CreateScheduleModal, \"ZCKq8oQH3FxDY5nDX6XNHUMMNbs=\");\n_c1 = CreateScheduleModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"ScheduleCalendar\");\n$RefreshReg$(_c1, \"CreateScheduleModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9xdWV1ZS9jb21wb25lbnRzL1NjaGVkdWxlQ2FsZW5kYXIuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU0QztBQUU3QixTQUFTRSxpQkFBaUIsS0FBaUQ7UUFBakQsRUFBRUMsU0FBUyxFQUFFQyxnQkFBZ0IsRUFBRUMsZ0JBQWdCLEVBQUUsR0FBakQ7O0lBQ3ZDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHUCwrQ0FBUUEsQ0FBQyxJQUFJUTtJQUNuRCxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1YsK0NBQVFBLENBQUMsVUFBVSxtQkFBbUI7SUFDOUQsTUFBTSxDQUFDVyxjQUFjQyxnQkFBZ0IsR0FBR1osK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDYSxpQkFBaUJDLG1CQUFtQixHQUFHZCwrQ0FBUUEsQ0FBQztJQUV2RCwwQ0FBMEM7SUFDMUMsTUFBTWUsa0JBQWtCO1FBQ3RCLE1BQU1DLE9BQU9WLFlBQVlXLFdBQVc7UUFDcEMsTUFBTUMsUUFBUVosWUFBWWEsUUFBUTtRQUVsQyxJQUFJVixTQUFTLFNBQVM7WUFDcEIsT0FBT1csYUFBYUosTUFBTUU7UUFDNUIsT0FBTyxJQUFJVCxTQUFTLFFBQVE7WUFDMUIsT0FBT1ksWUFBWWY7UUFDckIsT0FBTztZQUNMLE9BQU9nQixXQUFXaEI7UUFDcEI7SUFDRjtJQUVBLE1BQU1jLGVBQWUsQ0FBQ0osTUFBTUU7UUFDMUIsTUFBTUssV0FBVyxJQUFJZixLQUFLUSxNQUFNRSxPQUFPO1FBQ3ZDLE1BQU1NLFVBQVUsSUFBSWhCLEtBQUtRLE1BQU1FLFFBQVEsR0FBRztRQUMxQyxNQUFNTyxZQUFZLElBQUlqQixLQUFLZTtRQUMzQkUsVUFBVUMsT0FBTyxDQUFDRCxVQUFVRSxPQUFPLEtBQUtKLFNBQVNLLE1BQU07UUFFdkQsTUFBTUMsT0FBTyxFQUFFO1FBQ2YsTUFBTUMsVUFBVSxJQUFJdEIsS0FBS2lCO1FBRXpCLElBQUssSUFBSU0sSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7WUFDM0JGLEtBQUtHLElBQUksQ0FBQyxJQUFJeEIsS0FBS3NCO1lBQ25CQSxRQUFRSixPQUFPLENBQUNJLFFBQVFILE9BQU8sS0FBSztRQUN0QztRQUVBLE9BQU9FO0lBQ1Q7SUFFQSxNQUFNUixjQUFjLENBQUNZO1FBQ25CLE1BQU1DLGNBQWMsSUFBSTFCLEtBQUt5QjtRQUM3QkMsWUFBWVIsT0FBTyxDQUFDTyxLQUFLTixPQUFPLEtBQUtNLEtBQUtMLE1BQU07UUFFaEQsTUFBTUMsT0FBTyxFQUFFO1FBQ2YsSUFBSyxJQUFJRSxJQUFJLEdBQUdBLElBQUksR0FBR0EsSUFBSztZQUMxQixNQUFNSSxNQUFNLElBQUkzQixLQUFLMEI7WUFDckJDLElBQUlULE9BQU8sQ0FBQ1EsWUFBWVAsT0FBTyxLQUFLSTtZQUNwQ0YsS0FBS0csSUFBSSxDQUFDRztRQUNaO1FBRUEsT0FBT047SUFDVDtJQUVBLE1BQU1QLGFBQWEsQ0FBQ1c7UUFDbEIsT0FBTztZQUFDLElBQUl6QixLQUFLeUI7U0FBTTtJQUN6QjtJQUVBLE1BQU1HLHNCQUFzQixDQUFDSDtRQUMzQixNQUFNSSxVQUFVSixLQUFLSyxZQUFZO1FBQ2pDLE9BQU9uQyxVQUFVb0MsTUFBTSxDQUFDQyxDQUFBQTtZQUN0QixNQUFNQyxlQUFlLElBQUlqQyxLQUFLZ0MsU0FBU0UsS0FBSyxFQUFFSixZQUFZO1lBQzFELE9BQU9HLGlCQUFpQko7UUFDMUI7SUFDRjtJQUVBLE1BQU1NLG1CQUFtQixDQUFDQztRQUN4QixNQUFNQyxVQUFVLElBQUlyQyxLQUFLRjtRQUV6QixJQUFJRyxTQUFTLFNBQVM7WUFDcEJvQyxRQUFRQyxRQUFRLENBQUN4QyxZQUFZYSxRQUFRLEtBQUt5QjtRQUM1QyxPQUFPLElBQUluQyxTQUFTLFFBQVE7WUFDMUJvQyxRQUFRbkIsT0FBTyxDQUFDcEIsWUFBWXFCLE9BQU8sS0FBTWlCLFlBQVk7UUFDdkQsT0FBTztZQUNMQyxRQUFRbkIsT0FBTyxDQUFDcEIsWUFBWXFCLE9BQU8sS0FBS2lCO1FBQzFDO1FBRUFyQyxlQUFlc0M7SUFDakI7SUFFQSxNQUFNRSxtQkFBbUI7UUFDdkIsSUFBSXRDLFNBQVMsU0FBUztZQUNwQixPQUFPSCxZQUFZMEMsa0JBQWtCLENBQUMsU0FBUztnQkFBRTlCLE9BQU87Z0JBQVFGLE1BQU07WUFBVTtRQUNsRixPQUFPLElBQUlQLFNBQVMsUUFBUTtZQUMxQixNQUFNd0MsV0FBVzVCLFlBQVlmO1lBQzdCLE1BQU1vQyxRQUFRTyxRQUFRLENBQUMsRUFBRSxDQUFDRCxrQkFBa0IsQ0FBQyxTQUFTO2dCQUFFOUIsT0FBTztnQkFBU2lCLEtBQUs7WUFBVTtZQUN2RixNQUFNZSxNQUFNRCxRQUFRLENBQUMsRUFBRSxDQUFDRCxrQkFBa0IsQ0FBQyxTQUFTO2dCQUFFOUIsT0FBTztnQkFBU2lCLEtBQUs7WUFBVTtZQUNyRixPQUFPLEdBQWNlLE9BQVhSLE9BQU0sT0FBYXBDLE9BQVI0QyxLQUFJLE1BQThCLE9BQTFCNUMsWUFBWVcsV0FBVztRQUN0RCxPQUFPO1lBQ0wsT0FBT1gsWUFBWTBDLGtCQUFrQixDQUFDLFNBQVM7Z0JBQzdDRyxTQUFTO2dCQUNUbkMsTUFBTTtnQkFDTkUsT0FBTztnQkFDUGlCLEtBQUs7WUFDUDtRQUNGO0lBQ0Y7SUFFQSxNQUFNaUIsa0JBQWtCLENBQUNuQjtRQUN2QnJCLGdCQUFnQnFCO1FBQ2hCLElBQUk3QixrQkFBa0I7WUFDcEJBLGlCQUFpQjZCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNb0IsdUJBQXVCLENBQUNwQjtRQUM1QnJCLGdCQUFnQnFCO1FBQ2hCbkIsbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTXdDLGVBQWV2QztJQUVyQixxQkFDRSw4REFBQ3dDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFHRCxXQUFVOzhDQUFvQzs7Ozs7OzhDQUNsRCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1o7d0NBQUM7d0NBQVM7d0NBQVE7cUNBQU0sQ0FBQ0UsR0FBRyxDQUFDLENBQUNDLHlCQUM3Qiw4REFBQ0M7NENBRUNDLFNBQVMsSUFBTW5ELFFBQVFpRDs0Q0FDdkJILFdBQVcsNkJBSVYsT0FIQy9DLFNBQVNrRCxXQUNMLDhCQUNBO3NEQUdMQSxTQUFTRyxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLSixTQUFTSyxLQUFLLENBQUM7MkNBUjlDTDs7Ozs7Ozs7Ozs7Ozs7OztzQ0FjYiw4REFBQ0o7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNJOzRDQUNDQyxTQUFTLElBQU1sQixpQkFBaUIsQ0FBQzs0Q0FDakNhLFdBQVU7c0RBQ1g7Ozs7OztzREFHRCw4REFBQ1M7NENBQUtULFdBQVU7c0RBQ2JUOzs7Ozs7c0RBRUgsOERBQUNhOzRDQUNDQyxTQUFTLElBQU1sQixpQkFBaUI7NENBQ2hDYSxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7OENBS0gsOERBQUNJO29DQUNDQyxTQUFTLElBQU10RCxlQUFlLElBQUlDO29DQUNsQ2dELFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFQLDhEQUFDRDtnQkFBSUMsV0FBVTs7b0JBQ1ovQyxTQUFTLHlCQUNSLDhEQUFDOEM7d0JBQUlDLFdBQVU7OzRCQUVaO2dDQUFDO2dDQUFPO2dDQUFPO2dDQUFPO2dDQUFPO2dDQUFPO2dDQUFPOzZCQUFNLENBQUNFLEdBQUcsQ0FBQyxDQUFDdkIsb0JBQ3RELDhEQUFDb0I7b0NBQWNDLFdBQVU7OENBQ3RCckI7bUNBRE9BOzs7Ozs0QkFNWG1CLGFBQWFJLEdBQUcsQ0FBQyxDQUFDekIsTUFBTWlDO2dDQUN2QixNQUFNQyxlQUFlL0Isb0JBQW9CSDtnQ0FDekMsTUFBTW1DLGlCQUFpQm5DLEtBQUtkLFFBQVEsT0FBT2IsWUFBWWEsUUFBUTtnQ0FDL0QsTUFBTWtELFVBQVVwQyxLQUFLSyxZQUFZLE9BQU8sSUFBSTlCLE9BQU84QixZQUFZO2dDQUMvRCxNQUFNZ0MsYUFBYTNELGdCQUFnQnNCLEtBQUtLLFlBQVksT0FBTzNCLGFBQWEyQixZQUFZO2dDQUVwRixxQkFDRSw4REFBQ2lCO29DQUVDTSxTQUFTLElBQU1ULGdCQUFnQm5CO29DQUMvQnVCLFdBQVcsNEVBRVBhLE9BREYsQ0FBQ0QsaUJBQWlCLDZCQUE2QixJQUNoRCxLQUNDRSxPQURFRCxVQUFVLCtCQUErQixJQUFHLEtBRS9DLE9BRENDLGFBQWEseUJBQXlCOztzREFHeEMsOERBQUNmOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ1M7b0RBQUtULFdBQVcsV0FBb0QsT0FBekNhLFVBQVUsNEJBQTRCOzhEQUMvRHBDLEtBQUtOLE9BQU87Ozs7OztnREFFZHlDLGdDQUNDLDhEQUFDUjtvREFDQ0MsU0FBUyxDQUFDVTt3REFDUkEsRUFBRUMsZUFBZTt3REFDakJuQixxQkFBcUJwQjtvREFDdkI7b0RBQ0F1QixXQUFVOzhEQUNYOzs7Ozs7Ozs7Ozs7c0RBTUwsOERBQUNEOzRDQUFJQyxXQUFVOztnREFDWlcsYUFBYUgsS0FBSyxDQUFDLEdBQUcsR0FBR04sR0FBRyxDQUFDLENBQUNsQix5QkFDN0IsOERBQUNlO3dEQUVDTSxTQUFTLENBQUNVOzREQUNSQSxFQUFFQyxlQUFlOzREQUNqQnBFLG9CQUFvQkEsaUJBQWlCb0M7d0RBQ3ZDO3dEQUNBZ0IsV0FBVyxnQ0FLVixPQUpDaEIsU0FBU2lDLE1BQU0sS0FBSyxjQUFjLGdDQUNsQ2pDLFNBQVNpQyxNQUFNLEtBQUssV0FBVyw4QkFDL0JqQyxTQUFTaUMsTUFBTSxLQUFLLGNBQWMsNEJBQ2xDO2tFQUdEakMsU0FBU2tDLEtBQUs7dURBWlZsQyxTQUFTbUMsRUFBRTs7Ozs7Z0RBZW5CUixhQUFhUyxNQUFNLEdBQUcsbUJBQ3JCLDhEQUFDckI7b0RBQUlDLFdBQVU7O3dEQUF3Qjt3REFDbkNXLGFBQWFTLE1BQU0sR0FBRzt3REFBRTs7Ozs7Ozs7Ozs7Ozs7bUNBN0MzQlY7Ozs7OzRCQW1EWDs7Ozs7OztvQkFJSHpELFNBQVMsd0JBQ1IsOERBQUM4Qzt3QkFBSUMsV0FBVTtrQ0FDWkYsYUFBYUksR0FBRyxDQUFDLENBQUN6QixNQUFNaUM7NEJBQ3ZCLE1BQU1DLGVBQWUvQixvQkFBb0JIOzRCQUN6QyxNQUFNb0MsVUFBVXBDLEtBQUtLLFlBQVksT0FBTyxJQUFJOUIsT0FBTzhCLFlBQVk7NEJBQy9ELE1BQU1nQyxhQUFhM0QsZ0JBQWdCc0IsS0FBS0ssWUFBWSxPQUFPM0IsYUFBYTJCLFlBQVk7NEJBRXBGLHFCQUNFLDhEQUFDaUI7Z0NBQWdCQyxXQUFVOztrREFDekIsOERBQUNEO3dDQUFJQyxXQUFXLDRCQUFrRSxPQUF0Q2EsVUFBVSxlQUFlOzswREFDbkUsOERBQUNkO2dEQUFJQyxXQUFVOzBEQUNadkIsS0FBS2Usa0JBQWtCLENBQUMsU0FBUztvREFBRUcsU0FBUztnREFBUTs7Ozs7OzBEQUV2RCw4REFBQ0k7Z0RBQUlDLFdBQVcsV0FBb0QsT0FBekNhLFVBQVUsNEJBQTRCOzBEQUM5RHBDLEtBQUtOLE9BQU87Ozs7Ozs7Ozs7OztrREFJakIsOERBQUM0Qjt3Q0FBSUMsV0FBVTs7NENBQ1pXLGFBQWFULEdBQUcsQ0FBQyxDQUFDbEI7Z0RBQ2pCLE1BQU1xQyxZQUFZLElBQUlyRSxLQUFLZ0MsU0FBU0UsS0FBSyxFQUFFb0Msa0JBQWtCLENBQUMsU0FBUztvREFDckVDLE1BQU07b0RBQ05DLFFBQVE7Z0RBQ1Y7Z0RBRUEscUJBQ0UsOERBQUN6QjtvREFFQ00sU0FBUyxJQUFNekQsb0JBQW9CQSxpQkFBaUJvQztvREFDcERnQixXQUFXLHNDQUtWLE9BSkNoQixTQUFTaUMsTUFBTSxLQUFLLGNBQWMsZ0NBQ2xDakMsU0FBU2lDLE1BQU0sS0FBSyxXQUFXLDhCQUMvQmpDLFNBQVNpQyxNQUFNLEtBQUssY0FBYyw0QkFDbEM7O3NFQUdGLDhEQUFDbEI7NERBQUlDLFdBQVU7c0VBQWVxQjs7Ozs7O3NFQUM5Qiw4REFBQ3RCOzREQUFJQyxXQUFVO3NFQUFZaEIsU0FBU2tDLEtBQUs7Ozs7Ozs7bURBVnBDbEMsU0FBU21DLEVBQUU7Ozs7OzRDQWF0QjswREFFQSw4REFBQ2Y7Z0RBQ0NDLFNBQVMsSUFBTVIscUJBQXFCcEI7Z0RBQ3BDdUIsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7OzsrQkFyQ0tVOzs7Ozt3QkEyQ2Q7Ozs7OztvQkFJSHpELFNBQVMsdUJBQ1IsOERBQUM4Qzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDeUI7b0NBQUd6QixXQUFVOzhDQUNYbEQsWUFBWTBDLGtCQUFrQixDQUFDLFNBQVM7d0NBQ3ZDRyxTQUFTO3dDQUNUakMsT0FBTzt3Q0FDUGlCLEtBQUs7b0NBQ1A7Ozs7Ozs7Ozs7OzBDQUlKLDhEQUFDb0I7Z0NBQUlDLFdBQVU7MENBRVowQixNQUFNQyxJQUFJLENBQUM7b0NBQUVQLFFBQVE7Z0NBQUcsR0FBRyxDQUFDUSxHQUFHTDtvQ0FDOUIsTUFBTU0sV0FBVyxJQUFJN0UsS0FBS0Y7b0NBQzFCK0UsU0FBU0MsUUFBUSxDQUFDUCxNQUFNLEdBQUcsR0FBRztvQ0FFOUIsTUFBTVEsZ0JBQWdCcEYsVUFBVW9DLE1BQU0sQ0FBQ0MsQ0FBQUE7d0NBQ3JDLE1BQU1nRCxlQUFlLElBQUloRixLQUFLZ0MsU0FBU0UsS0FBSyxFQUFFK0MsUUFBUTt3Q0FDdEQsTUFBTWhELGVBQWUsSUFBSWpDLEtBQUtnQyxTQUFTRSxLQUFLLEVBQUVKLFlBQVk7d0NBQzFELE9BQU9rRCxpQkFBaUJULFFBQVF0QyxpQkFBaUJuQyxZQUFZZ0MsWUFBWTtvQ0FDM0U7b0NBRUEscUJBQ0UsOERBQUNpQjt3Q0FBZUMsV0FBVTs7MERBQ3hCLDhEQUFDRDtnREFBSUMsV0FBVTswREFDWnVCLFNBQVMsSUFBSSxVQUFVQSxPQUFPLEtBQUssR0FBUSxPQUFMQSxNQUFLLFNBQU9BLFNBQVMsS0FBSyxVQUFVLEdBQWEsT0FBVkEsT0FBTyxJQUFHOzs7Ozs7MERBRTFGLDhEQUFDeEI7Z0RBQUlDLFdBQVU7O29EQUNaK0IsY0FBYzdCLEdBQUcsQ0FBQyxDQUFDbEIseUJBQ2xCLDhEQUFDZTs0REFFQ00sU0FBUyxJQUFNekQsb0JBQW9CQSxpQkFBaUJvQzs0REFDcERnQixXQUFXLG1DQUtWLE9BSkNoQixTQUFTaUMsTUFBTSxLQUFLLGNBQWMsZ0NBQ2xDakMsU0FBU2lDLE1BQU0sS0FBSyxXQUFXLDhCQUMvQmpDLFNBQVNpQyxNQUFNLEtBQUssY0FBYyw0QkFDbEM7OzhFQUdGLDhEQUFDbEI7b0VBQUlDLFdBQVU7OEVBQWVoQixTQUFTa0MsS0FBSzs7Ozs7OzhFQUM1Qyw4REFBQ25CO29FQUFJQyxXQUFVOzt3RUFDWixJQUFJaEQsS0FBS2dDLFNBQVNFLEtBQUssRUFBRW9DLGtCQUFrQixDQUFDLFNBQVM7NEVBQ3BEQyxNQUFNOzRFQUNOQyxRQUFRO3dFQUNWO3dFQUFHO3dFQUFJLElBQUl4RSxLQUFLZ0MsU0FBU1UsR0FBRyxFQUFFNEIsa0JBQWtCLENBQUMsU0FBUzs0RUFDeERDLE1BQU07NEVBQ05DLFFBQVE7d0VBQ1Y7Ozs7Ozs7OzJEQWpCR3hDLFNBQVNtQyxFQUFFOzs7OztvREFzQm5CWSxjQUFjWCxNQUFNLEtBQUssbUJBQ3hCLDhEQUFDaEI7d0RBQ0NDLFNBQVM7NERBQ1AsTUFBTTZCLGVBQWUsSUFBSWxGLEtBQUtGOzREQUM5Qm9GLGFBQWFKLFFBQVEsQ0FBQ1AsTUFBTSxHQUFHLEdBQUc7NERBQ2xDMUIscUJBQXFCcUM7d0RBQ3ZCO3dEQUNBbEMsV0FBVTtrRUFDWDs7Ozs7Ozs7Ozs7Ozt1Q0FyQ0d1Qjs7Ozs7Z0NBNENkOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPUGxFLGlDQUNDLDhEQUFDOEU7Z0JBQ0NoRixjQUFjQTtnQkFDZGlGLFNBQVMsSUFBTTlFLG1CQUFtQjtnQkFDbEMrRSxVQUFVeEY7Ozs7Ozs7Ozs7OztBQUtwQjtHQXpYd0JIO0tBQUFBO0FBMlh4QixTQUFTeUYsb0JBQW9CLEtBQW1DO1FBQW5DLEVBQUVoRixZQUFZLEVBQUVpRixPQUFPLEVBQUVDLFFBQVEsRUFBRSxHQUFuQzs7SUFDM0IsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUcvRiwrQ0FBUUEsQ0FBQztRQUN2QzBFLE9BQU87UUFDUHNCLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsVUFBVTtZQUNSQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsVUFBVTtRQUNaO0lBQ0Y7SUFFQXRHLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSVUsY0FBYztZQUNoQixNQUFNNkYsY0FBYyxJQUFJaEcsS0FBS0c7WUFDN0I2RixZQUFZbEIsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHLElBQUksa0JBQWtCO1lBQ3BEUyxZQUFZVSxDQUFBQSxPQUFTO29CQUNuQixHQUFHQSxJQUFJO29CQUNQVCxZQUFZUSxZQUFZRSxXQUFXLEdBQUcxQyxLQUFLLENBQUMsR0FBRztnQkFDakQ7UUFDRjtJQUNGLEdBQUc7UUFBQ3JEO0tBQWE7SUFFakIsTUFBTWdHLGVBQWUsQ0FBQ3BDO1FBQ3BCQSxFQUFFcUMsY0FBYztRQUNoQixJQUFJZixVQUFVO1lBQ1pBLFNBQVM7Z0JBQ1BnQixnQkFBZ0JmLFNBQVNFLFVBQVU7Z0JBQ25DQyxZQUFZSCxTQUFTRyxVQUFVO2dCQUMvQkMsVUFBVUosU0FBU0ksUUFBUTtnQkFDM0JDLFlBQVlMLFNBQVNLLFVBQVU7Z0JBQy9CQyxVQUFVTixTQUFTTSxRQUFRO1lBQzdCO1FBQ0Y7UUFDQVI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDckM7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQW9DOzs7Ozs7c0NBQ2xELDhEQUFDSTs0QkFDQ0MsU0FBUytCOzRCQUNUcEMsV0FBVTtzQ0FDWDs7Ozs7Ozs7Ozs7OzhCQUtILDhEQUFDc0Q7b0JBQUtDLFVBQVVKO29CQUFjbkQsV0FBVTs7c0NBQ3RDLDhEQUFDRDs7OENBQ0MsOERBQUN5RDtvQ0FBTXhELFdBQVU7OENBQTBDOzs7Ozs7OENBQzNELDhEQUFDeUQ7b0NBQ0NDLE1BQUs7b0NBQ0xDLE9BQU9yQixTQUFTcEIsS0FBSztvQ0FDckIwQyxVQUFVLENBQUM3QyxJQUFNd0IsWUFBWVUsQ0FBQUEsT0FBUztnREFBRSxHQUFHQSxJQUFJO2dEQUFFL0IsT0FBT0gsRUFBRThDLE1BQU0sQ0FBQ0YsS0FBSzs0Q0FBQztvQ0FDdkUzRCxXQUFVO29DQUNWOEQsUUFBUTs7Ozs7Ozs7Ozs7O3NDQUlaLDhEQUFDL0Q7OzhDQUNDLDhEQUFDeUQ7b0NBQU14RCxXQUFVOzhDQUEwQzs7Ozs7OzhDQUMzRCw4REFBQ3lEO29DQUNDQyxNQUFLO29DQUNMQyxPQUFPckIsU0FBU0UsVUFBVTtvQ0FDMUJvQixVQUFVLENBQUM3QyxJQUFNd0IsWUFBWVUsQ0FBQUEsT0FBUztnREFBRSxHQUFHQSxJQUFJO2dEQUFFVCxZQUFZekIsRUFBRThDLE1BQU0sQ0FBQ0YsS0FBSzs0Q0FBQztvQ0FDNUUzRCxXQUFVO29DQUNWOEQsUUFBUTs7Ozs7Ozs7Ozs7O3NDQUlaLDhEQUFDL0Q7OzhDQUNDLDhEQUFDeUQ7b0NBQU14RCxXQUFVOzhDQUEwQzs7Ozs7OzhDQUMzRCw4REFBQytEO29DQUNDSixPQUFPckIsU0FBU0ssVUFBVTtvQ0FDMUJpQixVQUFVLENBQUM3QyxJQUFNd0IsWUFBWVUsQ0FBQUEsT0FBUztnREFBRSxHQUFHQSxJQUFJO2dEQUFFTixZQUFZNUIsRUFBRThDLE1BQU0sQ0FBQ0YsS0FBSzs0Q0FBQztvQ0FDNUUzRCxXQUFVO29DQUNWOEQsUUFBUTs7c0RBRVIsOERBQUNFOzRDQUFPTCxPQUFNO3NEQUFTOzs7Ozs7c0RBQ3ZCLDhEQUFDSzs0Q0FBT0wsT0FBTTtzREFBUzs7Ozs7O3NEQUN2Qiw4REFBQ0s7NENBQU9MLE9BQU07c0RBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJOUIsOERBQUM1RDs7OENBQ0MsOERBQUN5RDtvQ0FBTXhELFdBQVU7OENBQTBDOzs7Ozs7OENBQzNELDhEQUFDeUQ7b0NBQ0NDLE1BQUs7b0NBQ0xPLEtBQUk7b0NBQ0pDLEtBQUk7b0NBQ0pQLE9BQU9yQixTQUFTRyxVQUFVO29DQUMxQm1CLFVBQVUsQ0FBQzdDLElBQU13QixZQUFZVSxDQUFBQSxPQUFTO2dEQUFFLEdBQUdBLElBQUk7Z0RBQUVSLFlBQVkwQixTQUFTcEQsRUFBRThDLE1BQU0sQ0FBQ0YsS0FBSyxLQUFLOzRDQUFFO29DQUMzRjNELFdBQVU7b0NBQ1ZvRSxhQUFZO29DQUNaTixRQUFROzs7Ozs7OENBRVYsOERBQUNPO29DQUFFckUsV0FBVTs4Q0FBNkI7Ozs7Ozs7Ozs7OztzQ0FLNUMsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3dEO29DQUFNeEQsV0FBVTs4Q0FBMEM7Ozs7Ozs4Q0FDM0QsOERBQUN5RDtvQ0FDQ0MsTUFBSztvQ0FDTFUsYUFBWTtvQ0FDWlQsT0FBT3JCLFNBQVNNLFFBQVEsQ0FBQ0MsTUFBTTtvQ0FDL0JlLFVBQVUsQ0FBQzdDLElBQU13QixZQUFZVSxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQTCxVQUFVO29EQUFFLEdBQUdLLEtBQUtMLFFBQVE7b0RBQUVDLFFBQVE5QixFQUFFOEMsTUFBTSxDQUFDRixLQUFLO2dEQUFDOzRDQUN2RDtvQ0FDQTNELFdBQVU7b0NBQ1Y4RCxRQUFROzs7Ozs7OENBRVYsOERBQUNMO29DQUNDQyxNQUFLO29DQUNMVSxhQUFZO29DQUNaVCxPQUFPckIsU0FBU00sUUFBUSxDQUFDRSxLQUFLO29DQUM5QmMsVUFBVSxDQUFDN0MsSUFBTXdCLFlBQVlVLENBQUFBLE9BQVM7Z0RBQ3BDLEdBQUdBLElBQUk7Z0RBQ1BMLFVBQVU7b0RBQUUsR0FBR0ssS0FBS0wsUUFBUTtvREFBRUUsT0FBTy9CLEVBQUU4QyxNQUFNLENBQUNGLEtBQUs7Z0RBQUM7NENBQ3REO29DQUNBM0QsV0FBVTtvQ0FDVjhELFFBQVE7Ozs7Ozs4Q0FFViw4REFBQ0w7b0NBQ0NDLE1BQUs7b0NBQ0xVLGFBQVk7b0NBQ1pULE9BQU9yQixTQUFTTSxRQUFRLENBQUNHLFFBQVE7b0NBQ2pDYSxVQUFVLENBQUM3QyxJQUFNd0IsWUFBWVUsQ0FBQUEsT0FBUztnREFDcEMsR0FBR0EsSUFBSTtnREFDUEwsVUFBVTtvREFBRSxHQUFHSyxLQUFLTCxRQUFRO29EQUFFRyxVQUFVaEMsRUFBRThDLE1BQU0sQ0FBQ0YsS0FBSztnREFBQzs0Q0FDekQ7b0NBQ0EzRCxXQUFVO29DQUNWOEQsUUFBUTs7Ozs7Ozs7Ozs7O3NDQUlaLDhEQUFDL0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDSTtvQ0FDQ3NELE1BQUs7b0NBQ0xyRCxTQUFTK0I7b0NBQ1RwQyxXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNJO29DQUNDc0QsTUFBSztvQ0FDTDFELFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7SUFsS1NtQztNQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvcXVldWUvY29tcG9uZW50cy9TY2hlZHVsZUNhbGVuZGFyLmpzeD9kODNlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTY2hlZHVsZUNhbGVuZGFyKHsgc2NoZWR1bGVzLCBvblNjaGVkdWxlU2VsZWN0LCBvbkNyZWF0ZVNjaGVkdWxlIH0pIHtcclxuICBjb25zdCBbY3VycmVudERhdGUsIHNldEN1cnJlbnREYXRlXSA9IHVzZVN0YXRlKG5ldyBEYXRlKCkpO1xyXG4gIGNvbnN0IFt2aWV3LCBzZXRWaWV3XSA9IHVzZVN0YXRlKCdtb250aCcpOyAvLyBtb250aCwgd2VlaywgZGF5XHJcbiAgY29uc3QgW3NlbGVjdGVkRGF0ZSwgc2V0U2VsZWN0ZWREYXRlXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFtzaG93Q3JlYXRlTW9kYWwsIHNldFNob3dDcmVhdGVNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIEdldCBjYWxlbmRhciBkYXRhIGJhc2VkIG9uIGN1cnJlbnQgdmlld1xyXG4gIGNvbnN0IGdldENhbGVuZGFyRGF0YSA9ICgpID0+IHtcclxuICAgIGNvbnN0IHllYXIgPSBjdXJyZW50RGF0ZS5nZXRGdWxsWWVhcigpO1xyXG4gICAgY29uc3QgbW9udGggPSBjdXJyZW50RGF0ZS5nZXRNb250aCgpO1xyXG4gICAgXHJcbiAgICBpZiAodmlldyA9PT0gJ21vbnRoJykge1xyXG4gICAgICByZXR1cm4gZ2V0TW9udGhEYXRhKHllYXIsIG1vbnRoKTtcclxuICAgIH0gZWxzZSBpZiAodmlldyA9PT0gJ3dlZWsnKSB7XHJcbiAgICAgIHJldHVybiBnZXRXZWVrRGF0YShjdXJyZW50RGF0ZSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICByZXR1cm4gZ2V0RGF5RGF0YShjdXJyZW50RGF0ZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0TW9udGhEYXRhID0gKHllYXIsIG1vbnRoKSA9PiB7XHJcbiAgICBjb25zdCBmaXJzdERheSA9IG5ldyBEYXRlKHllYXIsIG1vbnRoLCAxKTtcclxuICAgIGNvbnN0IGxhc3REYXkgPSBuZXcgRGF0ZSh5ZWFyLCBtb250aCArIDEsIDApO1xyXG4gICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUoZmlyc3REYXkpO1xyXG4gICAgc3RhcnREYXRlLnNldERhdGUoc3RhcnREYXRlLmdldERhdGUoKSAtIGZpcnN0RGF5LmdldERheSgpKTtcclxuICAgIFxyXG4gICAgY29uc3QgZGF5cyA9IFtdO1xyXG4gICAgY29uc3QgY3VycmVudCA9IG5ldyBEYXRlKHN0YXJ0RGF0ZSk7XHJcbiAgICBcclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgNDI7IGkrKykgeyAvLyA2IHdlZWtzICogNyBkYXlzXHJcbiAgICAgIGRheXMucHVzaChuZXcgRGF0ZShjdXJyZW50KSk7XHJcbiAgICAgIGN1cnJlbnQuc2V0RGF0ZShjdXJyZW50LmdldERhdGUoKSArIDEpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gZGF5cztcclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRXZWVrRGF0YSA9IChkYXRlKSA9PiB7XHJcbiAgICBjb25zdCBzdGFydE9mV2VlayA9IG5ldyBEYXRlKGRhdGUpO1xyXG4gICAgc3RhcnRPZldlZWsuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGRhdGUuZ2V0RGF5KCkpO1xyXG4gICAgXHJcbiAgICBjb25zdCBkYXlzID0gW107XHJcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IDc7IGkrKykge1xyXG4gICAgICBjb25zdCBkYXkgPSBuZXcgRGF0ZShzdGFydE9mV2Vlayk7XHJcbiAgICAgIGRheS5zZXREYXRlKHN0YXJ0T2ZXZWVrLmdldERhdGUoKSArIGkpO1xyXG4gICAgICBkYXlzLnB1c2goZGF5KTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIGRheXM7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0RGF5RGF0YSA9IChkYXRlKSA9PiB7XHJcbiAgICByZXR1cm4gW25ldyBEYXRlKGRhdGUpXTtcclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRTY2hlZHVsZXNGb3JEYXRlID0gKGRhdGUpID0+IHtcclxuICAgIGNvbnN0IGRhdGVTdHIgPSBkYXRlLnRvRGF0ZVN0cmluZygpO1xyXG4gICAgcmV0dXJuIHNjaGVkdWxlcy5maWx0ZXIoc2NoZWR1bGUgPT4ge1xyXG4gICAgICBjb25zdCBzY2hlZHVsZURhdGUgPSBuZXcgRGF0ZShzY2hlZHVsZS5zdGFydCkudG9EYXRlU3RyaW5nKCk7XHJcbiAgICAgIHJldHVybiBzY2hlZHVsZURhdGUgPT09IGRhdGVTdHI7XHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBuYXZpZ2F0ZUNhbGVuZGFyID0gKGRpcmVjdGlvbikgPT4ge1xyXG4gICAgY29uc3QgbmV3RGF0ZSA9IG5ldyBEYXRlKGN1cnJlbnREYXRlKTtcclxuICAgIFxyXG4gICAgaWYgKHZpZXcgPT09ICdtb250aCcpIHtcclxuICAgICAgbmV3RGF0ZS5zZXRNb250aChjdXJyZW50RGF0ZS5nZXRNb250aCgpICsgZGlyZWN0aW9uKTtcclxuICAgIH0gZWxzZSBpZiAodmlldyA9PT0gJ3dlZWsnKSB7XHJcbiAgICAgIG5ld0RhdGUuc2V0RGF0ZShjdXJyZW50RGF0ZS5nZXREYXRlKCkgKyAoZGlyZWN0aW9uICogNykpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgbmV3RGF0ZS5zZXREYXRlKGN1cnJlbnREYXRlLmdldERhdGUoKSArIGRpcmVjdGlvbik7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHNldEN1cnJlbnREYXRlKG5ld0RhdGUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGZvcm1hdERhdGVIZWFkZXIgPSAoKSA9PiB7XHJcbiAgICBpZiAodmlldyA9PT0gJ21vbnRoJykge1xyXG4gICAgICByZXR1cm4gY3VycmVudERhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgbW9udGg6ICdsb25nJywgeWVhcjogJ251bWVyaWMnIH0pO1xyXG4gICAgfSBlbHNlIGlmICh2aWV3ID09PSAnd2VlaycpIHtcclxuICAgICAgY29uc3Qgd2Vla0RhdGEgPSBnZXRXZWVrRGF0YShjdXJyZW50RGF0ZSk7XHJcbiAgICAgIGNvbnN0IHN0YXJ0ID0gd2Vla0RhdGFbMF0udG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgbW9udGg6ICdzaG9ydCcsIGRheTogJ251bWVyaWMnIH0pO1xyXG4gICAgICBjb25zdCBlbmQgPSB3ZWVrRGF0YVs2XS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywgeyBtb250aDogJ3Nob3J0JywgZGF5OiAnbnVtZXJpYycgfSk7XHJcbiAgICAgIHJldHVybiBgJHtzdGFydH0gLSAke2VuZH0sICR7Y3VycmVudERhdGUuZ2V0RnVsbFllYXIoKX1gO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgcmV0dXJuIGN1cnJlbnREYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7IFxyXG4gICAgICAgIHdlZWtkYXk6ICdsb25nJywgXHJcbiAgICAgICAgeWVhcjogJ251bWVyaWMnLCBcclxuICAgICAgICBtb250aDogJ2xvbmcnLCBcclxuICAgICAgICBkYXk6ICdudW1lcmljJyBcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRGF0ZUNsaWNrID0gKGRhdGUpID0+IHtcclxuICAgIHNldFNlbGVjdGVkRGF0ZShkYXRlKTtcclxuICAgIGlmIChvblNjaGVkdWxlU2VsZWN0KSB7XHJcbiAgICAgIG9uU2NoZWR1bGVTZWxlY3QoZGF0ZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ3JlYXRlU2NoZWR1bGUgPSAoZGF0ZSkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWREYXRlKGRhdGUpO1xyXG4gICAgc2V0U2hvd0NyZWF0ZU1vZGFsKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGNhbGVuZGFyRGF0YSA9IGdldENhbGVuZGFyRGF0YSgpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvd1wiPlxyXG4gICAgICB7LyogQ2FsZW5kYXIgSGVhZGVyICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlNjaGVkdWxlIENhbGVuZGFyPC9oMz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxyXG4gICAgICAgICAgICAgIHtbJ21vbnRoJywgJ3dlZWsnLCAnZGF5J10ubWFwKCh2aWV3VHlwZSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBrZXk9e3ZpZXdUeXBlfVxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRWaWV3KHZpZXdUeXBlKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMyBweS0xIHRleHQtc20gcm91bmRlZCAke1xyXG4gICAgICAgICAgICAgICAgICAgIHZpZXcgPT09IHZpZXdUeXBlXHJcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtNzAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwJ1xyXG4gICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge3ZpZXdUeXBlLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgdmlld1R5cGUuc2xpY2UoMSl9XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cclxuICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZUNhbGVuZGFyKC0xKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIOKGkFxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtaW4tdy1bMjAwcHhdIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZUhlYWRlcigpfVxyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZUNhbGVuZGFyKDEpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMFwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAg4oaSXHJcbiAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnREYXRlKG5ldyBEYXRlKCkpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSB0ZXh0LXNtIHRleHQtYmx1ZS02MDAgYm9yZGVyIGJvcmRlci1ibHVlLTMwMCByb3VuZGVkIGhvdmVyOmJnLWJsdWUtNTBcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgVG9kYXlcclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogQ2FsZW5kYXIgQm9keSAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cclxuICAgICAgICB7dmlldyA9PT0gJ21vbnRoJyAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTcgZ2FwLTFcIj5cclxuICAgICAgICAgICAgey8qIERheSBoZWFkZXJzICovfVxyXG4gICAgICAgICAgICB7WydTdW4nLCAnTW9uJywgJ1R1ZScsICdXZWQnLCAnVGh1JywgJ0ZyaScsICdTYXQnXS5tYXAoKGRheSkgPT4gKFxyXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtkYXl9IGNsYXNzTmFtZT1cInAtMiB0ZXh0LWNlbnRlciB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICAgIHtkYXl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgey8qIENhbGVuZGFyIGRheXMgKi99XHJcbiAgICAgICAgICAgIHtjYWxlbmRhckRhdGEubWFwKChkYXRlLCBpbmRleCkgPT4ge1xyXG4gICAgICAgICAgICAgIGNvbnN0IGRheVNjaGVkdWxlcyA9IGdldFNjaGVkdWxlc0ZvckRhdGUoZGF0ZSk7XHJcbiAgICAgICAgICAgICAgY29uc3QgaXNDdXJyZW50TW9udGggPSBkYXRlLmdldE1vbnRoKCkgPT09IGN1cnJlbnREYXRlLmdldE1vbnRoKCk7XHJcbiAgICAgICAgICAgICAgY29uc3QgaXNUb2RheSA9IGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IG5ldyBEYXRlKCkudG9EYXRlU3RyaW5nKCk7XHJcbiAgICAgICAgICAgICAgY29uc3QgaXNTZWxlY3RlZCA9IHNlbGVjdGVkRGF0ZSAmJiBkYXRlLnRvRGF0ZVN0cmluZygpID09PSBzZWxlY3RlZERhdGUudG9EYXRlU3RyaW5nKCk7XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGF0ZUNsaWNrKGRhdGUpfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BtaW4taC1bMTAwcHhdIHAtMiBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktNTAgJHtcclxuICAgICAgICAgICAgICAgICAgICAhaXNDdXJyZW50TW9udGggPyAnYmctZ3JheS01MCB0ZXh0LWdyYXktNDAwJyA6ICcnXHJcbiAgICAgICAgICAgICAgICAgIH0gJHtpc1RvZGF5ID8gJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMjAwJyA6ICcnfSAke1xyXG4gICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWQgPyAncmluZy0yIHJpbmctYmx1ZS01MDAnIDogJydcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtICR7aXNUb2RheSA/ICdmb250LWJvbGQgdGV4dC1ibHVlLTYwMCcgOiAnJ31gfT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtkYXRlLmdldERhdGUoKX1cclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAge2lzQ3VycmVudE1vbnRoICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNyZWF0ZVNjaGVkdWxlKGRhdGUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICtcclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xIHNwYWNlLXktMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtkYXlTY2hlZHVsZXMuc2xpY2UoMCwgMykubWFwKChzY2hlZHVsZSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3NjaGVkdWxlLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25TY2hlZHVsZVNlbGVjdCAmJiBvblNjaGVkdWxlU2VsZWN0KHNjaGVkdWxlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyBwLTEgcm91bmRlZCB0cnVuY2F0ZSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNjaGVkdWxlLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2NoZWR1bGUuc3RhdHVzID09PSAnYWN0aXZlJyA/ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2NoZWR1bGUuc3RhdHVzID09PSAnY2FuY2VsbGVkJyA/ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtzY2hlZHVsZS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIHtkYXlTY2hlZHVsZXMubGVuZ3RoID4gMyAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAre2RheVNjaGVkdWxlcy5sZW5ndGggLSAzfSBtb3JlXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAge3ZpZXcgPT09ICd3ZWVrJyAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTcgZ2FwLTFcIj5cclxuICAgICAgICAgICAge2NhbGVuZGFyRGF0YS5tYXAoKGRhdGUsIGluZGV4KSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgZGF5U2NoZWR1bGVzID0gZ2V0U2NoZWR1bGVzRm9yRGF0ZShkYXRlKTtcclxuICAgICAgICAgICAgICBjb25zdCBpc1RvZGF5ID0gZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0gbmV3IERhdGUoKS50b0RhdGVTdHJpbmcoKTtcclxuICAgICAgICAgICAgICBjb25zdCBpc1NlbGVjdGVkID0gc2VsZWN0ZWREYXRlICYmIGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHNlbGVjdGVkRGF0ZS50b0RhdGVTdHJpbmcoKTtcclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC0yIHRleHQtY2VudGVyIGJvcmRlci1iICR7aXNUb2RheSA/ICdiZy1ibHVlLTUwJyA6ICdiZy1ncmF5LTUwJ31gfT5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2RhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgd2Vla2RheTogJ3Nob3J0JyB9KX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtbGcgJHtpc1RvZGF5ID8gJ2ZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwJyA6ICcnfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAge2RhdGUuZ2V0RGF0ZSgpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIG1pbi1oLVszMDBweF0gc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2RheVNjaGVkdWxlcy5tYXAoKHNjaGVkdWxlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzdGFydFRpbWUgPSBuZXcgRGF0ZShzY2hlZHVsZS5zdGFydCkudG9Mb2NhbGVUaW1lU3RyaW5nKCdlbi1VUycsIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaG91cjogJ251bWVyaWMnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW51dGU6ICcyLWRpZ2l0J1xyXG4gICAgICAgICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3NjaGVkdWxlLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uU2NoZWR1bGVTZWxlY3QgJiYgb25TY2hlZHVsZVNlbGVjdChzY2hlZHVsZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyBwLTIgcm91bmRlZCBjdXJzb3ItcG9pbnRlciAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NoZWR1bGUuc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjaGVkdWxlLnN0YXR1cyA9PT0gJ2FjdGl2ZScgPyAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NoZWR1bGUuc3RhdHVzID09PSAnY2FuY2VsbGVkJyA/ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntzdGFydFRpbWV9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0cnVuY2F0ZVwiPntzY2hlZHVsZS50aXRsZX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUNyZWF0ZVNjaGVkdWxlKGRhdGUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQteHMgdGV4dC1ibHVlLTYwMCBib3JkZXIgYm9yZGVyLWRhc2hlZCBib3JkZXItYmx1ZS0zMDAgcm91bmRlZCBwLTIgaG92ZXI6YmctYmx1ZS01MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKyBBZGQgU2NoZWR1bGVcclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICB9KX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHt2aWV3ID09PSAnZGF5JyAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxyXG4gICAgICAgICAgICAgICAge2N1cnJlbnREYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7IFxyXG4gICAgICAgICAgICAgICAgICB3ZWVrZGF5OiAnbG9uZycsIFxyXG4gICAgICAgICAgICAgICAgICBtb250aDogJ2xvbmcnLCBcclxuICAgICAgICAgICAgICAgICAgZGF5OiAnbnVtZXJpYycgXHJcbiAgICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICA8L2g0PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkXCI+XHJcbiAgICAgICAgICAgICAgey8qIFRpbWUgc2xvdHMgKi99XHJcbiAgICAgICAgICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IDI0IH0sIChfLCBob3VyKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0aW1lU2xvdCA9IG5ldyBEYXRlKGN1cnJlbnREYXRlKTtcclxuICAgICAgICAgICAgICAgIHRpbWVTbG90LnNldEhvdXJzKGhvdXIsIDAsIDAsIDApO1xyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICBjb25zdCBob3VyU2NoZWR1bGVzID0gc2NoZWR1bGVzLmZpbHRlcihzY2hlZHVsZSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHNjaGVkdWxlSG91ciA9IG5ldyBEYXRlKHNjaGVkdWxlLnN0YXJ0KS5nZXRIb3VycygpO1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCBzY2hlZHVsZURhdGUgPSBuZXcgRGF0ZShzY2hlZHVsZS5zdGFydCkudG9EYXRlU3RyaW5nKCk7XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiBzY2hlZHVsZUhvdXIgPT09IGhvdXIgJiYgc2NoZWR1bGVEYXRlID09PSBjdXJyZW50RGF0ZS50b0RhdGVTdHJpbmcoKTtcclxuICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aG91cn0gY2xhc3NOYW1lPVwiZmxleCBib3JkZXItYiBib3JkZXItZ3JheS0xMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgcC0yIHRleHQtc20gdGV4dC1ncmF5LTUwMCBib3JkZXItclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2hvdXIgPT09IDAgPyAnMTIgQU0nIDogaG91ciA8IDEyID8gYCR7aG91cn0gQU1gIDogaG91ciA9PT0gMTIgPyAnMTIgUE0nIDogYCR7aG91ciAtIDEyfSBQTWB9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC0yIG1pbi1oLVs2MHB4XVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2hvdXJTY2hlZHVsZXMubWFwKChzY2hlZHVsZSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtzY2hlZHVsZS5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvblNjaGVkdWxlU2VsZWN0ICYmIG9uU2NoZWR1bGVTZWxlY3Qoc2NoZWR1bGUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMiByb3VuZGVkIG1iLTEgY3Vyc29yLXBvaW50ZXIgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjaGVkdWxlLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY2hlZHVsZS5zdGF0dXMgPT09ICdhY3RpdmUnID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjaGVkdWxlLnN0YXR1cyA9PT0gJ2NhbmNlbGxlZCcgPyAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57c2NoZWR1bGUudGl0bGV9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoc2NoZWR1bGUuc3RhcnQpLnRvTG9jYWxlVGltZVN0cmluZygnZW4tVVMnLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXI6ICdudW1lcmljJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiAnMi1kaWdpdCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pfSAtIHtuZXcgRGF0ZShzY2hlZHVsZS5lbmQpLnRvTG9jYWxlVGltZVN0cmluZygnZW4tVVMnLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdXI6ICdudW1lcmljJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlOiAnMi1kaWdpdCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAgICAgICB7aG91clNjaGVkdWxlcy5sZW5ndGggPT09IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2NoZWR1bGVUaW1lID0gbmV3IERhdGUoY3VycmVudERhdGUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NoZWR1bGVUaW1lLnNldEhvdXJzKGhvdXIsIDAsIDAsIDApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ3JlYXRlU2NoZWR1bGUoc2NoZWR1bGVUaW1lKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgdGV4dC14cyB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS01MCByb3VuZGVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICsgQWRkIFNjaGVkdWxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIENyZWF0ZSBTY2hlZHVsZSBNb2RhbCAqL31cclxuICAgICAge3Nob3dDcmVhdGVNb2RhbCAmJiAoXHJcbiAgICAgICAgPENyZWF0ZVNjaGVkdWxlTW9kYWxcclxuICAgICAgICAgIHNlbGVjdGVkRGF0ZT17c2VsZWN0ZWREYXRlfVxyXG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0NyZWF0ZU1vZGFsKGZhbHNlKX1cclxuICAgICAgICAgIG9uQ3JlYXRlPXtvbkNyZWF0ZVNjaGVkdWxlfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcblxyXG5mdW5jdGlvbiBDcmVhdGVTY2hlZHVsZU1vZGFsKHsgc2VsZWN0ZWREYXRlLCBvbkNsb3NlLCBvbkNyZWF0ZSB9KSB7XHJcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XHJcbiAgICB0aXRsZTogJycsXHJcbiAgICBzdGFydF90aW1lOiAnJyxcclxuICAgIHNycF90YXJnZXQ6IDEwMCxcclxuICAgIGpvYl90eXBlOiAnc3BhcnhfcmVhZGVyJyxcclxuICAgIGxvZ2luX3R5cGU6ICdub3JtYWwnLFxyXG4gICAgam9iX2RhdGE6IHtcclxuICAgICAgc2Nob29sOiAnJyxcclxuICAgICAgZW1haWw6ICcnLFxyXG4gICAgICBwYXNzd29yZDogJydcclxuICAgIH1cclxuICB9KTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChzZWxlY3RlZERhdGUpIHtcclxuICAgICAgY29uc3QgZGVmYXVsdFRpbWUgPSBuZXcgRGF0ZShzZWxlY3RlZERhdGUpO1xyXG4gICAgICBkZWZhdWx0VGltZS5zZXRIb3Vycyg5LCAwLCAwLCAwKTsgLy8gRGVmYXVsdCB0byA5IEFNXHJcbiAgICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIHN0YXJ0X3RpbWU6IGRlZmF1bHRUaW1lLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTYpXHJcbiAgICAgIH0pKTtcclxuICAgIH1cclxuICB9LCBbc2VsZWN0ZWREYXRlXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChlKSA9PiB7XHJcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICBpZiAob25DcmVhdGUpIHtcclxuICAgICAgb25DcmVhdGUoe1xyXG4gICAgICAgIHNjaGVkdWxlZF90aW1lOiBmb3JtRGF0YS5zdGFydF90aW1lLFxyXG4gICAgICAgIHNycF90YXJnZXQ6IGZvcm1EYXRhLnNycF90YXJnZXQsXHJcbiAgICAgICAgam9iX3R5cGU6IGZvcm1EYXRhLmpvYl90eXBlLFxyXG4gICAgICAgIGxvZ2luX3R5cGU6IGZvcm1EYXRhLmxvZ2luX3R5cGUsXHJcbiAgICAgICAgam9iX2RhdGE6IGZvcm1EYXRhLmpvYl9kYXRhXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgb25DbG9zZSgpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBwLTYgdy1mdWxsIG1heC13LW1kXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItNFwiPlxyXG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPkNyZWF0ZSBTY2hlZHVsZTwvaDM+XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMFwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIMOXXHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+VGl0bGU8L2xhYmVsPlxyXG4gICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRpdGxlfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCB0aXRsZTogZS50YXJnZXQudmFsdWUgfSkpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTIgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcclxuICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlN0YXJ0IFRpbWU8L2xhYmVsPlxyXG4gICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICB0eXBlPVwiZGF0ZXRpbWUtbG9jYWxcIlxyXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5zdGFydF90aW1lfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBzdGFydF90aW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMSBibG9jayB3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMiBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxyXG4gICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+TG9naW4gVHlwZTwvbGFiZWw+XHJcbiAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubG9naW5fdHlwZX1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgbG9naW5fdHlwZTogZS50YXJnZXQudmFsdWUgfSkpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTIgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcclxuICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5vcm1hbFwiPvCfkaQgTm9ybWFsIExvZ2luPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImdvb2dsZVwiPvCflI0gR29vZ2xlIExvZ2luPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1pY3Jvc29mdFwiPvCfj6IgTWljcm9zb2Z0IExvZ2luPC9vcHRpb24+XHJcbiAgICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlNSUCBUYXJnZXQgKG1heCA0MDApPC9sYWJlbD5cclxuICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgbWluPVwiMVwiXHJcbiAgICAgICAgICAgICAgbWF4PVwiNDAwXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc3JwX3RhcmdldH1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgc3JwX3RhcmdldDogcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIHx8IDEgfSkpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTEgYmxvY2sgdy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTIgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcclxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIFNSUCB0YXJnZXQgKDEtNDAwKVwiXHJcbiAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICBCcm93c2VyIHdpbGwgYXV0b21hdGljYWxseSBjbG9zZSB3aGVuIHRoaXMgU1JQIHRhcmdldCBpcyByZWFjaGVkLlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+Sm9iIERldGFpbHM8L2xhYmVsPlxyXG4gICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTY2hvb2xcIlxyXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5qb2JfZGF0YS5zY2hvb2x9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgam9iX2RhdGE6IHsgLi4ucHJldi5qb2JfZGF0YSwgc2Nob29sOiBlLnRhcmdldC52YWx1ZSB9XHJcbiAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgcHgtMyBweS0yIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXHJcbiAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW1haWwvVXNlcm5hbWVcIlxyXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5qb2JfZGF0YS5lbWFpbH1cclxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICBqb2JfZGF0YTogeyAuLi5wcmV2LmpvYl9kYXRhLCBlbWFpbDogZS50YXJnZXQudmFsdWUgfVxyXG4gICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMiBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxyXG4gICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJQYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmpvYl9kYXRhLnBhc3N3b3JkfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgIGpvYl9kYXRhOiB7IC4uLnByZXYuam9iX2RhdGEsIHBhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9XHJcbiAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgcHgtMyBweS0yIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXHJcbiAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTNcIj5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtZ3JheS03MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTBcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBDcmVhdGUgU2NoZWR1bGVcclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Zvcm0+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlNjaGVkdWxlQ2FsZW5kYXIiLCJzY2hlZHVsZXMiLCJvblNjaGVkdWxlU2VsZWN0Iiwib25DcmVhdGVTY2hlZHVsZSIsImN1cnJlbnREYXRlIiwic2V0Q3VycmVudERhdGUiLCJEYXRlIiwidmlldyIsInNldFZpZXciLCJzZWxlY3RlZERhdGUiLCJzZXRTZWxlY3RlZERhdGUiLCJzaG93Q3JlYXRlTW9kYWwiLCJzZXRTaG93Q3JlYXRlTW9kYWwiLCJnZXRDYWxlbmRhckRhdGEiLCJ5ZWFyIiwiZ2V0RnVsbFllYXIiLCJtb250aCIsImdldE1vbnRoIiwiZ2V0TW9udGhEYXRhIiwiZ2V0V2Vla0RhdGEiLCJnZXREYXlEYXRhIiwiZmlyc3REYXkiLCJsYXN0RGF5Iiwic3RhcnREYXRlIiwic2V0RGF0ZSIsImdldERhdGUiLCJnZXREYXkiLCJkYXlzIiwiY3VycmVudCIsImkiLCJwdXNoIiwiZGF0ZSIsInN0YXJ0T2ZXZWVrIiwiZGF5IiwiZ2V0U2NoZWR1bGVzRm9yRGF0ZSIsImRhdGVTdHIiLCJ0b0RhdGVTdHJpbmciLCJmaWx0ZXIiLCJzY2hlZHVsZSIsInNjaGVkdWxlRGF0ZSIsInN0YXJ0IiwibmF2aWdhdGVDYWxlbmRhciIsImRpcmVjdGlvbiIsIm5ld0RhdGUiLCJzZXRNb250aCIsImZvcm1hdERhdGVIZWFkZXIiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ3ZWVrRGF0YSIsImVuZCIsIndlZWtkYXkiLCJoYW5kbGVEYXRlQ2xpY2siLCJoYW5kbGVDcmVhdGVTY2hlZHVsZSIsImNhbGVuZGFyRGF0YSIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwibWFwIiwidmlld1R5cGUiLCJidXR0b24iLCJvbkNsaWNrIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsInNwYW4iLCJpbmRleCIsImRheVNjaGVkdWxlcyIsImlzQ3VycmVudE1vbnRoIiwiaXNUb2RheSIsImlzU2VsZWN0ZWQiLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwic3RhdHVzIiwidGl0bGUiLCJpZCIsImxlbmd0aCIsInN0YXJ0VGltZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJoNCIsIkFycmF5IiwiZnJvbSIsIl8iLCJ0aW1lU2xvdCIsInNldEhvdXJzIiwiaG91clNjaGVkdWxlcyIsInNjaGVkdWxlSG91ciIsImdldEhvdXJzIiwic2NoZWR1bGVUaW1lIiwiQ3JlYXRlU2NoZWR1bGVNb2RhbCIsIm9uQ2xvc2UiLCJvbkNyZWF0ZSIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJzdGFydF90aW1lIiwic3JwX3RhcmdldCIsImpvYl90eXBlIiwibG9naW5fdHlwZSIsImpvYl9kYXRhIiwic2Nob29sIiwiZW1haWwiLCJwYXNzd29yZCIsImRlZmF1bHRUaW1lIiwicHJldiIsInRvSVNPU3RyaW5nIiwiaGFuZGxlU3VibWl0IiwicHJldmVudERlZmF1bHQiLCJzY2hlZHVsZWRfdGltZSIsImZvcm0iLCJvblN1Ym1pdCIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInJlcXVpcmVkIiwic2VsZWN0Iiwib3B0aW9uIiwibWluIiwibWF4IiwicGFyc2VJbnQiLCJwbGFjZWhvbGRlciIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/queue/components/ScheduleCalendar.jsx\n"));

/***/ })

});