{"name": "reader-auto", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-db.js", "setup": "npm install && npm run init-db", "test-queue": "node test-queue-system.js", "test-webhook": "node test-queue-system.js --webhook-only", "test-performance": "node test-queue-system.js --performance-only"}, "dependencies": {"bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "dotenv": "^17.0.1", "jsonwebtoken": "^9.0.2", "next": "14.0.0", "playwright": "^1.40.0", "react": "^18", "react-dom": "^18", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0"}}