const { getDatabase } = require('./database');
const { getWebhookManager } = require('./webhook');

class QueueProcessor {
  constructor() {
    this.isProcessing = false;
    this.processingInterval = null;
    this.maxConcurrentJobs = 3; // Maximum number of jobs to process simultaneously
    this.currentlyProcessing = new Set();
    this.starvationPreventionInterval = null;
  }

  start() {
    if (this.isProcessing) {
      console.log('Queue processor is already running');
      return;
    }

    console.log('Starting queue processor...');
    this.isProcessing = true;
    
    // Start main processing loop
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 5000); // Check every 5 seconds

    // Start starvation prevention (runs every 10 minutes)
    this.starvationPreventionInterval = setInterval(() => {
      this.applyStarvationPrevention();
    }, 10 * 60 * 1000);

    console.log('Queue processor started');
  }

  stop() {
    if (!this.isProcessing) {
      console.log('Queue processor is not running');
      return;
    }

    console.log('Stopping queue processor...');
    this.isProcessing = false;

    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    if (this.starvationPreventionInterval) {
      clearInterval(this.starvationPreventionInterval);
      this.starvationPreventionInterval = null;
    }

    console.log('Queue processor stopped');
  }

  async processQueue() {
    try {
      const db = getDatabase();

      console.log('🔍 Queue processor checking for jobs...');

      // Check if we can process more jobs
      if (this.currentlyProcessing.size >= this.maxConcurrentJobs) {
        console.log('⏸️ Max concurrent jobs reached:', this.currentlyProcessing.size);
        return;
      }

      // Get next jobs to process
      const availableSlots = this.maxConcurrentJobs - this.currentlyProcessing.size;
      const nextJobs = this.getNextJobs(availableSlots);

      console.log(`📋 Found ${nextJobs.length} jobs ready to process (${availableSlots} slots available)`);

      if (nextJobs.length === 0) {
        console.log('📭 No jobs found in queue');
        return;
      }

      for (const job of nextJobs) {
        if (this.currentlyProcessing.has(job.id)) continue;

        console.log(`🚀 Starting job ${job.id} for user ${job.username}`);
        this.currentlyProcessing.add(job.id);
        this.processJob(job).finally(() => {
          this.currentlyProcessing.delete(job.id);
        });
      }

    } catch (error) {
      console.error('Queue processing error:', error);
    }
  }

  getNextJobs(limit) {
    const db = getDatabase();

    // First, let's see all jobs in the database for debugging
    const allJobsStmt = db.db.prepare(`
      SELECT qj.id, qj.status, qj.scheduled_time, qj.retry_count, qj.max_retries,
             qb.batch_name, u.username
      FROM queue_jobs qj
      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id
      JOIN users u ON qj.user_id = u.id
      ORDER BY qj.created_at DESC
    `);
    const allJobs = allJobsStmt.all();
    console.log('🗂️ All jobs in database:', allJobs.map(j => ({
      id: j.id,
      status: j.status,
      scheduled_time: j.scheduled_time,
      retry_count: j.retry_count,
      max_retries: j.max_retries,
      username: j.username
    })));

    // Get jobs that are ready to process
    const stmt = db.db.prepare(`
      SELECT qj.*, qb.batch_name, u.username
      FROM queue_jobs qj
      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id
      JOIN users u ON qj.user_id = u.id
      WHERE qj.status = 'queued'
      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))
      AND qj.retry_count < qj.max_retries
      ORDER BY qj.effective_priority DESC, qj.created_at ASC
      LIMIT ?
    `);

    const readyJobs = stmt.all(limit);
    console.log('✅ Jobs ready to process:', readyJobs.length);

    return readyJobs;
  }

  async processJob(job) {
    const db = getDatabase();
    const webhook = getWebhookManager();
    
    try {
      console.log(`Processing job ${job.id} for user ${job.username}`);
      
      // Update job status to processing
      db.updateJobStatus(job.id, 'processing', null, new Date().toISOString());
      
      // Update batch status if this is the first job in the batch
      if (job.batch_id) {
        const batchJobs = db.getBatchJobs(job.batch_id);
        const processingJobs = batchJobs.filter(j => j.status === 'processing');
        
        if (processingJobs.length === 1) { // This is the first job being processed
          db.updateBatchStatus(job.batch_id, 'processing', null);
          const batchStmt = db.db.prepare('UPDATE queue_batches SET started_at = CURRENT_TIMESTAMP WHERE id = ?');
          batchStmt.run(job.batch_id);
        }
      }

      // Parse job data
      const jobData = JSON.parse(job.job_data);

      // Process the job based on job type
      let result;
      switch (job.job_type) {
        case 'sparx_reader':
          result = await this.processSparxReaderJob(jobData, job.srp_target || 100);
          break;
        default:
          throw new Error(`Unknown job type: ${job.job_type}`);
      }

      // Mark job as completed
      db.updateJobStatus(job.id, 'completed', null, null, new Date().toISOString());
      
      // Update batch progress
      if (job.batch_id) {
        this.updateBatchProgress(job.batch_id);
      }

      console.log(`Job ${job.id} completed successfully`);

    } catch (error) {
      console.error(`Job ${job.id} failed:`, error);
      
      // Check if we should retry
      if (job.retry_count < job.max_retries) {
        console.log(`Retrying job ${job.id} (attempt ${job.retry_count + 1}/${job.max_retries})`);
        db.incrementJobRetry(job.id);
      } else {
        // Mark as failed
        db.updateJobStatus(job.id, 'failed', error.message, null, new Date().toISOString());
        
        // Update batch progress
        if (job.batch_id) {
          this.updateBatchProgress(job.batch_id, true);
        }

        // Send failure notification
        await webhook.sendQueueAlert(
          'system_error',
          'Job processing failed',
          {
            job_id: job.id,
            user: job.username,
            error: error.message,
            batch_name: job.batch_name || 'N/A'
          }
        );
      }
    }
  }

  async processSparxReaderJob(jobData, srpTarget = 100) {
    // Integrate with actual Sparx Reader functionality
    const { school, email, password, username, login_method = 'normal' } = jobData;

    try {
      console.log(`Starting Sparx Reader job for ${email || username} at ${school} with SRP target: ${srpTarget}`);

      // Determine which start endpoint to use based on login method
      let startEndpoint;
      switch (login_method) {
        case 'google':
          startEndpoint = '/api/sparxreader/google-start';
          break;
        case 'microsoft':
          startEndpoint = '/api/sparxreader/microsoft-start';
          break;
        default:
          startEndpoint = '/api/sparxreader/start';
      }

      // Step 1: Start the browser session and login
      const startResponse = await fetch(`http://localhost:3001${startEndpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          school: school,
          email: email || username,
          password: password
        })
      });

      const startData = await startResponse.json();
      if (!startData.success) {
        throw new Error(`Login failed: ${startData.error}`);
      }

      console.log('Login successful, starting homework processing...');

      // Step 2: Navigate and process homework with SRP target
      const navigateResponse = await fetch('http://localhost:3001/api/sparxreader/navigate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'confirm',
          bookTitle: startData.bookTitle,
          targetSrp: srpTarget
        })
      });

      const navigateData = await navigateResponse.json();
      if (!navigateData.success) {
        throw new Error(`Navigation failed: ${navigateData.error}`);
      }

      console.log('Homework processing completed successfully');

      return {
        success: true,
        processed_at: new Date().toISOString(),
        school,
        email: (email || username).replace(/(.{2}).*(@.*)/, '$1***$2'), // Mask email for logging
        bookTitle: navigateData.bookTitle,
        srpTarget: srpTarget,
        storyContent: navigateData.storyContent
      };

    } catch (error) {
      console.error('Sparx Reader job failed:', error);
      throw error;
    }
  }

  updateBatchProgress(batchId, hasFailed = false) {
    const db = getDatabase();
    const webhook = getWebhookManager();
    
    try {
      // Get batch and job statistics
      const batch = db.getQueueBatches(null, null, 1, 0).find(b => b.id === batchId);
      if (!batch) return;

      const jobs = db.getBatchJobs(batchId);
      const completedJobs = jobs.filter(j => j.status === 'completed').length;
      const failedJobs = jobs.filter(j => j.status === 'failed').length;
      const totalJobs = jobs.length;

      // Update batch progress
      const updateStmt = db.db.prepare(`
        UPDATE queue_batches 
        SET processed_accounts = ?, failed_accounts = ?
        WHERE id = ?
      `);
      updateStmt.run(completedJobs, failedJobs, batchId);

      // Check if batch is complete
      const remainingJobs = jobs.filter(j => ['queued', 'processing'].includes(j.status)).length;
      
      if (remainingJobs === 0) {
        const finalStatus = failedJobs === totalJobs ? 'failed' : 'completed';
        db.updateBatchStatus(batchId, finalStatus, new Date().toISOString());
        
        // Send completion notification
        const processingDuration = batch.started_at ? 
          new Date().getTime() - new Date(batch.started_at).getTime() : 0;

        webhook.sendBatchCompleted(
          batch.username,
          batch.batch_name,
          completedJobs,
          failedJobs,
          processingDuration
        );

        console.log(`Batch ${batchId} completed: ${completedJobs} successful, ${failedJobs} failed`);
      }

    } catch (error) {
      console.error('Error updating batch progress:', error);
    }
  }

  applyStarvationPrevention() {
    try {
      const db = getDatabase();
      const result = db.applyStarvationPrevention();
      
      if (result.changes > 0) {
        console.log(`Applied starvation prevention to ${result.changes} jobs`);
      }
    } catch (error) {
      console.error('Error applying starvation prevention:', error);
    }
  }

  getStatus() {
    return {
      is_processing: this.isProcessing,
      currently_processing_count: this.currentlyProcessing.size,
      max_concurrent_jobs: this.maxConcurrentJobs,
      processing_job_ids: Array.from(this.currentlyProcessing)
    };
  }

  setMaxConcurrentJobs(max) {
    if (max > 0 && max <= 10) {
      this.maxConcurrentJobs = max;
      console.log(`Max concurrent jobs set to ${max}`);
    }
  }
}

// Export singleton instance
let processorInstance = null;

function getQueueProcessor() {
  if (!processorInstance) {
    processorInstance = new QueueProcessor();
  }
  return processorInstance;
}

module.exports = { getQueueProcessor, QueueProcessor };