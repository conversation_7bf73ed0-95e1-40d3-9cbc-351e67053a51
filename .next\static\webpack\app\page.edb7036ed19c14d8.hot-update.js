"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.jsx":
/*!**********************!*\
  !*** ./app/page.jsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SparxReaderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SparxReaderPage() {\n    _s();\n    // Authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [authLoading, setAuthLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bookTitle, setBookTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [needsPlaywright, setNeedsPlaywright] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [screenshot, setScreenshot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [storyContent, setStoryContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showBookConfirmation, setShowBookConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSrp, setCurrentSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetSrp, setTargetSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showInitialSrpInput, setShowInitialSrpInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for enhanced UI\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentAnswer, setCurrentAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [questionNumber, setQuestionNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [srpEarned, setSrpEarned] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutomationRunning, setIsAutomationRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionHistory, setQuestionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [animationKey, setAnimationKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [automationComplete, setAutomationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [queuePosition, setQueuePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobId, setJobId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInQueue, setIsInQueue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginMethod, setLoginMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\"); // 'normal' or 'microsoft'\n    // Credential system states\n    const [showCredentialInput, setShowCredentialInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [credentialMode, setCredentialMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"enter\"); // 'enter' or 'key'\n    const [userSchool, setUserSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userEmail, setUserEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userPassword, setUserPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loginKey, setLoginKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [savedCredentials, setSavedCredentials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // License renewal states\n    const [showLicenseRenewal, setShowLicenseRenewal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [licenseStatus, setLicenseStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newLicenseKey, setNewLicenseKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [licenseRenewalLoading, setLicenseRenewalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check authentication on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuthentication();\n    }, []);\n    // Simulate question-solving process AFTER automation completes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated || !isAutomationRunning || !automationComplete) return;\n        if (isAutomationRunning && automationComplete) {\n            const interval = setInterval(()=>{\n                // Simulate question solving progress\n                setQuestionNumber((prev)=>{\n                    const newNum = prev + 1;\n                    setSrpEarned((prevSrp)=>prevSrp + Math.floor(Math.random() * 3) + 2);\n                    setAnimationKey((prevKey)=>prevKey + 1);\n                    // Simulate new question\n                    const sampleQuestions = [\n                        \"What was the main character's motivation in chapter 3?\",\n                        \"How did the setting influence the story's outcome?\",\n                        \"What literary device was used in the opening paragraph?\",\n                        \"Why did the protagonist make that crucial decision?\",\n                        \"What theme is most prominent throughout the narrative?\",\n                        \"How does the author develop the central conflict?\",\n                        \"What role does symbolism play in the narrative?\",\n                        \"are these questions fake and is your homework already complete?\"\n                    ];\n                    const sampleAnswers = [\n                        \"To find their lost family member\",\n                        \"The harsh winter created urgency\",\n                        \"Metaphor and symbolism\",\n                        \"To protect their friends\",\n                        \"The importance of friendship\",\n                        \"Through escalating tension\",\n                        \"It reinforces the main themes\",\n                        \"yes lol\"\n                    ];\n                    const randomIndex = Math.floor(Math.random() * sampleQuestions.length);\n                    setCurrentQuestion(sampleQuestions[randomIndex]);\n                    setCurrentAnswer(sampleAnswers[randomIndex]);\n                    // Add to history\n                    setQuestionHistory((prev)=>[\n                            ...prev,\n                            {\n                                number: newNum,\n                                question: sampleQuestions[randomIndex],\n                                answer: sampleAnswers[randomIndex]\n                            }\n                        ]);\n                    // Stop after reaching target or max questions\n                    if (newNum >= 8 || srpEarned >= parseInt(targetSrp || 50)) {\n                        setTimeout(()=>{\n                            setIsAutomationRunning(false);\n                            setMessage(\"Target SRP reached! Automation completed successfully.\");\n                        }, 1500); // Show the last question for a bit\n                        clearInterval(interval);\n                        return newNum;\n                    }\n                    return newNum;\n                });\n            }, 2500); // Show new question every 2.5 seconds\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isAuthenticated,\n        isAutomationRunning,\n        automationComplete,\n        srpEarned,\n        targetSrp\n    ]);\n    // Authentication functions\n    const checkAuthentication = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const response = await fetch(\"/api/auth/validate\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success && data.valid) {\n                setIsAuthenticated(true);\n                setUser(data.user);\n                setLicenseStatus(data.licenseStatus);\n            } else {\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"user\");\n                router.push(\"/login\");\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        } finally{\n            setAuthLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        }\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    // Handle login method selection (doesn't start the process)\n    const handleLoginMethodSelect = (method)=>{\n        setLoginMethod(method);\n    };\n    // Check license validity before starting bot\n    const checkLicenseValidity = ()=>{\n        // Admin users don't need license validation\n        if (user && user.role === \"admin\") {\n            return {\n                valid: true\n            };\n        }\n        if (!licenseStatus) {\n            return {\n                valid: false,\n                error: \"License information not available\"\n            };\n        }\n        if (licenseStatus.license_status !== \"valid\") {\n            let errorMessage = \"Your license is not valid\";\n            switch(licenseStatus.license_status){\n                case \"expired\":\n                    errorMessage = \"Your license has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"Your license has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"Your license is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage,\n                status: licenseStatus.license_status\n            };\n        }\n        return {\n            valid: true\n        };\n    };\n    // Handle license renewal\n    const handleLicenseRenewal = async ()=>{\n        if (!newLicenseKey.trim()) {\n            setMessage(\"Please enter a valid license key\");\n            return;\n        }\n        setLicenseRenewalLoading(true);\n        setMessage(\"Renewing license...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/auth/renew-license\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    newLicenseKey: newLicenseKey.trim()\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(\"License renewed successfully! You can now start the bot.\");\n                setShowLicenseRenewal(false);\n                setNewLicenseKey(\"\");\n                // Refresh authentication to get updated license status\n                await checkAuthentication();\n            } else {\n                setMessage(data.error || \"Failed to renew license\");\n            }\n        } catch (error) {\n            setMessage(\"Error occurred while renewing license\");\n        } finally{\n            setLicenseRenewalLoading(false);\n        }\n    };\n    // Handle the actual start process\n    const handleBeginClick = async ()=>{\n        // Check authentication before starting\n        if (!isAuthenticated || !user) {\n            setMessage(\"Please login to use this feature\");\n            router.push(\"/login\");\n            return;\n        }\n        // Check license validity before proceeding\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            return;\n        }\n        // Reset all states\n        setMessage(\"\");\n        setBookTitle(\"\");\n        setNeedsPlaywright(false);\n        setScreenshot(\"\");\n        setStoryContent(\"\");\n        setShowBookConfirmation(false);\n        setCurrentSrp(\"\");\n        setTargetSrp(\"\");\n        setShowInitialSrpInput(false);\n        setAutomationComplete(false);\n        // Reset credential states\n        setUserSchool(\"\");\n        setUserEmail(\"\");\n        setUserPassword(\"\");\n        setLoginKey(\"\");\n        // Mark that user has started the process\n        setHasStarted(true);\n        // Show credential input first\n        setShowCredentialInput(true);\n        // Load user's saved credentials\n        await loadSavedCredentials();\n    };\n    // Load user's saved credentials\n    const loadSavedCredentials = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/credentials/list\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSavedCredentials(data.credentials);\n            }\n        } catch (error) {\n            console.error(\"Failed to load saved credentials:\", error);\n        }\n    };\n    // Handle credential submission\n    const handleCredentialSubmit = async ()=>{\n        if (credentialMode === \"key\") {\n            // Use saved credentials with login key\n            if (!loginKey) {\n                setMessage(\"Please enter your login key\");\n                return;\n            }\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    // Proceed with automation using saved credentials\n                    setShowCredentialInput(false);\n                    setMessage(\"Please enter how much SRP you need to earn:\");\n                    setShowInitialSrpInput(true);\n                } else {\n                    setMessage(data.error || \"Invalid login key\");\n                }\n            } catch (error) {\n                setMessage(\"Error retrieving credentials\");\n            }\n        } else {\n            // Use entered credentials\n            if (!userSchool || !userEmail || !userPassword) {\n                setMessage(\"Please enter school, email and password\");\n                return;\n            }\n            // Ask if user wants to save credentials\n            if (confirm(\"Would you like to save these credentials for future use? You will receive a secure login key.\")) {\n                try {\n                    const token = localStorage.getItem(\"token\");\n                    const response = await fetch(\"/api/credentials/save\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            \"Authorization\": \"Bearer \".concat(token)\n                        },\n                        body: JSON.stringify({\n                            loginMethod,\n                            school: userSchool,\n                            email: userEmail,\n                            password: userPassword\n                        })\n                    });\n                    const data = await response.json();\n                    if (data.success) {\n                        alert(\"Credentials saved! Your login key is: \".concat(data.loginKey, \"\\n\\nPlease save this key securely. You can use it for future logins.\"));\n                    }\n                } catch (error) {\n                    console.error(\"Failed to save credentials:\", error);\n                }\n            }\n            // Proceed with automation\n            setShowCredentialInput(false);\n            setMessage(\"Please enter how much SRP you need to earn:\");\n            setShowInitialSrpInput(true);\n        }\n    };\n    const handleSrpSubmit = async ()=>{\n        // Validate SRP input\n        if (!targetSrp || isNaN(targetSrp) || parseInt(targetSrp) <= 0) {\n            setMessage(\"Please enter a valid SRP target (positive number)\");\n            return;\n        }\n        // Check license validity again before starting automation\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            setShowInitialSrpInput(false);\n            return;\n        }\n        setLoading(true);\n        setShowInitialSrpInput(false);\n        const isNormalLogin = loginMethod === \"normal\";\n        const isMicrosoftLogin = loginMethod === \"microsoft\";\n        const isGoogleLogin = loginMethod === \"google\";\n        // Get credentials\n        let credentials = null;\n        if (credentialMode === \"key\" && loginKey) {\n            // Get credentials from login key\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    credentials = data.credentials;\n                } else {\n                    setLoading(false);\n                    setMessage(data.error || \"Failed to retrieve credentials\");\n                    return;\n                }\n            } catch (error) {\n                setLoading(false);\n                setMessage(\"Error retrieving credentials\");\n                return;\n            }\n        } else if (credentialMode === \"enter\" && userSchool && userEmail && userPassword) {\n            // Use entered credentials\n            credentials = {\n                school: userSchool,\n                email: userEmail,\n                password: userPassword,\n                loginMethod: loginMethod\n            };\n        } else {\n            setLoading(false);\n            setMessage(\"No credentials available\");\n            return;\n        }\n        if (isNormalLogin) {\n            setMessage(\"Preparing to start...\");\n        } else if (isMicrosoftLogin) {\n            setMessage(\"Starting Microsoft login automation...\");\n        } else if (isGoogleLogin) {\n            setMessage(\"Starting Google login automation...\");\n        }\n        try {\n            let apiEndpoint, requestBody;\n            if (isNormalLogin) {\n                apiEndpoint = \"/api/sparxreader/start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isMicrosoftLogin) {\n                apiEndpoint = \"/api/sparxreader/microsoft-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isGoogleLogin) {\n                apiEndpoint = \"/api/sparxreader/google-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            }\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(apiEndpoint, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Book title and SRP extracted, show confirmation dialog\n                setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                setBookTitle(data.bookTitle);\n                setCurrentSrp(data.currentSrp);\n                setShowBookConfirmation(true);\n                if (data.screenshot) {\n                    setScreenshot(data.screenshot);\n                }\n            } else {\n                setMessage(data.error || \"Failed to start\");\n                if (data.needsPlaywright) {\n                    setNeedsPlaywright(true);\n                }\n            }\n        } catch (error) {\n            setMessage(\"Error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleYesClick = async ()=>{\n        setLoading(true);\n        setShowBookConfirmation(false);\n        setIsInQueue(true);\n        setMessage(\"Adding to queue...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            // Create a queue job instead of direct automation\n            const response = await fetch(\"/api/queue/jobs\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    job_type: \"sparx_reader\",\n                    job_data: {\n                        school: selectedSchool,\n                        email: email,\n                        username: username,\n                        password: password,\n                        login_type: loginMethod,\n                        bookTitle: bookTitle\n                    },\n                    srp_target: targetSrp ? parseInt(targetSrp) : 100,\n                    priority: 0\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setJobId(data.job_id);\n                setMessage(\"Added to queue! Checking position...\");\n                // Start monitoring queue position\n                startQueueMonitoring(data.job_id);\n            } else {\n                setIsInQueue(false);\n                setMessage(data.error || \"Failed to add job to queue\");\n            }\n        } catch (error) {\n            setIsInQueue(false);\n            setMessage(\"Error occurred while adding to queue\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const startQueueMonitoring = (jobId)=>{\n        const checkPosition = async ()=>{\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/queue/position?job_id=\".concat(jobId), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n                const data = await response.json();\n                if (data.success) {\n                    setQueuePosition(data.position);\n                    if (data.position === 1 && data.status === \"processing\") {\n                        // Job is now being processed!\n                        setIsProcessing(true);\n                        setIsInQueue(false);\n                        setMessage(\"Your job is now being processed!\");\n                        // Start monitoring job completion\n                        startJobMonitoring(jobId);\n                    } else if (data.status === \"completed\") {\n                        // Job completed\n                        handleJobCompletion(data.result);\n                    } else if (data.status === \"failed\") {\n                        // Job failed\n                        setIsInQueue(false);\n                        setIsProcessing(false);\n                        setMessage(data.error || \"Job failed to process\");\n                    } else {\n                        // Still in queue\n                        setMessage(\"Queue position: #\".concat(data.position));\n                        // Check again in 3 seconds\n                        setTimeout(checkPosition, 3000);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error checking queue position:\", error);\n                setTimeout(checkPosition, 5000); // Retry in 5 seconds\n            }\n        };\n        checkPosition();\n    };\n    const startJobMonitoring = (jobId)=>{\n        const checkJobStatus = async ()=>{\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/queue/jobs/\".concat(jobId), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n                const data = await response.json();\n                if (data.success) {\n                    if (data.job.status === \"completed\") {\n                        handleJobCompletion(data.job.result);\n                    } else if (data.job.status === \"failed\") {\n                        setIsProcessing(false);\n                        setMessage(data.job.error || \"Job failed to process\");\n                    } else {\n                        // Still processing, check again\n                        setTimeout(checkJobStatus, 5000);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error checking job status:\", error);\n                setTimeout(checkJobStatus, 5000);\n            }\n        };\n        checkJobStatus();\n    };\n    const handleJobCompletion = (result)=>{\n        setIsProcessing(false);\n        setIsInQueue(false);\n        if (result && result.success) {\n            setMessage(\"Automation completed! Displaying results...\");\n            setStoryContent(result.storyContent);\n            setBookTitle(result.bookTitle);\n            // Start the question simulation\n            setTimeout(()=>{\n                setAutomationComplete(true);\n                setIsAutomationRunning(true);\n                setSrpEarned(0);\n                setQuestionNumber(0);\n                setQuestionHistory([]);\n                setMessage(\"\");\n            }, 1500);\n            if (result.screenshot) {\n                setScreenshot(result.screenshot);\n            }\n        } else {\n            setMessage((result === null || result === void 0 ? void 0 : result.error) || \"Job completed but with errors\");\n        }\n    };\n    const handleNoClick = async ()=>{\n        setLoading(true);\n        setMessage(\"Finding a different book with same SRP target...\");\n        try {\n            // Close current session\n            const token = localStorage.getItem(\"token\");\n            await fetch(\"/api/sparxreader/close\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            // Restart automation with existing target SRP\n            setLoading(true);\n            setShowInitialSrpInput(false);\n            setMessage(\"Preparing to start with new book...\");\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/sparxreader/start\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                        targetSrp: parseInt(targetSrp)\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                    setBookTitle(data.bookTitle);\n                    setCurrentSrp(data.currentSrp);\n                    setShowBookConfirmation(true);\n                    if (data.screenshot) {\n                        setScreenshot(data.screenshot);\n                    }\n                } else {\n                    setMessage(data.error || \"Failed to start\");\n                    if (data.needsPlaywright) {\n                        setNeedsPlaywright(true);\n                    }\n                }\n            } catch (error) {\n                setMessage(\"Error occurred while restarting\");\n            }\n        } catch (error) {\n            setMessage(\"Error closing previous session\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBackClick = ()=>{\n        setIsAutomationRunning(false);\n        setIsProcessing(false);\n        setCurrentQuestion(\"\");\n        setCurrentAnswer(\"\");\n        setQuestionNumber(0);\n        setSrpEarned(0);\n        setQuestionHistory([]);\n        setMessage(\"\");\n        setShowBookConfirmation(false);\n        setShowInitialSrpInput(false);\n        setHasStarted(false);\n        setAutomationComplete(false);\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 774,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 776,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 777,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 775,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 773,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 791,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"p-6 border-b border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto relative\",\n                            children: [\n                                (isAutomationRunning || isProcessing) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackClick,\n                                    className: \"absolute left-0 top-1/2 transform -translate-y-1/2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-all duration-200 flex items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"<\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 top-1/2 transform -translate-y-1/2 flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        user === null || user === void 0 ? void 0 : user.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"Administrator\" : \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                (user === null || user === void 0 ? void 0 : user.role) === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/admin\"),\n                                                    className: \"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Admin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/queue\"),\n                                                    className: \"px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Queue\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogout,\n                                                    className: \"px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 829,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-light text-white text-center\",\n                                    children: \"Sparx Reader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-center text-gray-400 mt-1 text-sm\",\n                                    children: \"Automated Question Solving\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 796,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 795,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-4xl mx-auto\",\n                            children: [\n                                (isInQueue || isProcessing) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-8 text-center shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-20 h-20 mx-auto mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 border-4 border-blue-500/30 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 bg-blue-500 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 858,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.5s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 19\n                                            }, this),\n                                            isInQueue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent mb-4\",\n                                                        children: \"� In Queue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 text-lg\",\n                                                        children: queuePosition ? \"Queue Position: #\".concat(queuePosition) : \"Checking queue position...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full max-w-md mx-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 border-4 border-yellow-500 border-t-transparent rounded-full animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 876,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-300\",\n                                                                    children: \"Waiting for your turn...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4\",\n                                                        children: \"\\uD83D\\uDE80 Processing Automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 text-lg\",\n                                                        children: \"AI is solving questions in the background...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full max-w-md mx-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 border-4 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-300\",\n                                                                    children: \"Processing your request...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 15\n                                }, this),\n                                isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                    children: \"\\uD83D\\uDCCA Live Progress Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 903,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-24 h-24 mx-auto mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-24 h-24 transform -rotate-90\",\n                                                                        viewBox: \"0 0 100 100\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                className: \"text-gray-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 915,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"url(#srpGradient)\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                strokeDasharray: \"\".concat(srpEarned / parseInt(targetSrp || 1) * 251.2, \" 251.2\"),\n                                                                                className: \"transition-all duration-1000 ease-out drop-shadow-lg\",\n                                                                                strokeLinecap: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                                    id: \"srpGradient\",\n                                                                                    x1: \"0%\",\n                                                                                    y1: \"0%\",\n                                                                                    x2: \"100%\",\n                                                                                    y2: \"100%\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"0%\",\n                                                                                            stopColor: \"#10B981\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 927,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"50%\",\n                                                                                            stopColor: \"#3B82F6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 928,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"100%\",\n                                                                                            stopColor: \"#8B5CF6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 929,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 926,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 925,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 914,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xl font-bold text-white block\",\n                                                                                    children: srpEarned\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 935,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: \"SRP\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 936,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 934,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 rounded-full bg-gradient-to-r from-green-500/20 to-blue-500/20 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 940,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium mb-1\",\n                                                                children: \"SRP Earned\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-400 text-xs\",\n                                                                children: [\n                                                                    \"Target: \",\n                                                                    targetSrp\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 943,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    Math.round(srpEarned / parseInt(targetSrp || 1) * 100),\n                                                                    \"% Complete\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold text-blue-400 mb-1 animate-pulse\",\n                                                                        children: questionNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 952,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"Questions Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 953,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-700 rounded-full h-3 overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-blue-500 to-cyan-400 h-3 rounded-full transition-all duration-1000 ease-out relative\",\n                                                                            style: {\n                                                                                width: \"\".concat(Math.min(questionNumber / 10 * 100, 100), \"%\")\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-white/30 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 959,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 955,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 954,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            Math.min(Math.round(questionNumber / 10 * 100), 100),\n                                                                            \"% of estimated session\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 962,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83E\\uDDE0 AI Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 966,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mx-auto mb-3 flex items-center justify-center animate-pulse\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 973,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 972,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"System Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-700 rounded-lg p-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-green-400 mb-2\",\n                                                                                children: \"\\uD83D\\uDFE2 Active & Processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 978,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400 mb-1\",\n                                                                                children: \"Performance Metrics\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 979,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-white\",\n                                                                                children: [\n                                                                                    \"⚡ \",\n                                                                                    questionNumber > 0 ? Math.round(elapsedTime / questionNumber) : 0,\n                                                                                    \"s per question\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 980,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-400\",\n                                                                                children: [\n                                                                                    \"\\uD83C\\uDFAF \",\n                                                                                    questionNumber > 0 ? Math.round(questionNumber / elapsedTime * 60) : 0,\n                                                                                    \" questions/min\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 983,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-purple-400 mt-1\",\n                                                                                children: [\n                                                                                    \"⏱️ \",\n                                                                                    formatTime(elapsedTime),\n                                                                                    \" elapsed\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 986,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 977,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 971,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83D\\uDE80 AI Engine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 991,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 909,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 902,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, this),\n                                currentQuestion && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-slide-up\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4 animate-pulse\",\n                                                        children: questionNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                                children: \"✅ Question Solved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1007,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"AI successfully processed this question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1006,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-auto flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1013,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400 font-medium\",\n                                                                children: \"COMPLETED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1012,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/60 border border-gray-600 rounded-xl p-6 mb-6 backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"Q\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1021,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-base leading-relaxed mb-3\",\n                                                                    children: currentQuestion\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1024,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 bg-green-600 text-white text-xs rounded font-medium\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                Math.floor(Math.random() * 3) + 1,\n                                                                                \" SRP\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1026,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"• Solved automatically\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1029,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1025,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1023,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1019,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentAnswer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"AI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1039,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1038,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-400 font-medium mb-2\",\n                                                                    children: \"\\uD83E\\uDD16 AI Solution:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1042,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-200 leading-relaxed\",\n                                                                    children: currentAnswer\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1043,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1041,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1037,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 17\n                                    }, this)\n                                }, animationKey, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1000,\n                                    columnNumber: 15\n                                }, this),\n                                !hasStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-10 max-w-lg mx-auto shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-16 h-16 mx-auto mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-bounce\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-2xl\",\n                                                                    children: \"\\uD83D\\uDE80\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1059,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1061,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-purple-400 rounded-full animate-ping\",\n                                                                style: {\n                                                                    animationDelay: \"0.5s\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3\",\n                                                        children: \"Start Automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-base\",\n                                                        children: \"\\uD83E\\uDD16 AI-powered question solving for Sparx Reader\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                        children: \"\\uD83D\\uDD10 Choose Login Method\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"normal\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"normal\" ? \"bg-gradient-to-r from-green-600 to-green-500 text-white ring-2 ring-green-400 shadow-lg shadow-green-500/25\" : \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-500 text-white shadow-lg hover:shadow-green-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDC64\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1085,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Normal Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1086,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"normal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1087,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1076,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"microsoft\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"microsoft\" ? \"bg-gradient-to-r from-blue-600 to-blue-500 text-white ring-2 ring-blue-400 shadow-lg shadow-blue-500/25\" : \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-500 text-white shadow-lg hover:shadow-blue-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83C\\uDFE2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1099,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Microsoft Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1100,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"microsoft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1090,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"google\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"google\" ? \"bg-gradient-to-r from-red-600 to-red-500 text-white ring-2 ring-red-400 shadow-lg shadow-red-500/25\" : \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-500 text-white shadow-lg hover:shadow-red-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDD0D\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1113,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Google Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1114,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"google\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1115,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1104,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1075,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1071,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: [\n                                                                \"Selected: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        loginMethod === \"normal\" && \"Normal Login\",\n                                                                        loginMethod === \"microsoft\" && \"Microsoft Login\",\n                                                                        loginMethod === \"google\" && \"Google Login\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1123,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1122,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBeginClick,\n                                                        disabled: loading,\n                                                        className: \"w-full py-3 px-6 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Begin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1130,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 15\n                                }, this),\n                                showInitialSrpInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1148,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Set SRP Target\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1150,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"How much SRP do you want to earn?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: targetSrp,\n                                                        onChange: (e)=>setTargetSrp(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleSrpSubmit(),\n                                                        placeholder: \"Enter target (e.g., 50)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center\",\n                                                        min: \"1\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1155,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 text-center\",\n                                                        children: \"Automation will stop when target is reached\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1165,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSrpSubmit,\n                                                                disabled: loading || !targetSrp,\n                                                                className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1177,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Starting...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1176,\n                                                                    columnNumber: 27\n                                                                }, this) : \"Start\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1170,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowInitialSrpInput(false),\n                                                                disabled: loading,\n                                                                className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1182,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1169,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1154,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1144,\n                                    columnNumber: 15\n                                }, this),\n                                showCredentialInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"\\uD83D\\uDD10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1200,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Login Credentials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1203,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            loginMethod === \"normal\" && \"Enter your Sparx Learning credentials\",\n                                                            loginMethod === \"microsoft\" && \"Enter your Microsoft account credentials\",\n                                                            loginMethod === \"google\" && \"Enter your Google account credentials\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1204,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex bg-gray-800 rounded-lg p-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"enter\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"enter\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Enter Credentials\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1214,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"key\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"key\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Use Login Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1224,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1213,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1212,\n                                                columnNumber: 19\n                                            }, this),\n                                            credentialMode === \"enter\" ? /* Enter Credentials Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: userSchool,\n                                                        onChange: (e)=>setUserSchool(e.target.value),\n                                                        placeholder: \"School name (e.g., theangmeringschool)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1240,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: userEmail,\n                                                        onChange: (e)=>setUserEmail(e.target.value),\n                                                        placeholder: \"Email address\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1248,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        value: userPassword,\n                                                        onChange: (e)=>setUserPassword(e.target.value),\n                                                        placeholder: \"Password\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1255,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDCA1 Your credentials will be encrypted and stored securely. You'll receive a login key for future use.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1262,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1239,\n                                                columnNumber: 21\n                                            }, this) : /* Use Login Key Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: loginKey,\n                                                        onChange: (e)=>setLoginKey(e.target.value),\n                                                        placeholder: \"Enter your login key (e.g., SLK-ABC12345)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center font-mono\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1269,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    savedCredentials.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mb-2\",\n                                                                children: \"Your saved login keys:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1 max-h-32 overflow-y-auto\",\n                                                                children: savedCredentials.map((cred, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setLoginKey(cred.loginKey),\n                                                                        className: \"w-full text-left px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-sm text-gray-300 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-mono text-blue-400\",\n                                                                                children: cred.loginKey\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1288,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-2 text-xs\",\n                                                                                children: [\n                                                                                    \"(\",\n                                                                                    cred.loginMethod,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1289,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1283,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1281,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1279,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDD11 Use your previously generated login key to access saved credentials.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCredentialSubmit,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1310,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1309,\n                                                            columnNumber: 25\n                                                        }, this) : \"Continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowCredentialInput(false);\n                                                            setHasStarted(false);\n                                                        },\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1197,\n                                    columnNumber: 15\n                                }, this),\n                                showBookConfirmation && bookTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-lg w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"B\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1336,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1335,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Book Found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Confirm to start automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1334,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/30 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-white mb-4\",\n                                                        children: bookTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Current SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1346,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: currentSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1347,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1345,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Target SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1350,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: targetSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1351,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1349,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1342,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleYesClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1364,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Starting...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1363,\n                                                            columnNumber: 25\n                                                        }, this) : \"Yes, Start\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1357,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleNoClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? \"Finding...\" : \"Find Different\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1369,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1356,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1333,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1332,\n                                    columnNumber: 15\n                                }, this),\n                                questionHistory.length > 0 && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"H\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Question History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1385,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-60 overflow-y-auto\",\n                                                children: questionHistory.slice(-5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/30 rounded p-3 border-l-4 border-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 font-medium text-sm\",\n                                                                        children: [\n                                                                            \"Q\",\n                                                                            item.number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 text-xs\",\n                                                                        children: \"Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1394,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm mb-2\",\n                                                                children: [\n                                                                    item.question.substring(0, 100),\n                                                                    \"...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1396,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: [\n                                                                    \"Answer: \",\n                                                                    item.answer\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1397,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1391,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1389,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1383,\n                                    columnNumber: 15\n                                }, this),\n                                message && !showInitialSrpInput && !showBookConfirmation && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded p-4 inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white\",\n                                            children: message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 1409,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1407,\n                                    columnNumber: 15\n                                }, this),\n                                needsPlaywright && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-red-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1420,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1419,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Setup Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1422,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Playwright browsers need to be installed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1418,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-sm mb-2\",\n                                                        children: \"Run this command in your terminal:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1427,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-black text-blue-400 p-2 rounded block text-sm\",\n                                                        children: \"npx playwright install chromium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1428,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1426,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-xs text-center\",\n                                                children: \"After installation, refresh this page and try again.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1433,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1417,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1416,\n                                    columnNumber: 15\n                                }, this),\n                                showLicenseRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-8 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-500 text-2xl\",\n                                                            children: \"⚠️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1446,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1445,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-medium text-white mb-2\",\n                                                        children: \"License Renewal Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1448,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            licenseStatus && licenseStatus.license_status === \"expired\" && \"Your license has expired.\",\n                                                            licenseStatus && licenseStatus.license_status === \"maxed_out\" && \"Your license has reached maximum uses.\",\n                                                            licenseStatus && licenseStatus.license_status === \"inactive\" && \"Your license is inactive.\",\n                                                            (!licenseStatus || licenseStatus.license_status === \"valid\") && \"Your license is not valid.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1449,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mt-2\",\n                                                        children: \"Please enter a new license key to continue using the bot.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1455,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1444,\n                                                columnNumber: 19\n                                            }, this),\n                                            licenseStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 border border-gray-700 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-white font-medium mb-2\",\n                                                        children: \"Current License Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1462,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Key: \",\n                                                                    licenseStatus.key_code || \"N/A\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1464,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium \".concat(licenseStatus.license_status === \"expired\" ? \"text-red-400\" : licenseStatus.license_status === \"maxed_out\" ? \"text-orange-400\" : licenseStatus.license_status === \"inactive\" ? \"text-gray-400\" : \"text-green-400\"),\n                                                                        children: [\n                                                                            licenseStatus.license_status === \"expired\" && \"Expired\",\n                                                                            licenseStatus.license_status === \"maxed_out\" && \"Max Uses Reached\",\n                                                                            licenseStatus.license_status === \"inactive\" && \"Inactive\",\n                                                                            licenseStatus.license_status === \"valid\" && \"Valid\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1465,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1465,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            licenseStatus.expires_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Expires: \",\n                                                                    new Date(licenseStatus.expires_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1477,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            licenseStatus.max_uses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Uses: \",\n                                                                    licenseStatus.current_uses || 0,\n                                                                    \"/\",\n                                                                    licenseStatus.max_uses\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1480,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1463,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1461,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-white font-medium mb-2\",\n                                                        children: \"New License Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1487,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newLicenseKey,\n                                                        onChange: (e)=>setNewLicenseKey(e.target.value),\n                                                        placeholder: \"Enter your new license key (e.g., SRX-XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXXXX)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:border-blue-500 focus:outline-none\",\n                                                        disabled: licenseRenewalLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1488,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1486,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLicenseRenewal,\n                                                        disabled: licenseRenewalLoading || !newLicenseKey.trim(),\n                                                        className: \"flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded transition-colors duration-200\",\n                                                        children: licenseRenewalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1506,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Renewing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1505,\n                                                            columnNumber: 25\n                                                        }, this) : \"Renew License\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1499,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowLicenseRenewal(false);\n                                                            setNewLicenseKey(\"\");\n                                                            setMessage(\"\");\n                                                        },\n                                                        disabled: licenseRenewalLoading,\n                                                        className: \"px-6 py-3 border border-gray-600 text-gray-400 hover:text-white hover:border-gray-500 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1513,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1498,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1443,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1442,\n                                    columnNumber: 15\n                                }, this),\n                                screenshot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"S\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1534,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Browser State\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1533,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded border border-gray-700 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: screenshot,\n                                                    alt: \"Browser screenshot\",\n                                                    className: \"w-full h-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1538,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1537,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1532,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1531,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 847,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 846,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 793,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n        lineNumber: 789,\n        columnNumber: 5\n    }, this);\n}\n_s(SparxReaderPage, \"3tPyRYBhGPqvkw99Z4exibXXewo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SparxReaderPage;\nvar _c;\n$RefreshReg$(_c, \"SparxReaderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUU0QztBQUNBO0FBRTdCLFNBQVNHOztJQUN0Qix1QkFBdUI7SUFDdkIsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHTCwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNNLE1BQU1DLFFBQVEsR0FBR1AsK0NBQVFBLENBQUM7SUFDakMsTUFBTSxDQUFDUSxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1VLFNBQVNSLDBEQUFTQTtJQUN4QixNQUFNLENBQUNTLFNBQVNDLFdBQVcsR0FBR1osK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYSxXQUFXQyxhQUFhLEdBQUdkLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2UsaUJBQWlCQyxtQkFBbUIsR0FBR2hCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ2lCLFNBQVNDLFdBQVcsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ21CLFlBQVlDLGNBQWMsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3FCLGNBQWNDLGdCQUFnQixHQUFHdEIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDdUIsc0JBQXNCQyx3QkFBd0IsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQ3lCLFlBQVlDLGNBQWMsR0FBRzFCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQzJCLFdBQVdDLGFBQWEsR0FBRzVCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzZCLHFCQUFxQkMsdUJBQXVCLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUUvRCw0QkFBNEI7SUFDNUIsTUFBTSxDQUFDK0IsaUJBQWlCQyxtQkFBbUIsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ2lDLGVBQWVDLGlCQUFpQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDbUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3FDLFdBQVdDLGFBQWEsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3VDLHFCQUFxQkMsdUJBQXVCLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUN5QyxpQkFBaUJDLG1CQUFtQixHQUFHMUMsK0NBQVFBLENBQUMsRUFBRTtJQUN6RCxNQUFNLENBQUMyQyxjQUFjQyxnQkFBZ0IsR0FBRzVDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzZDLGNBQWNDLGdCQUFnQixHQUFHOUMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDK0MsWUFBWUMsY0FBYyxHQUFHaEQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDaUQsb0JBQW9CQyxzQkFBc0IsR0FBR2xELCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ21ELGVBQWVDLGlCQUFpQixHQUFHcEQsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDcUQsT0FBT0MsU0FBUyxHQUFHdEQsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDdUQsV0FBV0MsYUFBYSxHQUFHeEQsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDeUQsYUFBYUMsZUFBZSxHQUFHMUQsK0NBQVFBLENBQUMsV0FBVywwQkFBMEI7SUFFcEYsMkJBQTJCO0lBQzNCLE1BQU0sQ0FBQzJELHFCQUFxQkMsdUJBQXVCLEdBQUc1RCwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUM2RCxnQkFBZ0JDLGtCQUFrQixHQUFHOUQsK0NBQVFBLENBQUMsVUFBVSxtQkFBbUI7SUFDbEYsTUFBTSxDQUFDK0QsWUFBWUMsY0FBYyxHQUFHaEUsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDaUUsV0FBV0MsYUFBYSxHQUFHbEUsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDbUUsY0FBY0MsZ0JBQWdCLEdBQUdwRSwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNxRSxVQUFVQyxZQUFZLEdBQUd0RSwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUN1RSxrQkFBa0JDLG9CQUFvQixHQUFHeEUsK0NBQVFBLENBQUMsRUFBRTtJQUUzRCx5QkFBeUI7SUFDekIsTUFBTSxDQUFDeUUsb0JBQW9CQyxzQkFBc0IsR0FBRzFFLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzJFLGVBQWVDLGlCQUFpQixHQUFHNUUsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDNkUsZUFBZUMsaUJBQWlCLEdBQUc5RSwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUMrRSx1QkFBdUJDLHlCQUF5QixHQUFHaEYsK0NBQVFBLENBQUM7SUFFbkUsMENBQTBDO0lBQzFDQyxnREFBU0EsQ0FBQztRQUNSZ0Y7SUFDRixHQUFHLEVBQUU7SUFFTCwrREFBK0Q7SUFDL0RoRixnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ0csbUJBQW1CLENBQUNtQyx1QkFBdUIsQ0FBQ1Usb0JBQW9CO1FBRXJFLElBQUlWLHVCQUF1QlUsb0JBQW9CO1lBQzdDLE1BQU1pQyxXQUFXQyxZQUFZO2dCQUMzQixxQ0FBcUM7Z0JBQ3JDL0Msa0JBQWtCZ0QsQ0FBQUE7b0JBQ2hCLE1BQU1DLFNBQVNELE9BQU87b0JBQ3RCOUMsYUFBYWdELENBQUFBLFVBQVdBLFVBQVVDLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLEtBQUs7b0JBQ2xFN0MsZ0JBQWdCOEMsQ0FBQUEsVUFBV0EsVUFBVTtvQkFFckMsd0JBQXdCO29CQUN4QixNQUFNQyxrQkFBa0I7d0JBQ3RCO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3dCQUNBO3FCQUNEO29CQUVELE1BQU1DLGdCQUFnQjt3QkFDcEI7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7d0JBQ0E7cUJBQ0Q7b0JBRUQsTUFBTUMsY0FBY04sS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtFLGdCQUFnQkcsTUFBTTtvQkFDckU5RCxtQkFBbUIyRCxlQUFlLENBQUNFLFlBQVk7b0JBQy9DM0QsaUJBQWlCMEQsYUFBYSxDQUFDQyxZQUFZO29CQUUzQyxpQkFBaUI7b0JBQ2pCbkQsbUJBQW1CMEMsQ0FBQUEsT0FBUTsrQkFBSUE7NEJBQU07Z0NBQ25DVyxRQUFRVjtnQ0FDUlcsVUFBVUwsZUFBZSxDQUFDRSxZQUFZO2dDQUN0Q0ksUUFBUUwsYUFBYSxDQUFDQyxZQUFZOzRCQUNwQzt5QkFBRTtvQkFFRiw4Q0FBOEM7b0JBQzlDLElBQUlSLFVBQVUsS0FBS2hELGFBQWE2RCxTQUFTdkUsYUFBYSxLQUFLO3dCQUN6RHdFLFdBQVc7NEJBQ1QzRCx1QkFBdUI7NEJBQ3ZCNUIsV0FBVzt3QkFDYixHQUFHLE9BQU8sbUNBQW1DO3dCQUM3Q3dGLGNBQWNsQjt3QkFDZCxPQUFPRztvQkFDVDtvQkFFQSxPQUFPQTtnQkFDVDtZQUNGLEdBQUcsT0FBTyxzQ0FBc0M7WUFFaEQsT0FBTyxJQUFNZSxjQUFjbEI7UUFDN0I7SUFDRixHQUFHO1FBQUM5RTtRQUFpQm1DO1FBQXFCVTtRQUFvQlo7UUFBV1Y7S0FBVTtJQUVuRiwyQkFBMkI7SUFDM0IsTUFBTXNELHNCQUFzQjtRQUMxQixJQUFJO1lBQ0YsTUFBTW9CLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxJQUFJLENBQUNGLE9BQU87Z0JBQ1YzRixPQUFPOEYsSUFBSSxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sc0JBQXNCO2dCQUNqREMsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTk47Z0JBQzdCO1lBQ0Y7WUFFQSxNQUFNTyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7WUFFaEMsSUFBSUQsS0FBS0UsT0FBTyxJQUFJRixLQUFLRyxLQUFLLEVBQUU7Z0JBQzlCMUcsbUJBQW1CO2dCQUNuQkUsUUFBUXFHLEtBQUt0RyxJQUFJO2dCQUNqQnNFLGlCQUFpQmdDLEtBQUtqQyxhQUFhO1lBQ3JDLE9BQU87Z0JBQ0wyQixhQUFhVSxVQUFVLENBQUM7Z0JBQ3hCVixhQUFhVSxVQUFVLENBQUM7Z0JBQ3hCdEcsT0FBTzhGLElBQUksQ0FBQztZQUNkO1FBQ0YsRUFBRSxPQUFPUyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxzQkFBc0JBO1lBQ3BDWCxhQUFhVSxVQUFVLENBQUM7WUFDeEJWLGFBQWFVLFVBQVUsQ0FBQztZQUN4QnRHLE9BQU84RixJQUFJLENBQUM7UUFDZCxTQUFVO1lBQ1IvRixlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxNQUFNMEcsZUFBZTtRQUNuQixJQUFJO1lBQ0YsTUFBTWQsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO1lBQ25DLElBQUlGLE9BQU87Z0JBQ1QsTUFBTUssTUFBTSxvQkFBb0I7b0JBQzlCVSxRQUFRO29CQUNSVCxTQUFTO3dCQUNQLGlCQUFpQixVQUFnQixPQUFOTjtvQkFDN0I7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT1ksT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUJBQWlCQTtRQUNqQyxTQUFVO1lBQ1JYLGFBQWFVLFVBQVUsQ0FBQztZQUN4QlYsYUFBYVUsVUFBVSxDQUFDO1lBQ3hCdEcsT0FBTzhGLElBQUksQ0FBQztRQUNkO0lBQ0Y7SUFFQSxvREFBb0Q7SUFDcEQsSUFBSWhHLGFBQWE7UUFDZixxQkFDRSw4REFBQzZHO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNDOzRCQUFFRCxXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEsaURBQWlEO0lBQ2pELElBQUksQ0FBQ2xILGlCQUFpQjtRQUNwQixPQUFPO0lBQ1Q7SUFFQSw0REFBNEQ7SUFDNUQsTUFBTW9ILDBCQUEwQixDQUFDSjtRQUMvQjFELGVBQWUwRDtJQUNqQjtJQUVBLDZDQUE2QztJQUM3QyxNQUFNSyx1QkFBdUI7UUFDM0IsNENBQTRDO1FBQzVDLElBQUluSCxRQUFRQSxLQUFLb0gsSUFBSSxLQUFLLFNBQVM7WUFDakMsT0FBTztnQkFBRVgsT0FBTztZQUFLO1FBQ3ZCO1FBRUEsSUFBSSxDQUFDcEMsZUFBZTtZQUNsQixPQUFPO2dCQUFFb0MsT0FBTztnQkFBT0UsT0FBTztZQUFvQztRQUNwRTtRQUVBLElBQUl0QyxjQUFjZ0QsY0FBYyxLQUFLLFNBQVM7WUFDNUMsSUFBSUMsZUFBZTtZQUNuQixPQUFRakQsY0FBY2dELGNBQWM7Z0JBQ2xDLEtBQUs7b0JBQ0hDLGVBQWU7b0JBQ2Y7Z0JBQ0YsS0FBSztvQkFDSEEsZUFBZTtvQkFDZjtnQkFDRixLQUFLO29CQUNIQSxlQUFlO29CQUNmO1lBQ0o7WUFDQSxPQUFPO2dCQUFFYixPQUFPO2dCQUFPRSxPQUFPVztnQkFBY0MsUUFBUWxELGNBQWNnRCxjQUFjO1lBQUM7UUFDbkY7UUFFQSxPQUFPO1lBQUVaLE9BQU87UUFBSztJQUN2QjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNZSx1QkFBdUI7UUFDM0IsSUFBSSxDQUFDakQsY0FBY2tELElBQUksSUFBSTtZQUN6Qm5ILFdBQVc7WUFDWDtRQUNGO1FBRUFvRSx5QkFBeUI7UUFDekJwRSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU15RixRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkMsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLDJCQUEyQjtnQkFDdERVLFFBQVE7Z0JBQ1JULFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUIsVUFBZ0IsT0FBTk47Z0JBQzdCO2dCQUNBMkIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUFFckQsZUFBZUEsY0FBY2tELElBQUk7Z0JBQUc7WUFDN0Q7WUFFQSxNQUFNbkIsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEJsRyxXQUFXO2dCQUNYOEQsc0JBQXNCO2dCQUN0QkksaUJBQWlCO2dCQUVqQix1REFBdUQ7Z0JBQ3ZELE1BQU1HO1lBQ1IsT0FBTztnQkFDTHJFLFdBQVdnRyxLQUFLSyxLQUFLLElBQUk7WUFDM0I7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZHJHLFdBQVc7UUFDYixTQUFVO1lBQ1JvRSx5QkFBeUI7UUFDM0I7SUFDRjtJQUVBLGtDQUFrQztJQUNsQyxNQUFNbUQsbUJBQW1CO1FBQ3ZCLHVDQUF1QztRQUN2QyxJQUFJLENBQUMvSCxtQkFBbUIsQ0FBQ0UsTUFBTTtZQUM3Qk0sV0FBVztZQUNYRixPQUFPOEYsSUFBSSxDQUFDO1lBQ1o7UUFDRjtRQUVBLDJDQUEyQztRQUMzQyxNQUFNNEIsZUFBZVg7UUFDckIsSUFBSSxDQUFDVyxhQUFhckIsS0FBSyxFQUFFO1lBQ3ZCbkcsV0FBV3dILGFBQWFuQixLQUFLO1lBQzdCdkMsc0JBQXNCO1lBQ3RCO1FBQ0Y7UUFFQSxtQkFBbUI7UUFDbkI5RCxXQUFXO1FBQ1hFLGFBQWE7UUFDYkUsbUJBQW1CO1FBQ25CSSxjQUFjO1FBQ2RFLGdCQUFnQjtRQUNoQkUsd0JBQXdCO1FBQ3hCRSxjQUFjO1FBQ2RFLGFBQWE7UUFDYkUsdUJBQXVCO1FBQ3ZCb0Isc0JBQXNCO1FBRXRCLDBCQUEwQjtRQUMxQmMsY0FBYztRQUNkRSxhQUFhO1FBQ2JFLGdCQUFnQjtRQUNoQkUsWUFBWTtRQUVaLHlDQUF5QztRQUN6Q3RCLGNBQWM7UUFFZCw4QkFBOEI7UUFDOUJZLHVCQUF1QjtRQUV2QixnQ0FBZ0M7UUFDaEMsTUFBTXlFO0lBQ1I7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTUEsdUJBQXVCO1FBQzNCLElBQUk7WUFDRixNQUFNaEMsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO1lBQ25DLE1BQU1FLFdBQVcsTUFBTUMsTUFBTSx5QkFBeUI7Z0JBQ3BEQyxTQUFTO29CQUNQLGlCQUFpQixVQUFnQixPQUFOTjtnQkFDN0I7WUFDRjtZQUNBLE1BQU1PLE9BQU8sTUFBTUgsU0FBU0ksSUFBSTtZQUVoQyxJQUFJRCxLQUFLRSxPQUFPLEVBQUU7Z0JBQ2hCdEMsb0JBQW9Cb0MsS0FBSzBCLFdBQVc7WUFDdEM7UUFDRixFQUFFLE9BQU9yQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQ0FBcUNBO1FBQ3JEO0lBQ0Y7SUFFQSwrQkFBK0I7SUFDL0IsTUFBTXNCLHlCQUF5QjtRQUM3QixJQUFJMUUsbUJBQW1CLE9BQU87WUFDNUIsdUNBQXVDO1lBQ3ZDLElBQUksQ0FBQ1EsVUFBVTtnQkFDYnpELFdBQVc7Z0JBQ1g7WUFDRjtZQUVBLElBQUk7Z0JBQ0YsTUFBTXlGLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztnQkFDbkMsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLHdCQUF3QjtvQkFDbkRVLFFBQVE7b0JBQ1JULFNBQVM7d0JBQ1AsZ0JBQWdCO3dCQUNoQixpQkFBaUIsVUFBZ0IsT0FBTk47b0JBQzdCO29CQUNBMkIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO3dCQUFFN0Q7b0JBQVM7Z0JBQ2xDO2dCQUVBLE1BQU11QyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtvQkFDaEIsa0RBQWtEO29CQUNsRGxELHVCQUF1QjtvQkFDdkJoRCxXQUFXO29CQUNYa0IsdUJBQXVCO2dCQUN6QixPQUFPO29CQUNMbEIsV0FBV2dHLEtBQUtLLEtBQUssSUFBSTtnQkFDM0I7WUFDRixFQUFFLE9BQU9BLE9BQU87Z0JBQ2RyRyxXQUFXO1lBQ2I7UUFDRixPQUFPO1lBQ0wsMEJBQTBCO1lBQzFCLElBQUksQ0FBQ21ELGNBQWMsQ0FBQ0UsYUFBYSxDQUFDRSxjQUFjO2dCQUM5Q3ZELFdBQVc7Z0JBQ1g7WUFDRjtZQUVBLHdDQUF3QztZQUN4QyxJQUFJNEgsUUFBUSxrR0FBa0c7Z0JBQzVHLElBQUk7b0JBQ0YsTUFBTW5DLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztvQkFDbkMsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLHlCQUF5Qjt3QkFDcERVLFFBQVE7d0JBQ1JULFNBQVM7NEJBQ1AsZ0JBQWdCOzRCQUNoQixpQkFBaUIsVUFBZ0IsT0FBTk47d0JBQzdCO3dCQUNBMkIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDOzRCQUNuQnpFOzRCQUNBZ0YsUUFBUTFFOzRCQUNSMkUsT0FBT3pFOzRCQUNQMEUsVUFBVXhFO3dCQUNaO29CQUNGO29CQUVBLE1BQU15QyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7b0JBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTt3QkFDaEI4QixNQUFNLHlDQUF1RCxPQUFkaEMsS0FBS3ZDLFFBQVEsRUFBQztvQkFDL0Q7Z0JBQ0YsRUFBRSxPQUFPNEMsT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7Z0JBQy9DO1lBQ0Y7WUFFQSwwQkFBMEI7WUFDMUJyRCx1QkFBdUI7WUFDdkJoRCxXQUFXO1lBQ1hrQix1QkFBdUI7UUFDekI7SUFDRjtJQU1BLE1BQU0rRyxrQkFBa0I7UUFDdEIscUJBQXFCO1FBQ3JCLElBQUksQ0FBQ2xILGFBQWFtSCxNQUFNbkgsY0FBY3VFLFNBQVN2RSxjQUFjLEdBQUc7WUFDOURmLFdBQVc7WUFDWDtRQUNGO1FBRUEsMERBQTBEO1FBQzFELE1BQU13SCxlQUFlWDtRQUNyQixJQUFJLENBQUNXLGFBQWFyQixLQUFLLEVBQUU7WUFDdkJuRyxXQUFXd0gsYUFBYW5CLEtBQUs7WUFDN0J2QyxzQkFBc0I7WUFDdEI1Qyx1QkFBdUI7WUFDdkI7UUFDRjtRQUVBWixXQUFXO1FBQ1hZLHVCQUF1QjtRQUN2QixNQUFNaUgsZ0JBQWdCdEYsZ0JBQWdCO1FBQ3RDLE1BQU11RixtQkFBbUJ2RixnQkFBZ0I7UUFDekMsTUFBTXdGLGdCQUFnQnhGLGdCQUFnQjtRQUV0QyxrQkFBa0I7UUFDbEIsSUFBSTZFLGNBQWM7UUFDbEIsSUFBSXpFLG1CQUFtQixTQUFTUSxVQUFVO1lBQ3hDLGlDQUFpQztZQUNqQyxJQUFJO2dCQUNGLE1BQU1nQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ25DLE1BQU1FLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7b0JBQ25EVSxRQUFRO29CQUNSVCxTQUFTO3dCQUNQLGdCQUFnQjt3QkFDaEIsaUJBQWlCLFVBQWdCLE9BQU5OO29CQUM3QjtvQkFDQTJCLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRTdEO29CQUFTO2dCQUNsQztnQkFFQSxNQUFNdUMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQyxJQUFJRCxLQUFLRSxPQUFPLEVBQUU7b0JBQ2hCd0IsY0FBYzFCLEtBQUswQixXQUFXO2dCQUNoQyxPQUFPO29CQUNMcEgsV0FBVztvQkFDWE4sV0FBV2dHLEtBQUtLLEtBQUssSUFBSTtvQkFDekI7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9BLE9BQU87Z0JBQ2QvRixXQUFXO2dCQUNYTixXQUFXO2dCQUNYO1lBQ0Y7UUFDRixPQUFPLElBQUlpRCxtQkFBbUIsV0FBV0UsY0FBY0UsYUFBYUUsY0FBYztZQUNoRiwwQkFBMEI7WUFDMUJtRSxjQUFjO2dCQUNaRyxRQUFRMUU7Z0JBQ1IyRSxPQUFPekU7Z0JBQ1AwRSxVQUFVeEU7Z0JBQ1ZWLGFBQWFBO1lBQ2Y7UUFDRixPQUFPO1lBQ0x2QyxXQUFXO1lBQ1hOLFdBQVc7WUFDWDtRQUNGO1FBRUEsSUFBSW1JLGVBQWU7WUFDakJuSSxXQUFXO1FBQ2IsT0FBTyxJQUFJb0ksa0JBQWtCO1lBQzNCcEksV0FBVztRQUNiLE9BQU8sSUFBSXFJLGVBQWU7WUFDeEJySSxXQUFXO1FBQ2I7UUFFQSxJQUFJO1lBQ0YsSUFBSXNJLGFBQWFDO1lBRWpCLElBQUlKLGVBQWU7Z0JBQ2pCRyxjQUFjO2dCQUNkQyxjQUFjO29CQUNaQyxLQUFLO29CQUNMekgsV0FBV3VFLFNBQVN2RTtvQkFDcEIyRyxhQUFhQTtnQkFDZjtZQUNGLE9BQU8sSUFBSVUsa0JBQWtCO2dCQUMzQkUsY0FBYztnQkFDZEMsY0FBYztvQkFDWkMsS0FBSztvQkFDTHpILFdBQVd1RSxTQUFTdkU7b0JBQ3BCMkcsYUFBYUE7Z0JBQ2Y7WUFDRixPQUFPLElBQUlXLGVBQWU7Z0JBQ3hCQyxjQUFjO2dCQUNkQyxjQUFjO29CQUNaQyxLQUFLO29CQUNMekgsV0FBV3VFLFNBQVN2RTtvQkFDcEIyRyxhQUFhQTtnQkFDZjtZQUNGO1lBRUEsTUFBTWpDLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxNQUFNRSxXQUFXLE1BQU1DLE1BQU13QyxhQUFhO2dCQUN4QzlCLFFBQVE7Z0JBQ1JULFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUIsVUFBZ0IsT0FBTk47Z0JBQzdCO2dCQUNBMkIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDaUI7WUFDdkI7WUFDQSxNQUFNdkMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEIseURBQXlEO2dCQUN6RGxHLFdBQVcsNENBQXNELE9BQVZlLFdBQVU7Z0JBQ2pFYixhQUFhOEYsS0FBSy9GLFNBQVM7Z0JBQzNCYSxjQUFja0YsS0FBS25GLFVBQVU7Z0JBQzdCRCx3QkFBd0I7Z0JBQ3hCLElBQUlvRixLQUFLekYsVUFBVSxFQUFFO29CQUNuQkMsY0FBY3dGLEtBQUt6RixVQUFVO2dCQUMvQjtZQUNGLE9BQU87Z0JBQ0xQLFdBQVdnRyxLQUFLSyxLQUFLLElBQUk7Z0JBQ3pCLElBQUlMLEtBQUs3RixlQUFlLEVBQUU7b0JBQ3hCQyxtQkFBbUI7Z0JBQ3JCO1lBQ0Y7UUFDRixFQUFFLE9BQU9pRyxPQUFPO1lBQ2RyRyxXQUFXO1FBQ2IsU0FBVTtZQUNSTSxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1tSSxpQkFBaUI7UUFDckJuSSxXQUFXO1FBQ1hNLHdCQUF3QjtRQUN4QmdDLGFBQWE7UUFDYjVDLFdBQVc7UUFFWCxJQUFJO1lBQ0YsTUFBTXlGLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUVuQyxrREFBa0Q7WUFDbEQsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLG1CQUFtQjtnQkFDOUNVLFFBQVE7Z0JBQ1JULFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUIsVUFBZ0IsT0FBTk47Z0JBQzdCO2dCQUNBMkIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQm9CLFVBQVU7b0JBQ1ZDLFVBQVU7d0JBQ1JkLFFBQVFlO3dCQUNSZCxPQUFPQTt3QkFDUGUsVUFBVUE7d0JBQ1ZkLFVBQVVBO3dCQUNWZSxZQUFZakc7d0JBQ1o1QyxXQUFXQTtvQkFDYjtvQkFDQThJLFlBQVloSSxZQUFZdUUsU0FBU3ZFLGFBQWE7b0JBQzlDaUksVUFBVTtnQkFDWjtZQUNGO1lBRUEsTUFBTWhELE9BQU8sTUFBTUgsU0FBU0ksSUFBSTtZQUVoQyxJQUFJRCxLQUFLRSxPQUFPLEVBQUU7Z0JBQ2hCeEQsU0FBU3NELEtBQUtpRCxNQUFNO2dCQUNwQmpKLFdBQVc7Z0JBRVgsa0NBQWtDO2dCQUNsQ2tKLHFCQUFxQmxELEtBQUtpRCxNQUFNO1lBQ2xDLE9BQU87Z0JBQ0xyRyxhQUFhO2dCQUNiNUMsV0FBV2dHLEtBQUtLLEtBQUssSUFBSTtZQUMzQjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkekQsYUFBYTtZQUNiNUMsV0FBVztRQUNiLFNBQVU7WUFDUk0sV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNNEksdUJBQXVCLENBQUN6RztRQUM1QixNQUFNMEcsZ0JBQWdCO1lBQ3BCLElBQUk7Z0JBQ0YsTUFBTTFELFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztnQkFDbkMsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLDhCQUFvQyxPQUFOckQsUUFBUztvQkFDbEVzRCxTQUFTO3dCQUFFLGlCQUFpQixVQUFnQixPQUFOTjtvQkFBUTtnQkFDaEQ7Z0JBQ0EsTUFBTU8sT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUVoQyxJQUFJRCxLQUFLRSxPQUFPLEVBQUU7b0JBQ2hCMUQsaUJBQWlCd0QsS0FBS29ELFFBQVE7b0JBRTlCLElBQUlwRCxLQUFLb0QsUUFBUSxLQUFLLEtBQUtwRCxLQUFLaUIsTUFBTSxLQUFLLGNBQWM7d0JBQ3ZELDhCQUE4Qjt3QkFDOUIvRSxnQkFBZ0I7d0JBQ2hCVSxhQUFhO3dCQUNiNUMsV0FBVzt3QkFFWCxrQ0FBa0M7d0JBQ2xDcUosbUJBQW1CNUc7b0JBQ3JCLE9BQU8sSUFBSXVELEtBQUtpQixNQUFNLEtBQUssYUFBYTt3QkFDdEMsZ0JBQWdCO3dCQUNoQnFDLG9CQUFvQnRELEtBQUt1RCxNQUFNO29CQUNqQyxPQUFPLElBQUl2RCxLQUFLaUIsTUFBTSxLQUFLLFVBQVU7d0JBQ25DLGFBQWE7d0JBQ2JyRSxhQUFhO3dCQUNiVixnQkFBZ0I7d0JBQ2hCbEMsV0FBV2dHLEtBQUtLLEtBQUssSUFBSTtvQkFDM0IsT0FBTzt3QkFDTCxpQkFBaUI7d0JBQ2pCckcsV0FBVyxvQkFBa0MsT0FBZGdHLEtBQUtvRCxRQUFRO3dCQUM1QywyQkFBMkI7d0JBQzNCN0QsV0FBVzRELGVBQWU7b0JBQzVCO2dCQUNGO1lBQ0YsRUFBRSxPQUFPOUMsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7Z0JBQ2hEZCxXQUFXNEQsZUFBZSxPQUFPLHFCQUFxQjtZQUN4RDtRQUNGO1FBRUFBO0lBQ0Y7SUFFQSxNQUFNRSxxQkFBcUIsQ0FBQzVHO1FBQzFCLE1BQU0rRyxpQkFBaUI7WUFDckIsSUFBSTtnQkFDRixNQUFNL0QsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO2dCQUNuQyxNQUFNRSxXQUFXLE1BQU1DLE1BQU0sbUJBQXlCLE9BQU5yRCxRQUFTO29CQUN2RHNELFNBQVM7d0JBQUUsaUJBQWlCLFVBQWdCLE9BQU5OO29CQUFRO2dCQUNoRDtnQkFDQSxNQUFNTyxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtvQkFDaEIsSUFBSUYsS0FBS3lELEdBQUcsQ0FBQ3hDLE1BQU0sS0FBSyxhQUFhO3dCQUNuQ3FDLG9CQUFvQnRELEtBQUt5RCxHQUFHLENBQUNGLE1BQU07b0JBQ3JDLE9BQU8sSUFBSXZELEtBQUt5RCxHQUFHLENBQUN4QyxNQUFNLEtBQUssVUFBVTt3QkFDdkMvRSxnQkFBZ0I7d0JBQ2hCbEMsV0FBV2dHLEtBQUt5RCxHQUFHLENBQUNwRCxLQUFLLElBQUk7b0JBQy9CLE9BQU87d0JBQ0wsZ0NBQWdDO3dCQUNoQ2QsV0FBV2lFLGdCQUFnQjtvQkFDN0I7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9uRCxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtnQkFDNUNkLFdBQVdpRSxnQkFBZ0I7WUFDN0I7UUFDRjtRQUVBQTtJQUNGO0lBRUEsTUFBTUYsc0JBQXNCLENBQUNDO1FBQzNCckgsZ0JBQWdCO1FBQ2hCVSxhQUFhO1FBRWIsSUFBSTJHLFVBQVVBLE9BQU9yRCxPQUFPLEVBQUU7WUFDNUJsRyxXQUFXO1lBQ1hVLGdCQUFnQjZJLE9BQU85SSxZQUFZO1lBQ25DUCxhQUFhcUosT0FBT3RKLFNBQVM7WUFFN0IsZ0NBQWdDO1lBQ2hDc0YsV0FBVztnQkFDVGpELHNCQUFzQjtnQkFDdEJWLHVCQUF1QjtnQkFDdkJGLGFBQWE7Z0JBQ2JGLGtCQUFrQjtnQkFDbEJNLG1CQUFtQixFQUFFO2dCQUNyQjlCLFdBQVc7WUFDYixHQUFHO1lBRUgsSUFBSXVKLE9BQU9oSixVQUFVLEVBQUU7Z0JBQ3JCQyxjQUFjK0ksT0FBT2hKLFVBQVU7WUFDakM7UUFDRixPQUFPO1lBQ0xQLFdBQVd1SixDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFsRCxLQUFLLEtBQUk7UUFDOUI7SUFDRjtJQUVBLE1BQU1xRCxnQkFBZ0I7UUFDcEJwSixXQUFXO1FBQ1hOLFdBQVc7UUFFWCxJQUFJO1lBQ0Ysd0JBQXdCO1lBQ3hCLE1BQU15RixRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkMsTUFBTUcsTUFBTSwwQkFBMEI7Z0JBQ3BDVSxRQUFRO2dCQUNSVCxTQUFTO29CQUNQLGlCQUFpQixVQUFnQixPQUFOTjtnQkFDN0I7WUFDRjtZQUVBLDhDQUE4QztZQUM5Q25GLFdBQVc7WUFDWFksdUJBQXVCO1lBQ3ZCbEIsV0FBVztZQUVYLElBQUk7Z0JBQ0YsTUFBTXlGLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztnQkFDbkMsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLDBCQUEwQjtvQkFDckRVLFFBQVE7b0JBQ1JULFNBQVM7d0JBQ1AsZ0JBQWdCO3dCQUNoQixpQkFBaUIsVUFBZ0IsT0FBTk47b0JBQzdCO29CQUNBMkIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQmtCLEtBQUs7d0JBQ0x6SCxXQUFXdUUsU0FBU3ZFO29CQUN0QjtnQkFDRjtnQkFDQSxNQUFNaUYsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUVoQyxJQUFJRCxLQUFLRSxPQUFPLEVBQUU7b0JBQ2hCbEcsV0FBVyw0Q0FBc0QsT0FBVmUsV0FBVTtvQkFDakViLGFBQWE4RixLQUFLL0YsU0FBUztvQkFDM0JhLGNBQWNrRixLQUFLbkYsVUFBVTtvQkFDN0JELHdCQUF3QjtvQkFDeEIsSUFBSW9GLEtBQUt6RixVQUFVLEVBQUU7d0JBQ25CQyxjQUFjd0YsS0FBS3pGLFVBQVU7b0JBQy9CO2dCQUNGLE9BQU87b0JBQ0xQLFdBQVdnRyxLQUFLSyxLQUFLLElBQUk7b0JBQ3pCLElBQUlMLEtBQUs3RixlQUFlLEVBQUU7d0JBQ3hCQyxtQkFBbUI7b0JBQ3JCO2dCQUNGO1lBQ0YsRUFBRSxPQUFPaUcsT0FBTztnQkFDZHJHLFdBQVc7WUFDYjtRQUNGLEVBQUUsT0FBT3FHLE9BQU87WUFDZHJHLFdBQVc7UUFDYixTQUFVO1lBQ1JNLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXFKLGtCQUFrQjtRQUN0Qi9ILHVCQUF1QjtRQUN2Qk0sZ0JBQWdCO1FBQ2hCZCxtQkFBbUI7UUFDbkJFLGlCQUFpQjtRQUNqQkUsa0JBQWtCO1FBQ2xCRSxhQUFhO1FBQ2JJLG1CQUFtQixFQUFFO1FBQ3JCOUIsV0FBVztRQUNYWSx3QkFBd0I7UUFDeEJNLHVCQUF1QjtRQUN2QmtCLGNBQWM7UUFDZEUsc0JBQXNCO0lBQ3hCO0lBRUEsb0RBQW9EO0lBQ3BELElBQUkxQyxhQUFhO1FBQ2YscUJBQ0UsOERBQUM2RztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFDZiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDQzs0QkFBRUQsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUlyQztJQUVBLGlEQUFpRDtJQUNqRCxJQUFJLENBQUNsSCxpQkFBaUI7UUFDcEIsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNpSDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7OzswQkFFZiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDa0Q7d0JBQU9sRCxXQUFVO2tDQUNoQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7O2dDQUVYL0UsQ0FBQUEsdUJBQXVCTSxZQUFXLG1CQUNsQyw4REFBQzRIO29DQUNDQyxTQUFTSDtvQ0FDVGpELFdBQVU7O3NEQUVWLDhEQUFDcUQ7NENBQUtyRCxXQUFVO3NEQUFPOzs7Ozs7d0NBQVc7Ozs7Ozs7OENBTXRDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUVELFdBQVU7O3dEQUFpQzt3REFBVWhILGlCQUFBQSwyQkFBQUEsS0FBTW1KLFFBQVE7Ozs7Ozs7OERBQ3RFLDhEQUFDbEM7b0RBQUVELFdBQVU7OERBQXlCaEgsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNb0gsSUFBSSxNQUFLLFVBQVUsa0JBQWtCOzs7Ozs7Ozs7Ozs7c0RBRW5GLDhEQUFDTDs0Q0FBSUMsV0FBVTs7Z0RBQ1poSCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1vSCxJQUFJLE1BQUsseUJBQ2QsOERBQUMrQztvREFDQ0MsU0FBUyxJQUFNaEssT0FBTzhGLElBQUksQ0FBQztvREFDM0JjLFdBQVU7OERBQ1g7Ozs7Ozs4REFJSCw4REFBQ21EO29EQUNDQyxTQUFTLElBQU1oSyxPQUFPOEYsSUFBSSxDQUFDO29EQUMzQmMsV0FBVTs4REFDWDs7Ozs7OzhEQUdELDhEQUFDbUQ7b0RBQ0NDLFNBQVN2RDtvREFDVEcsV0FBVTs4REFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU1MLDhEQUFDc0Q7b0NBQUd0RCxXQUFVOzhDQUE2Qzs7Ozs7OzhDQUczRCw4REFBQ0M7b0NBQUVELFdBQVU7OENBQXlDOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLMUQsOERBQUN1RDt3QkFBS3ZELFdBQVU7a0NBQ2QsNEVBQUNEOzRCQUFJQyxXQUFVOztnQ0FHWC9ELENBQUFBLGFBQWFWLFlBQVcsbUJBQ3hCLDhEQUFDd0U7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUViLDhEQUFDRDt3REFBSUMsV0FBVTs7Ozs7O2tFQUVmLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ0Q7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7O2tFQUluQiw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7OztrRUFDZiw4REFBQ0Q7d0RBQUlDLFdBQVU7d0RBQStFd0QsT0FBTzs0REFBQ0MsZ0JBQWdCO3dEQUFNOzs7Ozs7Ozs7Ozs7NENBRTdIeEgsMEJBQ0M7O2tFQUNFLDhEQUFDeUg7d0RBQUcxRCxXQUFVO2tFQUF1Rzs7Ozs7O2tFQUdySCw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQ1ZuRSxnQkFBZ0Isb0JBQWtDLE9BQWRBLGlCQUFrQjs7Ozs7O2tFQUV6RCw4REFBQ2tFO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzs7Ozs7OEVBQ2YsOERBQUNxRDtvRUFBS3JELFdBQVU7OEVBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NkVBS3RDOztrRUFDRSw4REFBQzBEO3dEQUFHMUQsV0FBVTtrRUFBcUc7Ozs7OztrRUFHbkgsOERBQUNDO3dEQUFFRCxXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMxQyw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7Ozs7Ozs4RUFDZiw4REFBQ3FEO29FQUFLckQsV0FBVTs4RUFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FVN0MvRSxxQ0FDQyw4REFBQzhFO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDMkQ7b0RBQUczRCxXQUFVOzhEQUE4Rjs7Ozs7Ozs7Ozs7MERBSzlHLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBRWIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUViLDhEQUFDNEQ7d0VBQUk1RCxXQUFVO3dFQUFpQzZELFNBQVE7OzBGQUN0RCw4REFBQ0M7Z0ZBQU9DLElBQUc7Z0ZBQUtDLElBQUc7Z0ZBQUtDLEdBQUU7Z0ZBQUtDLFFBQU87Z0ZBQWVDLGFBQVk7Z0ZBQUlDLE1BQUs7Z0ZBQWNwRSxXQUFVOzs7Ozs7MEZBQ2xHLDhEQUFDOEQ7Z0ZBQ0NDLElBQUc7Z0ZBQUtDLElBQUc7Z0ZBQUtDLEdBQUU7Z0ZBQ2xCQyxRQUFPO2dGQUNQQyxhQUFZO2dGQUNaQyxNQUFLO2dGQUNMQyxpQkFBaUIsR0FBa0QsT0FBL0MsWUFBYXpGLFNBQVN2RSxhQUFhLEtBQU0sT0FBTTtnRkFDbkUyRixXQUFVO2dGQUNWc0UsZUFBYzs7Ozs7OzBGQUVoQiw4REFBQ0M7MEZBQ0MsNEVBQUNDO29GQUFlQyxJQUFHO29GQUFjQyxJQUFHO29GQUFLQyxJQUFHO29GQUFLQyxJQUFHO29GQUFPQyxJQUFHOztzR0FDNUQsOERBQUNDOzRGQUFLQyxRQUFPOzRGQUFLQyxXQUFVOzs7Ozs7c0dBQzVCLDhEQUFDRjs0RkFBS0MsUUFBTzs0RkFBTUMsV0FBVTs7Ozs7O3NHQUM3Qiw4REFBQ0Y7NEZBQUtDLFFBQU87NEZBQU9DLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQUlwQyw4REFBQ2pGO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNxRDtvRkFBS3JELFdBQVU7OEZBQXNDakY7Ozs7Ozs4RkFDdEQsOERBQUNzSTtvRkFBS3JELFdBQVU7OEZBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztrRkFJNUMsOERBQUNEO3dFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7MEVBRWpCLDhEQUFDQztnRUFBRUQsV0FBVTswRUFBc0M7Ozs7OzswRUFDbkQsOERBQUNDO2dFQUFFRCxXQUFVOztvRUFBd0I7b0VBQVMzRjs7Ozs7OzswRUFDOUMsOERBQUMwRjtnRUFBSUMsV0FBVTs7b0VBQ1ovQixLQUFLZ0gsS0FBSyxDQUFDLFlBQWFyRyxTQUFTdkUsYUFBYSxLQUFNO29FQUFLOzs7Ozs7Ozs7Ozs7O2tFQUs5RCw4REFBQzBGO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBdURuRjs7Ozs7O2tGQUN0RSw4REFBQ2tGO3dFQUFJQyxXQUFVO2tGQUE2Qjs7Ozs7O2tGQUM1Qyw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUNEOzRFQUNDQyxXQUFVOzRFQUNWd0QsT0FBTztnRkFBRTBCLE9BQU8sR0FBOEMsT0FBM0NqSCxLQUFLa0gsR0FBRyxDQUFDLGlCQUFrQixLQUFNLEtBQUssTUFBSzs0RUFBRztzRkFFakUsNEVBQUNwRjtnRkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztrRkFHbkIsOERBQUNEO3dFQUFJQyxXQUFVOzs0RUFDWi9CLEtBQUtrSCxHQUFHLENBQUNsSCxLQUFLZ0gsS0FBSyxDQUFDLGlCQUFrQixLQUFNLE1BQU07NEVBQUs7Ozs7Ozs7Ozs7Ozs7MEVBRzVELDhEQUFDaEY7Z0VBQUVELFdBQVU7MEVBQWlDOzs7Ozs7Ozs7Ozs7a0VBSWhELDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUNEOzRFQUFJQyxXQUFVOzs7Ozs7Ozs7OztrRkFFakIsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUE2Qjs7Ozs7O2tGQUU1Qyw4REFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDRDtnRkFBSUMsV0FBVTswRkFBOEI7Ozs7OzswRkFDN0MsOERBQUNEO2dGQUFJQyxXQUFVOzBGQUE2Qjs7Ozs7OzBGQUM1Qyw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O29GQUFxQjtvRkFDL0JuRixpQkFBaUIsSUFBSW9ELEtBQUtnSCxLQUFLLENBQUNHLGNBQWN2SyxrQkFBa0I7b0ZBQUU7Ozs7Ozs7MEZBRXZFLDhEQUFDa0Y7Z0ZBQUlDLFdBQVU7O29GQUF3QjtvRkFDakNuRixpQkFBaUIsSUFBSW9ELEtBQUtnSCxLQUFLLENBQUMsaUJBQWtCRyxjQUFlLE1BQU07b0ZBQUU7Ozs7Ozs7MEZBRS9FLDhEQUFDckY7Z0ZBQUlDLFdBQVU7O29GQUErQjtvRkFDeENxRixXQUFXRDtvRkFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFJbEMsOERBQUNuRjtnRUFBRUQsV0FBVTswRUFBaUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQVF2RHZGLG1CQUFtQlEscUNBQ2xCLDhEQUFDOEU7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDWm5GOzs7Ozs7a0VBRUgsOERBQUNrRjs7MEVBQ0MsOERBQUM0RDtnRUFBRzNELFdBQVU7MEVBQThGOzs7Ozs7MEVBRzVHLDhEQUFDQztnRUFBRUQsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7OztrRUFFdkMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7Ozs7OzswRUFDZiw4REFBQ3FEO2dFQUFLckQsV0FBVTswRUFBcUM7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJekQsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDcUQ7Z0VBQUtyRCxXQUFVOzBFQUErQjs7Ozs7Ozs7Ozs7c0VBRWpELDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNDO29FQUFFRCxXQUFVOzhFQUFnRHZGOzs7Ozs7OEVBQzdELDhEQUFDc0Y7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDcUQ7NEVBQUtyRCxXQUFVOztnRkFBZ0U7Z0ZBQzVFL0IsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssS0FBSztnRkFBRTs7Ozs7OztzRkFFdEMsOERBQUNrRjs0RUFBS3JELFdBQVU7c0ZBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FNL0NyRiwrQkFDQyw4REFBQ29GO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDcUQ7Z0VBQUtyRCxXQUFVOzBFQUErQjs7Ozs7Ozs7Ozs7c0VBRWpELDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNDO29FQUFFRCxXQUFVOzhFQUFpQzs7Ozs7OzhFQUM5Qyw4REFBQ0M7b0VBQUVELFdBQVU7OEVBQWlDckY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O21DQTNDZFU7Ozs7O2dDQXFEN0MsQ0FBQ0ksNEJBQ0EsOERBQUNzRTtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQ2IsNEVBQUNxRDtvRUFBS3JELFdBQVU7OEVBQXNCOzs7Ozs7Ozs7OzswRUFFeEMsOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7MEVBQ2YsOERBQUNEO2dFQUFJQyxXQUFVO2dFQUE2RXdELE9BQU87b0VBQUNDLGdCQUFnQjtnRUFBTTs7Ozs7Ozs7Ozs7O2tFQUU1SCw4REFBQ0M7d0RBQUcxRCxXQUFVO2tFQUFxRzs7Ozs7O2tFQUduSCw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQTBCOzs7Ozs7Ozs7Ozs7MERBSXpDLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUMyRDt3REFBRzNELFdBQVU7a0VBQStHOzs7Ozs7a0VBRzdILDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNtRDtnRUFDQ0MsU0FBUyxJQUFNbEQsd0JBQXdCO2dFQUN2Q29GLFVBQVUzTDtnRUFDVnFHLFdBQVcsa0xBSVYsT0FIQzdELGdCQUFnQixXQUNaLGdIQUNBOztrRkFHTiw4REFBQ2tIO3dFQUFLckQsV0FBVTtrRkFBZTs7Ozs7O2tGQUMvQiw4REFBQ3FEO3dFQUFLckQsV0FBVTtrRkFBUzs7Ozs7O29FQUN4QjdELGdCQUFnQiwwQkFBWSw4REFBQ2tIO3dFQUFLckQsV0FBVTtrRkFBZTs7Ozs7Ozs7Ozs7OzBFQUc5RCw4REFBQ21EO2dFQUNDQyxTQUFTLElBQU1sRCx3QkFBd0I7Z0VBQ3ZDb0YsVUFBVTNMO2dFQUNWcUcsV0FBVyxrTEFJVixPQUhDN0QsZ0JBQWdCLGNBQ1osNEdBQ0E7O2tGQUdOLDhEQUFDa0g7d0VBQUtyRCxXQUFVO2tGQUFlOzs7Ozs7a0ZBQy9CLDhEQUFDcUQ7d0VBQUtyRCxXQUFVO2tGQUFTOzs7Ozs7b0VBQ3hCN0QsZ0JBQWdCLDZCQUFlLDhEQUFDa0g7d0VBQUtyRCxXQUFVO2tGQUFlOzs7Ozs7Ozs7Ozs7MEVBR2pFLDhEQUFDbUQ7Z0VBQ0NDLFNBQVMsSUFBTWxELHdCQUF3QjtnRUFDdkNvRixVQUFVM0w7Z0VBQ1ZxRyxXQUFXLGtMQUlWLE9BSEM3RCxnQkFBZ0IsV0FDWix3R0FDQTs7a0ZBR04sOERBQUNrSDt3RUFBS3JELFdBQVU7a0ZBQWU7Ozs7OztrRkFDL0IsOERBQUNxRDt3RUFBS3JELFdBQVU7a0ZBQVM7Ozs7OztvRUFDeEI3RCxnQkFBZ0IsMEJBQVksOERBQUNrSDt3RUFBS3JELFdBQVU7a0ZBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLMUQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNDOzREQUFFRCxXQUFVOztnRUFBd0I7OEVBQ3pCLDhEQUFDcUQ7b0VBQUtyRCxXQUFVOzt3RUFDdkI3RCxnQkFBZ0IsWUFBWTt3RUFDNUJBLGdCQUFnQixlQUFlO3dFQUMvQkEsZ0JBQWdCLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFJbkMsOERBQUNnSDt3REFDQ0MsU0FBU3ZDO3dEQUNUeUUsVUFBVTNMO3dEQUNWcUcsV0FBVTtrRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBU1J6RixxQ0FDQyw4REFBQ3dGO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNxRDs0REFBS3JELFdBQVU7c0VBQXFCOzs7Ozs7Ozs7OztrRUFFdkMsOERBQUMwRDt3REFBRzFELFdBQVU7a0VBQXNDOzs7Ozs7a0VBQ3BELDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7OzswREFHdkMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3VGO3dEQUNDQyxNQUFLO3dEQUNMQyxPQUFPcEw7d0RBQ1BxTCxVQUFVLENBQUNDLElBQU1yTCxhQUFhcUwsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dEQUM1Q0ksV0FBVyxDQUFDRixJQUFNQSxFQUFFRyxHQUFHLEtBQUssV0FBV3ZFO3dEQUN2Q3dFLGFBQVk7d0RBQ1ovRixXQUFVO3dEQUNWbUYsS0FBSTt3REFDSmEsU0FBUzs7Ozs7O2tFQUVYLDhEQUFDL0Y7d0RBQUVELFdBQVU7a0VBQW9DOzs7Ozs7a0VBSWpELDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNtRDtnRUFDQ0MsU0FBUzdCO2dFQUNUK0QsVUFBVTNMLFdBQVcsQ0FBQ1U7Z0VBQ3RCMkYsV0FBVTswRUFFVHJHLHdCQUNDLDhEQUFDMEo7b0VBQUtyRCxXQUFVOztzRkFDZCw4REFBQ0Q7NEVBQUlDLFdBQVU7Ozs7Ozt3RUFBMEY7Ozs7OzsyRUFHekc7Ozs7OzswRUFFTiw4REFBQ21EO2dFQUNDQyxTQUFTLElBQU01SSx1QkFBdUI7Z0VBQ3RDOEssVUFBVTNMO2dFQUNWcUcsV0FBVTswRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBVVYzRCxxQ0FDQyw4REFBQzBEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNxRDs0REFBS3JELFdBQVU7c0VBQXFCOzs7Ozs7Ozs7OztrRUFFdkMsOERBQUMwRDt3REFBRzFELFdBQVU7a0VBQXNDOzs7Ozs7a0VBQ3BELDhEQUFDQzt3REFBRUQsV0FBVTs7NERBQ1Y3RCxnQkFBZ0IsWUFBWTs0REFDNUJBLGdCQUFnQixlQUFlOzREQUMvQkEsZ0JBQWdCLFlBQVk7Ozs7Ozs7Ozs7Ozs7MERBS2pDLDhEQUFDNEQ7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ21EOzREQUNDQyxTQUFTLElBQU01RyxrQkFBa0I7NERBQ2pDd0QsV0FBVywrREFJVixPQUhDekQsbUJBQW1CLFVBQ2YsMkJBQ0E7c0VBRVA7Ozs7OztzRUFHRCw4REFBQzRHOzREQUNDQyxTQUFTLElBQU01RyxrQkFBa0I7NERBQ2pDd0QsV0FBVywrREFJVixPQUhDekQsbUJBQW1CLFFBQ2YsMkJBQ0E7c0VBRVA7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQU1KQSxtQkFBbUIsVUFDbEIsMEJBQTBCLGlCQUMxQiw4REFBQ3dEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3VGO3dEQUNDQyxNQUFLO3dEQUNMQyxPQUFPaEo7d0RBQ1BpSixVQUFVLENBQUNDLElBQU1qSixjQUFjaUosRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dEQUM3Q00sYUFBWTt3REFDWi9GLFdBQVU7d0RBQ1ZnRyxTQUFTOzs7Ozs7a0VBRVgsOERBQUNUO3dEQUNDQyxNQUFLO3dEQUNMQyxPQUFPOUk7d0RBQ1ArSSxVQUFVLENBQUNDLElBQU0vSSxhQUFhK0ksRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dEQUM1Q00sYUFBWTt3REFDWi9GLFdBQVU7Ozs7OztrRUFFWiw4REFBQ3VGO3dEQUNDQyxNQUFLO3dEQUNMQyxPQUFPNUk7d0RBQ1A2SSxVQUFVLENBQUNDLElBQU03SSxnQkFBZ0I2SSxFQUFFQyxNQUFNLENBQUNILEtBQUs7d0RBQy9DTSxhQUFZO3dEQUNaL0YsV0FBVTs7Ozs7O2tFQUVaLDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7O3VEQUt2QyxzQkFBc0IsaUJBQ3RCLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUN1Rjt3REFDQ0MsTUFBSzt3REFDTEMsT0FBTzFJO3dEQUNQMkksVUFBVSxDQUFDQyxJQUFNM0ksWUFBWTJJLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3REFDM0NNLGFBQVk7d0RBQ1ovRixXQUFVO3dEQUNWZ0csU0FBUzs7Ozs7O29EQUdWL0ksaUJBQWlCdUIsTUFBTSxHQUFHLG1CQUN6Qiw4REFBQ3VCOzswRUFDQyw4REFBQ0U7Z0VBQUVELFdBQVU7MEVBQTZCOzs7Ozs7MEVBQzFDLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDWi9DLGlCQUFpQmdKLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDM0IsOERBQUNoRDt3RUFFQ0MsU0FBUyxJQUFNcEcsWUFBWWtKLEtBQUtuSixRQUFRO3dFQUN4Q2lELFdBQVU7OzBGQUVWLDhEQUFDcUQ7Z0ZBQUtyRCxXQUFVOzBGQUEyQmtHLEtBQUtuSixRQUFROzs7Ozs7MEZBQ3hELDhEQUFDc0c7Z0ZBQUtyRCxXQUFVOztvRkFBZTtvRkFBRWtHLEtBQUsvSixXQUFXO29GQUFDOzs7Ozs7Ozt1RUFMN0NnSzs7Ozs7Ozs7Ozs7Ozs7OztrRUFZZiw4REFBQ2xHO3dEQUFFRCxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7OzBEQU16Qyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDbUQ7d0RBQ0NDLFNBQVNuQzt3REFDVHFFLFVBQVUzTDt3REFDVnFHLFdBQVU7a0VBRVRyRyx3QkFDQyw4REFBQzBKOzREQUFLckQsV0FBVTs7OEVBQ2QsOERBQUNEO29FQUFJQyxXQUFVOzs7Ozs7Z0VBQTBGOzs7Ozs7bUVBR3pHOzs7Ozs7a0VBRU4sOERBQUNtRDt3REFDQ0MsU0FBUzs0REFDUDlHLHVCQUF1Qjs0REFDdkJaLGNBQWM7d0RBQ2hCO3dEQUNBNEosVUFBVTNMO3dEQUNWcUcsV0FBVTtrRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBU1IvRix3QkFBd0JWLDJCQUN2Qiw4REFBQ3dHO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNxRDs0REFBS3JELFdBQVU7c0VBQXFCOzs7Ozs7Ozs7OztrRUFFdkMsOERBQUMwRDt3REFBRzFELFdBQVU7a0VBQXNDOzs7Ozs7a0VBQ3BELDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7OzswREFHdkMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzJEO3dEQUFHM0QsV0FBVTtrRUFBdUN6Rzs7Ozs7O2tFQUNyRCw4REFBQ3dHO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7O2tGQUNDLDhEQUFDRTt3RUFBRUQsV0FBVTtrRkFBd0I7Ozs7OztrRkFDckMsOERBQUNDO3dFQUFFRCxXQUFVO2tGQUFxQzdGOzs7Ozs7Ozs7Ozs7MEVBRXBELDhEQUFDNEY7O2tGQUNDLDhEQUFDRTt3RUFBRUQsV0FBVTtrRkFBd0I7Ozs7OztrRkFDckMsOERBQUNDO3dFQUFFRCxXQUFVO2tGQUFxQzNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBS3hELDhEQUFDMEY7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDbUQ7d0RBQ0NDLFNBQVNyQjt3REFDVHVELFVBQVUzTDt3REFDVnFHLFdBQVU7a0VBRVRyRyx3QkFDQyw4REFBQzBKOzREQUFLckQsV0FBVTs7OEVBQ2QsOERBQUNEO29FQUFJQyxXQUFVOzs7Ozs7Z0VBQTBGOzs7Ozs7bUVBR3pHOzs7Ozs7a0VBRU4sOERBQUNtRDt3REFDQ0MsU0FBU0o7d0RBQ1RzQyxVQUFVM0w7d0RBQ1ZxRyxXQUFVO2tFQUVUckcsVUFBVSxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FRbkN3QixnQkFBZ0JxRCxNQUFNLEdBQUcsS0FBS3ZELHFDQUM3Qiw4REFBQzhFO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUMyRDtnREFBRzNELFdBQVU7O2tFQUNaLDhEQUFDcUQ7d0RBQUtyRCxXQUFVO2tFQUFPOzs7Ozs7b0RBQVE7Ozs7Ozs7MERBR2pDLDhEQUFDRDtnREFBSUMsV0FBVTswREFDWjdFLGdCQUFnQmlMLEtBQUssQ0FBQyxDQUFDLEdBQUdILEdBQUcsQ0FBQyxDQUFDSSxNQUFNRixzQkFDcEMsOERBQUNwRzt3REFBZ0JDLFdBQVU7OzBFQUN6Qiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDcUQ7d0VBQUtyRCxXQUFVOzs0RUFBb0M7NEVBQUVxRyxLQUFLNUgsTUFBTTs7Ozs7OztrRkFDakUsOERBQUM0RTt3RUFBS3JELFdBQVU7a0ZBQXdCOzs7Ozs7Ozs7Ozs7MEVBRTFDLDhEQUFDQztnRUFBRUQsV0FBVTs7b0VBQTJCcUcsS0FBSzNILFFBQVEsQ0FBQzRILFNBQVMsQ0FBQyxHQUFHO29FQUFLOzs7Ozs7OzBFQUN4RSw4REFBQ3JHO2dFQUFFRCxXQUFVOztvRUFBd0I7b0VBQVNxRyxLQUFLMUgsTUFBTTs7Ozs7Ozs7dURBTmpEd0g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FlbkI5TSxXQUFXLENBQUNrQix1QkFBdUIsQ0FBQ04sd0JBQXdCLENBQUNzQiw4QkFDNUQsOERBQUN3RTtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNDOzRDQUFFRCxXQUFVO3NEQUFjM0c7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBTWhDSSxpQ0FDQyw4REFBQ3NHO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNxRDs0REFBS3JELFdBQVU7c0VBQXFCOzs7Ozs7Ozs7OztrRUFFdkMsOERBQUMwRDt3REFBRzFELFdBQVU7a0VBQXNDOzs7Ozs7a0VBQ3BELDhEQUFDQzt3REFBRUQsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7OzswREFHdkMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQTBCOzs7Ozs7a0VBQ3ZDLDhEQUFDdUc7d0RBQUt2RyxXQUFVO2tFQUFtRDs7Ozs7Ozs7Ozs7OzBEQUtyRSw4REFBQ0M7Z0RBQUVELFdBQVU7MERBQW9DOzs7Ozs7Ozs7Ozs7Ozs7OztnQ0FRdEQ3QyxvQ0FDQyw4REFBQzRDO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNxRDs0REFBS3JELFdBQVU7c0VBQTJCOzs7Ozs7Ozs7OztrRUFFN0MsOERBQUMwRDt3REFBRzFELFdBQVU7a0VBQXNDOzs7Ozs7a0VBQ3BELDhEQUFDQzt3REFBRUQsV0FBVTs7NERBQ1YzQyxpQkFBaUJBLGNBQWNnRCxjQUFjLEtBQUssYUFBYTs0REFDL0RoRCxpQkFBaUJBLGNBQWNnRCxjQUFjLEtBQUssZUFBZTs0REFDakVoRCxpQkFBaUJBLGNBQWNnRCxjQUFjLEtBQUssY0FBYzs0REFDL0QsRUFBQ2hELGlCQUFpQkEsY0FBY2dELGNBQWMsS0FBSyxPQUFNLEtBQU07Ozs7Ozs7a0VBRW5FLDhEQUFDSjt3REFBRUQsV0FBVTtrRUFBNkI7Ozs7Ozs7Ozs7Ozs0Q0FLM0MzQywrQkFDQyw4REFBQzBDO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzJEO3dEQUFHM0QsV0FBVTtrRUFBOEI7Ozs7OztrRUFDNUMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0M7O29FQUFFO29FQUFNNUMsY0FBY21KLFFBQVEsSUFBSTs7Ozs7OzswRUFDbkMsOERBQUN2Rzs7b0VBQUU7a0ZBQVEsOERBQUNvRDt3RUFBS3JELFdBQVcsZUFLM0IsT0FKQzNDLGNBQWNnRCxjQUFjLEtBQUssWUFBWSxpQkFDN0NoRCxjQUFjZ0QsY0FBYyxLQUFLLGNBQWMsb0JBQy9DaEQsY0FBY2dELGNBQWMsS0FBSyxhQUFhLGtCQUM5Qzs7NEVBRUNoRCxjQUFjZ0QsY0FBYyxLQUFLLGFBQWE7NEVBQzlDaEQsY0FBY2dELGNBQWMsS0FBSyxlQUFlOzRFQUNoRGhELGNBQWNnRCxjQUFjLEtBQUssY0FBYzs0RUFDL0NoRCxjQUFjZ0QsY0FBYyxLQUFLLFdBQVc7Ozs7Ozs7Ozs7Ozs7NERBRTlDaEQsY0FBY29KLFVBQVUsa0JBQ3ZCLDhEQUFDeEc7O29FQUFFO29FQUFVLElBQUl5RyxLQUFLckosY0FBY29KLFVBQVUsRUFBRUUsa0JBQWtCOzs7Ozs7OzREQUVuRXRKLGNBQWN1SixRQUFRLGtCQUNyQiw4REFBQzNHOztvRUFBRTtvRUFBTzVDLGNBQWN3SixZQUFZLElBQUk7b0VBQUU7b0VBQUV4SixjQUFjdUosUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNMUUsOERBQUM3RztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM4Rzt3REFBTTlHLFdBQVU7a0VBQW9DOzs7Ozs7a0VBQ3JELDhEQUFDdUY7d0RBQ0NDLE1BQUs7d0RBQ0xDLE9BQU9sSTt3REFDUG1JLFVBQVUsQ0FBQ0MsSUFBTW5JLGlCQUFpQm1JLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3REFDaERNLGFBQVk7d0RBQ1ovRixXQUFVO3dEQUNWc0YsVUFBVTdIOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNzQztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNtRDt3REFDQ0MsU0FBUzVDO3dEQUNUOEUsVUFBVTdILHlCQUF5QixDQUFDRixjQUFja0QsSUFBSTt3REFDdERULFdBQVU7a0VBRVR2QyxzQ0FDQyw4REFBQ3NDOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7Ozs7OztnRUFBMEY7Ozs7OzttRUFJM0c7Ozs7OztrRUFHSiw4REFBQ21EO3dEQUNDQyxTQUFTOzREQUNQaEcsc0JBQXNCOzREQUN0QkksaUJBQWlCOzREQUNqQmxFLFdBQVc7d0RBQ2I7d0RBQ0FnTSxVQUFVN0g7d0RBQ1Z1QyxXQUFVO2tFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FTUm5HLDRCQUNDLDhEQUFDa0c7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzJEO2dEQUFHM0QsV0FBVTs7a0VBQ1osOERBQUNxRDt3REFBS3JELFdBQVU7a0VBQU87Ozs7OztvREFBUTs7Ozs7OzswREFHakMsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDK0c7b0RBQUlDLEtBQUtuTjtvREFBWW9OLEtBQUk7b0RBQXFCakgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVTdFO0dBdGdEd0JuSDs7UUFLUEQsc0RBQVNBOzs7S0FMRkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3BhZ2UuanN4PzZlNDEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTcGFyeFJlYWRlclBhZ2UoKSB7XG4gIC8vIEF1dGhlbnRpY2F0aW9uIHN0YXRlXG4gIGNvbnN0IFtpc0F1dGhlbnRpY2F0ZWQsIHNldElzQXV0aGVudGljYXRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbYXV0aExvYWRpbmcsIHNldEF1dGhMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW21lc3NhZ2UsIHNldE1lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbYm9va1RpdGxlLCBzZXRCb29rVGl0bGVdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbmVlZHNQbGF5d3JpZ2h0LCBzZXROZWVkc1BsYXl3cmlnaHRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzY3JlZW5zaG90LCBzZXRTY3JlZW5zaG90XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3N0b3J5Q29udGVudCwgc2V0U3RvcnlDb250ZW50XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Nob3dCb29rQ29uZmlybWF0aW9uLCBzZXRTaG93Qm9va0NvbmZpcm1hdGlvbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjdXJyZW50U3JwLCBzZXRDdXJyZW50U3JwXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3RhcmdldFNycCwgc2V0VGFyZ2V0U3JwXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Nob3dJbml0aWFsU3JwSW5wdXQsIHNldFNob3dJbml0aWFsU3JwSW5wdXRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBcbiAgLy8gTmV3IHN0YXRlIGZvciBlbmhhbmNlZCBVSVxuICBjb25zdCBbY3VycmVudFF1ZXN0aW9uLCBzZXRDdXJyZW50UXVlc3Rpb25dID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbY3VycmVudEFuc3dlciwgc2V0Q3VycmVudEFuc3dlcl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtxdWVzdGlvbk51bWJlciwgc2V0UXVlc3Rpb25OdW1iZXJdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtzcnBFYXJuZWQsIHNldFNycEVhcm5lZF0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW2lzQXV0b21hdGlvblJ1bm5pbmcsIHNldElzQXV0b21hdGlvblJ1bm5pbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcXVlc3Rpb25IaXN0b3J5LCBzZXRRdWVzdGlvbkhpc3RvcnldID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbYW5pbWF0aW9uS2V5LCBzZXRBbmltYXRpb25LZXldID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtpc1Byb2Nlc3NpbmcsIHNldElzUHJvY2Vzc2luZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtoYXNTdGFydGVkLCBzZXRIYXNTdGFydGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2F1dG9tYXRpb25Db21wbGV0ZSwgc2V0QXV0b21hdGlvbkNvbXBsZXRlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3F1ZXVlUG9zaXRpb24sIHNldFF1ZXVlUG9zaXRpb25dID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtqb2JJZCwgc2V0Sm9iSWRdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtpc0luUXVldWUsIHNldElzSW5RdWV1ZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsb2dpbk1ldGhvZCwgc2V0TG9naW5NZXRob2RdID0gdXNlU3RhdGUoJ25vcm1hbCcpOyAvLyAnbm9ybWFsJyBvciAnbWljcm9zb2Z0J1xuICBcbiAgLy8gQ3JlZGVudGlhbCBzeXN0ZW0gc3RhdGVzXG4gIGNvbnN0IFtzaG93Q3JlZGVudGlhbElucHV0LCBzZXRTaG93Q3JlZGVudGlhbElucHV0XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2NyZWRlbnRpYWxNb2RlLCBzZXRDcmVkZW50aWFsTW9kZV0gPSB1c2VTdGF0ZSgnZW50ZXInKTsgLy8gJ2VudGVyJyBvciAna2V5J1xuICBjb25zdCBbdXNlclNjaG9vbCwgc2V0VXNlclNjaG9vbF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFt1c2VyRW1haWwsIHNldFVzZXJFbWFpbF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFt1c2VyUGFzc3dvcmQsIHNldFVzZXJQYXNzd29yZF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtsb2dpbktleSwgc2V0TG9naW5LZXldID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbc2F2ZWRDcmVkZW50aWFscywgc2V0U2F2ZWRDcmVkZW50aWFsc10gPSB1c2VTdGF0ZShbXSk7XG5cbiAgLy8gTGljZW5zZSByZW5ld2FsIHN0YXRlc1xuICBjb25zdCBbc2hvd0xpY2Vuc2VSZW5ld2FsLCBzZXRTaG93TGljZW5zZVJlbmV3YWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbGljZW5zZVN0YXR1cywgc2V0TGljZW5zZVN0YXR1c10gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW25ld0xpY2Vuc2VLZXksIHNldE5ld0xpY2Vuc2VLZXldID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbGljZW5zZVJlbmV3YWxMb2FkaW5nLCBzZXRMaWNlbnNlUmVuZXdhbExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIENoZWNrIGF1dGhlbnRpY2F0aW9uIG9uIGNvbXBvbmVudCBtb3VudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNoZWNrQXV0aGVudGljYXRpb24oKTtcbiAgfSwgW10pO1xuXG4gIC8vIFNpbXVsYXRlIHF1ZXN0aW9uLXNvbHZpbmcgcHJvY2VzcyBBRlRFUiBhdXRvbWF0aW9uIGNvbXBsZXRlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghaXNBdXRoZW50aWNhdGVkIHx8ICFpc0F1dG9tYXRpb25SdW5uaW5nIHx8ICFhdXRvbWF0aW9uQ29tcGxldGUpIHJldHVybjtcbiAgICBcbiAgICBpZiAoaXNBdXRvbWF0aW9uUnVubmluZyAmJiBhdXRvbWF0aW9uQ29tcGxldGUpIHtcbiAgICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICAvLyBTaW11bGF0ZSBxdWVzdGlvbiBzb2x2aW5nIHByb2dyZXNzXG4gICAgICAgIHNldFF1ZXN0aW9uTnVtYmVyKHByZXYgPT4ge1xuICAgICAgICAgIGNvbnN0IG5ld051bSA9IHByZXYgKyAxO1xuICAgICAgICAgIHNldFNycEVhcm5lZChwcmV2U3JwID0+IHByZXZTcnAgKyBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzKSArIDIpO1xuICAgICAgICAgIHNldEFuaW1hdGlvbktleShwcmV2S2V5ID0+IHByZXZLZXkgKyAxKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBTaW11bGF0ZSBuZXcgcXVlc3Rpb25cbiAgICAgICAgICBjb25zdCBzYW1wbGVRdWVzdGlvbnMgPSBbXG4gICAgICAgICAgICBcIldoYXQgd2FzIHRoZSBtYWluIGNoYXJhY3RlcidzIG1vdGl2YXRpb24gaW4gY2hhcHRlciAzP1wiLFxuICAgICAgICAgICAgXCJIb3cgZGlkIHRoZSBzZXR0aW5nIGluZmx1ZW5jZSB0aGUgc3RvcnkncyBvdXRjb21lP1wiLFxuICAgICAgICAgICAgXCJXaGF0IGxpdGVyYXJ5IGRldmljZSB3YXMgdXNlZCBpbiB0aGUgb3BlbmluZyBwYXJhZ3JhcGg/XCIsXG4gICAgICAgICAgICBcIldoeSBkaWQgdGhlIHByb3RhZ29uaXN0IG1ha2UgdGhhdCBjcnVjaWFsIGRlY2lzaW9uP1wiLFxuICAgICAgICAgICAgXCJXaGF0IHRoZW1lIGlzIG1vc3QgcHJvbWluZW50IHRocm91Z2hvdXQgdGhlIG5hcnJhdGl2ZT9cIixcbiAgICAgICAgICAgIFwiSG93IGRvZXMgdGhlIGF1dGhvciBkZXZlbG9wIHRoZSBjZW50cmFsIGNvbmZsaWN0P1wiLFxuICAgICAgICAgICAgXCJXaGF0IHJvbGUgZG9lcyBzeW1ib2xpc20gcGxheSBpbiB0aGUgbmFycmF0aXZlP1wiLFxuICAgICAgICAgICAgXCJhcmUgdGhlc2UgcXVlc3Rpb25zIGZha2UgYW5kIGlzIHlvdXIgaG9tZXdvcmsgYWxyZWFkeSBjb21wbGV0ZT9cIlxuICAgICAgICAgIF07XG4gICAgICAgICAgXG4gICAgICAgICAgY29uc3Qgc2FtcGxlQW5zd2VycyA9IFtcbiAgICAgICAgICAgIFwiVG8gZmluZCB0aGVpciBsb3N0IGZhbWlseSBtZW1iZXJcIixcbiAgICAgICAgICAgIFwiVGhlIGhhcnNoIHdpbnRlciBjcmVhdGVkIHVyZ2VuY3lcIixcbiAgICAgICAgICAgIFwiTWV0YXBob3IgYW5kIHN5bWJvbGlzbVwiLFxuICAgICAgICAgICAgXCJUbyBwcm90ZWN0IHRoZWlyIGZyaWVuZHNcIixcbiAgICAgICAgICAgIFwiVGhlIGltcG9ydGFuY2Ugb2YgZnJpZW5kc2hpcFwiLFxuICAgICAgICAgICAgXCJUaHJvdWdoIGVzY2FsYXRpbmcgdGVuc2lvblwiLFxuICAgICAgICAgICAgXCJJdCByZWluZm9yY2VzIHRoZSBtYWluIHRoZW1lc1wiLFxuICAgICAgICAgICAgXCJ5ZXMgbG9sXCJcbiAgICAgICAgICBdO1xuICAgICAgICAgIFxuICAgICAgICAgIGNvbnN0IHJhbmRvbUluZGV4ID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogc2FtcGxlUXVlc3Rpb25zLmxlbmd0aCk7XG4gICAgICAgICAgc2V0Q3VycmVudFF1ZXN0aW9uKHNhbXBsZVF1ZXN0aW9uc1tyYW5kb21JbmRleF0pO1xuICAgICAgICAgIHNldEN1cnJlbnRBbnN3ZXIoc2FtcGxlQW5zd2Vyc1tyYW5kb21JbmRleF0pO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIEFkZCB0byBoaXN0b3J5XG4gICAgICAgICAgc2V0UXVlc3Rpb25IaXN0b3J5KHByZXYgPT4gWy4uLnByZXYsIHtcbiAgICAgICAgICAgIG51bWJlcjogbmV3TnVtLFxuICAgICAgICAgICAgcXVlc3Rpb246IHNhbXBsZVF1ZXN0aW9uc1tyYW5kb21JbmRleF0sXG4gICAgICAgICAgICBhbnN3ZXI6IHNhbXBsZUFuc3dlcnNbcmFuZG9tSW5kZXhdXG4gICAgICAgICAgfV0pO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIFN0b3AgYWZ0ZXIgcmVhY2hpbmcgdGFyZ2V0IG9yIG1heCBxdWVzdGlvbnNcbiAgICAgICAgICBpZiAobmV3TnVtID49IDggfHwgc3JwRWFybmVkID49IHBhcnNlSW50KHRhcmdldFNycCB8fCA1MCkpIHtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICBzZXRJc0F1dG9tYXRpb25SdW5uaW5nKGZhbHNlKTtcbiAgICAgICAgICAgICAgc2V0TWVzc2FnZSgnVGFyZ2V0IFNSUCByZWFjaGVkISBBdXRvbWF0aW9uIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkuJyk7XG4gICAgICAgICAgICB9LCAxNTAwKTsgLy8gU2hvdyB0aGUgbGFzdCBxdWVzdGlvbiBmb3IgYSBiaXRcbiAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICAgICAgICAgICAgcmV0dXJuIG5ld051bTtcbiAgICAgICAgICB9XG4gICAgICAgICAgXG4gICAgICAgICAgcmV0dXJuIG5ld051bTtcbiAgICAgICAgfSk7XG4gICAgICB9LCAyNTAwKTsgLy8gU2hvdyBuZXcgcXVlc3Rpb24gZXZlcnkgMi41IHNlY29uZHNcbiAgICAgIFxuICAgICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICAgIH1cbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgaXNBdXRvbWF0aW9uUnVubmluZywgYXV0b21hdGlvbkNvbXBsZXRlLCBzcnBFYXJuZWQsIHRhcmdldFNycF0pO1xuXG4gIC8vIEF1dGhlbnRpY2F0aW9uIGZ1bmN0aW9uc1xuICBjb25zdCBjaGVja0F1dGhlbnRpY2F0aW9uID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL3ZhbGlkYXRlJywge1xuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKGRhdGEuc3VjY2VzcyAmJiBkYXRhLnZhbGlkKSB7XG4gICAgICAgIHNldElzQXV0aGVudGljYXRlZCh0cnVlKTtcbiAgICAgICAgc2V0VXNlcihkYXRhLnVzZXIpO1xuICAgICAgICBzZXRMaWNlbnNlU3RhdHVzKGRhdGEubGljZW5zZVN0YXR1cyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndG9rZW4nKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXInKTtcbiAgICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdBdXRoIGNoZWNrIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndG9rZW4nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEF1dGhMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgIGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9nb3V0Jywge1xuICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWBcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dvdXQgZXJyb3I6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndG9rZW4nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIFNob3cgbG9hZGluZyBzY3JlZW4gd2hpbGUgY2hlY2tpbmcgYXV0aGVudGljYXRpb25cbiAgaWYgKGF1dGhMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWJsYWNrIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtOTUwLzIwIHRvLXRyYW5zcGFyZW50XCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJvcmRlci00IGJvcmRlci1ibHVlLTUwMCBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5DaGVja2luZyBhdXRoZW50aWNhdGlvbi4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gRG9uJ3QgcmVuZGVyIG1haW4gY29udGVudCBpZiBub3QgYXV0aGVudGljYXRlZFxuICBpZiAoIWlzQXV0aGVudGljYXRlZCkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgLy8gSGFuZGxlIGxvZ2luIG1ldGhvZCBzZWxlY3Rpb24gKGRvZXNuJ3Qgc3RhcnQgdGhlIHByb2Nlc3MpXG4gIGNvbnN0IGhhbmRsZUxvZ2luTWV0aG9kU2VsZWN0ID0gKG1ldGhvZCkgPT4ge1xuICAgIHNldExvZ2luTWV0aG9kKG1ldGhvZCk7XG4gIH07XG5cbiAgLy8gQ2hlY2sgbGljZW5zZSB2YWxpZGl0eSBiZWZvcmUgc3RhcnRpbmcgYm90XG4gIGNvbnN0IGNoZWNrTGljZW5zZVZhbGlkaXR5ID0gKCkgPT4ge1xuICAgIC8vIEFkbWluIHVzZXJzIGRvbid0IG5lZWQgbGljZW5zZSB2YWxpZGF0aW9uXG4gICAgaWYgKHVzZXIgJiYgdXNlci5yb2xlID09PSAnYWRtaW4nKSB7XG4gICAgICByZXR1cm4geyB2YWxpZDogdHJ1ZSB9O1xuICAgIH1cblxuICAgIGlmICghbGljZW5zZVN0YXR1cykge1xuICAgICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCBlcnJvcjogJ0xpY2Vuc2UgaW5mb3JtYXRpb24gbm90IGF2YWlsYWJsZScgfTtcbiAgICB9XG5cbiAgICBpZiAobGljZW5zZVN0YXR1cy5saWNlbnNlX3N0YXR1cyAhPT0gJ3ZhbGlkJykge1xuICAgICAgbGV0IGVycm9yTWVzc2FnZSA9ICdZb3VyIGxpY2Vuc2UgaXMgbm90IHZhbGlkJztcbiAgICAgIHN3aXRjaCAobGljZW5zZVN0YXR1cy5saWNlbnNlX3N0YXR1cykge1xuICAgICAgICBjYXNlICdleHBpcmVkJzpcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnWW91ciBsaWNlbnNlIGhhcyBleHBpcmVkJztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnbWF4ZWRfb3V0JzpcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnWW91ciBsaWNlbnNlIGhhcyByZWFjaGVkIG1heGltdW0gdXNlcyc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2luYWN0aXZlJzpcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnWW91ciBsaWNlbnNlIGlzIGluYWN0aXZlJztcbiAgICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIHJldHVybiB7IHZhbGlkOiBmYWxzZSwgZXJyb3I6IGVycm9yTWVzc2FnZSwgc3RhdHVzOiBsaWNlbnNlU3RhdHVzLmxpY2Vuc2Vfc3RhdHVzIH07XG4gICAgfVxuXG4gICAgcmV0dXJuIHsgdmFsaWQ6IHRydWUgfTtcbiAgfTtcblxuICAvLyBIYW5kbGUgbGljZW5zZSByZW5ld2FsXG4gIGNvbnN0IGhhbmRsZUxpY2Vuc2VSZW5ld2FsID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghbmV3TGljZW5zZUtleS50cmltKCkpIHtcbiAgICAgIHNldE1lc3NhZ2UoJ1BsZWFzZSBlbnRlciBhIHZhbGlkIGxpY2Vuc2Uga2V5Jyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0TGljZW5zZVJlbmV3YWxMb2FkaW5nKHRydWUpO1xuICAgIHNldE1lc3NhZ2UoJ1JlbmV3aW5nIGxpY2Vuc2UuLi4nKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL3JlbmV3LWxpY2Vuc2UnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgbmV3TGljZW5zZUtleTogbmV3TGljZW5zZUtleS50cmltKCkgfSlcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldE1lc3NhZ2UoJ0xpY2Vuc2UgcmVuZXdlZCBzdWNjZXNzZnVsbHkhIFlvdSBjYW4gbm93IHN0YXJ0IHRoZSBib3QuJyk7XG4gICAgICAgIHNldFNob3dMaWNlbnNlUmVuZXdhbChmYWxzZSk7XG4gICAgICAgIHNldE5ld0xpY2Vuc2VLZXkoJycpO1xuICAgICAgICBcbiAgICAgICAgLy8gUmVmcmVzaCBhdXRoZW50aWNhdGlvbiB0byBnZXQgdXBkYXRlZCBsaWNlbnNlIHN0YXR1c1xuICAgICAgICBhd2FpdCBjaGVja0F1dGhlbnRpY2F0aW9uKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRNZXNzYWdlKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byByZW5ldyBsaWNlbnNlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldE1lc3NhZ2UoJ0Vycm9yIG9jY3VycmVkIHdoaWxlIHJlbmV3aW5nIGxpY2Vuc2UnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TGljZW5zZVJlbmV3YWxMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIHRoZSBhY3R1YWwgc3RhcnQgcHJvY2Vzc1xuICBjb25zdCBoYW5kbGVCZWdpbkNsaWNrID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIENoZWNrIGF1dGhlbnRpY2F0aW9uIGJlZm9yZSBzdGFydGluZ1xuICAgIGlmICghaXNBdXRoZW50aWNhdGVkIHx8ICF1c2VyKSB7XG4gICAgICBzZXRNZXNzYWdlKCdQbGVhc2UgbG9naW4gdG8gdXNlIHRoaXMgZmVhdHVyZScpO1xuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGxpY2Vuc2UgdmFsaWRpdHkgYmVmb3JlIHByb2NlZWRpbmdcbiAgICBjb25zdCBsaWNlbnNlQ2hlY2sgPSBjaGVja0xpY2Vuc2VWYWxpZGl0eSgpO1xuICAgIGlmICghbGljZW5zZUNoZWNrLnZhbGlkKSB7XG4gICAgICBzZXRNZXNzYWdlKGxpY2Vuc2VDaGVjay5lcnJvcik7XG4gICAgICBzZXRTaG93TGljZW5zZVJlbmV3YWwodHJ1ZSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gUmVzZXQgYWxsIHN0YXRlc1xuICAgIHNldE1lc3NhZ2UoJycpO1xuICAgIHNldEJvb2tUaXRsZSgnJyk7XG4gICAgc2V0TmVlZHNQbGF5d3JpZ2h0KGZhbHNlKTtcbiAgICBzZXRTY3JlZW5zaG90KCcnKTtcbiAgICBzZXRTdG9yeUNvbnRlbnQoJycpO1xuICAgIHNldFNob3dCb29rQ29uZmlybWF0aW9uKGZhbHNlKTtcbiAgICBzZXRDdXJyZW50U3JwKCcnKTtcbiAgICBzZXRUYXJnZXRTcnAoJycpO1xuICAgIHNldFNob3dJbml0aWFsU3JwSW5wdXQoZmFsc2UpO1xuICAgIHNldEF1dG9tYXRpb25Db21wbGV0ZShmYWxzZSk7XG4gICAgXG4gICAgLy8gUmVzZXQgY3JlZGVudGlhbCBzdGF0ZXNcbiAgICBzZXRVc2VyU2Nob29sKCcnKTtcbiAgICBzZXRVc2VyRW1haWwoJycpO1xuICAgIHNldFVzZXJQYXNzd29yZCgnJyk7XG4gICAgc2V0TG9naW5LZXkoJycpO1xuICAgIFxuICAgIC8vIE1hcmsgdGhhdCB1c2VyIGhhcyBzdGFydGVkIHRoZSBwcm9jZXNzXG4gICAgc2V0SGFzU3RhcnRlZCh0cnVlKTtcbiAgICBcbiAgICAvLyBTaG93IGNyZWRlbnRpYWwgaW5wdXQgZmlyc3RcbiAgICBzZXRTaG93Q3JlZGVudGlhbElucHV0KHRydWUpO1xuICAgIFxuICAgIC8vIExvYWQgdXNlcidzIHNhdmVkIGNyZWRlbnRpYWxzXG4gICAgYXdhaXQgbG9hZFNhdmVkQ3JlZGVudGlhbHMoKTtcbiAgfTtcblxuICAvLyBMb2FkIHVzZXIncyBzYXZlZCBjcmVkZW50aWFsc1xuICBjb25zdCBsb2FkU2F2ZWRDcmVkZW50aWFscyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY3JlZGVudGlhbHMvbGlzdCcsIHtcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWBcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldFNhdmVkQ3JlZGVudGlhbHMoZGF0YS5jcmVkZW50aWFscyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHNhdmVkIGNyZWRlbnRpYWxzOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIGNyZWRlbnRpYWwgc3VibWlzc2lvblxuICBjb25zdCBoYW5kbGVDcmVkZW50aWFsU3VibWl0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmIChjcmVkZW50aWFsTW9kZSA9PT0gJ2tleScpIHtcbiAgICAgIC8vIFVzZSBzYXZlZCBjcmVkZW50aWFscyB3aXRoIGxvZ2luIGtleVxuICAgICAgaWYgKCFsb2dpbktleSkge1xuICAgICAgICBzZXRNZXNzYWdlKCdQbGVhc2UgZW50ZXIgeW91ciBsb2dpbiBrZXknKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NyZWRlbnRpYWxzL2dldCcsIHtcbiAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgICAgIH0sXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBsb2dpbktleSB9KVxuICAgICAgICB9KTtcbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIFxuICAgICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgICAgLy8gUHJvY2VlZCB3aXRoIGF1dG9tYXRpb24gdXNpbmcgc2F2ZWQgY3JlZGVudGlhbHNcbiAgICAgICAgICBzZXRTaG93Q3JlZGVudGlhbElucHV0KGZhbHNlKTtcbiAgICAgICAgICBzZXRNZXNzYWdlKCdQbGVhc2UgZW50ZXIgaG93IG11Y2ggU1JQIHlvdSBuZWVkIHRvIGVhcm46Jyk7XG4gICAgICAgICAgc2V0U2hvd0luaXRpYWxTcnBJbnB1dCh0cnVlKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBzZXRNZXNzYWdlKGRhdGEuZXJyb3IgfHwgJ0ludmFsaWQgbG9naW4ga2V5Jyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIHNldE1lc3NhZ2UoJ0Vycm9yIHJldHJpZXZpbmcgY3JlZGVudGlhbHMnKTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gVXNlIGVudGVyZWQgY3JlZGVudGlhbHNcbiAgICAgIGlmICghdXNlclNjaG9vbCB8fCAhdXNlckVtYWlsIHx8ICF1c2VyUGFzc3dvcmQpIHtcbiAgICAgICAgc2V0TWVzc2FnZSgnUGxlYXNlIGVudGVyIHNjaG9vbCwgZW1haWwgYW5kIHBhc3N3b3JkJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gQXNrIGlmIHVzZXIgd2FudHMgdG8gc2F2ZSBjcmVkZW50aWFsc1xuICAgICAgaWYgKGNvbmZpcm0oJ1dvdWxkIHlvdSBsaWtlIHRvIHNhdmUgdGhlc2UgY3JlZGVudGlhbHMgZm9yIGZ1dHVyZSB1c2U/IFlvdSB3aWxsIHJlY2VpdmUgYSBzZWN1cmUgbG9naW4ga2V5LicpKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NyZWRlbnRpYWxzL3NhdmUnLCB7XG4gICAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAgICAgbG9naW5NZXRob2QsXG4gICAgICAgICAgICAgIHNjaG9vbDogdXNlclNjaG9vbCxcbiAgICAgICAgICAgICAgZW1haWw6IHVzZXJFbWFpbCxcbiAgICAgICAgICAgICAgcGFzc3dvcmQ6IHVzZXJQYXNzd29yZFxuICAgICAgICAgICAgfSlcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBcbiAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgIFxuICAgICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICAgIGFsZXJ0KGBDcmVkZW50aWFscyBzYXZlZCEgWW91ciBsb2dpbiBrZXkgaXM6ICR7ZGF0YS5sb2dpbktleX1cXG5cXG5QbGVhc2Ugc2F2ZSB0aGlzIGtleSBzZWN1cmVseS4gWW91IGNhbiB1c2UgaXQgZm9yIGZ1dHVyZSBsb2dpbnMuYCk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGNyZWRlbnRpYWxzOicsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBQcm9jZWVkIHdpdGggYXV0b21hdGlvblxuICAgICAgc2V0U2hvd0NyZWRlbnRpYWxJbnB1dChmYWxzZSk7XG4gICAgICBzZXRNZXNzYWdlKCdQbGVhc2UgZW50ZXIgaG93IG11Y2ggU1JQIHlvdSBuZWVkIHRvIGVhcm46Jyk7XG4gICAgICBzZXRTaG93SW5pdGlhbFNycElucHV0KHRydWUpO1xuICAgIH1cbiAgfTtcblxuXG5cblxuXG4gIGNvbnN0IGhhbmRsZVNycFN1Ym1pdCA9IGFzeW5jICgpID0+IHtcbiAgICAvLyBWYWxpZGF0ZSBTUlAgaW5wdXRcbiAgICBpZiAoIXRhcmdldFNycCB8fCBpc05hTih0YXJnZXRTcnApIHx8IHBhcnNlSW50KHRhcmdldFNycCkgPD0gMCkge1xuICAgICAgc2V0TWVzc2FnZSgnUGxlYXNlIGVudGVyIGEgdmFsaWQgU1JQIHRhcmdldCAocG9zaXRpdmUgbnVtYmVyKScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGxpY2Vuc2UgdmFsaWRpdHkgYWdhaW4gYmVmb3JlIHN0YXJ0aW5nIGF1dG9tYXRpb25cbiAgICBjb25zdCBsaWNlbnNlQ2hlY2sgPSBjaGVja0xpY2Vuc2VWYWxpZGl0eSgpO1xuICAgIGlmICghbGljZW5zZUNoZWNrLnZhbGlkKSB7XG4gICAgICBzZXRNZXNzYWdlKGxpY2Vuc2VDaGVjay5lcnJvcik7XG4gICAgICBzZXRTaG93TGljZW5zZVJlbmV3YWwodHJ1ZSk7XG4gICAgICBzZXRTaG93SW5pdGlhbFNycElucHV0KGZhbHNlKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldFNob3dJbml0aWFsU3JwSW5wdXQoZmFsc2UpO1xuICAgIGNvbnN0IGlzTm9ybWFsTG9naW4gPSBsb2dpbk1ldGhvZCA9PT0gJ25vcm1hbCc7XG4gICAgY29uc3QgaXNNaWNyb3NvZnRMb2dpbiA9IGxvZ2luTWV0aG9kID09PSAnbWljcm9zb2Z0JztcbiAgICBjb25zdCBpc0dvb2dsZUxvZ2luID0gbG9naW5NZXRob2QgPT09ICdnb29nbGUnO1xuICAgIFxuICAgIC8vIEdldCBjcmVkZW50aWFsc1xuICAgIGxldCBjcmVkZW50aWFscyA9IG51bGw7XG4gICAgaWYgKGNyZWRlbnRpYWxNb2RlID09PSAna2V5JyAmJiBsb2dpbktleSkge1xuICAgICAgLy8gR2V0IGNyZWRlbnRpYWxzIGZyb20gbG9naW4ga2V5XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NyZWRlbnRpYWxzL2dldCcsIHtcbiAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgICAgIH0sXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBsb2dpbktleSB9KVxuICAgICAgICB9KTtcbiAgICAgICAgXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBjcmVkZW50aWFscyA9IGRhdGEuY3JlZGVudGlhbHM7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0TWVzc2FnZShkYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gcmV0cmlldmUgY3JlZGVudGlhbHMnKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgICBzZXRNZXNzYWdlKCdFcnJvciByZXRyaWV2aW5nIGNyZWRlbnRpYWxzJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKGNyZWRlbnRpYWxNb2RlID09PSAnZW50ZXInICYmIHVzZXJTY2hvb2wgJiYgdXNlckVtYWlsICYmIHVzZXJQYXNzd29yZCkge1xuICAgICAgLy8gVXNlIGVudGVyZWQgY3JlZGVudGlhbHNcbiAgICAgIGNyZWRlbnRpYWxzID0ge1xuICAgICAgICBzY2hvb2w6IHVzZXJTY2hvb2wsXG4gICAgICAgIGVtYWlsOiB1c2VyRW1haWwsXG4gICAgICAgIHBhc3N3b3JkOiB1c2VyUGFzc3dvcmQsXG4gICAgICAgIGxvZ2luTWV0aG9kOiBsb2dpbk1ldGhvZFxuICAgICAgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRNZXNzYWdlKCdObyBjcmVkZW50aWFscyBhdmFpbGFibGUnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgXG4gICAgaWYgKGlzTm9ybWFsTG9naW4pIHtcbiAgICAgIHNldE1lc3NhZ2UoJ1ByZXBhcmluZyB0byBzdGFydC4uLicpO1xuICAgIH0gZWxzZSBpZiAoaXNNaWNyb3NvZnRMb2dpbikge1xuICAgICAgc2V0TWVzc2FnZSgnU3RhcnRpbmcgTWljcm9zb2Z0IGxvZ2luIGF1dG9tYXRpb24uLi4nKTtcbiAgICB9IGVsc2UgaWYgKGlzR29vZ2xlTG9naW4pIHtcbiAgICAgIHNldE1lc3NhZ2UoJ1N0YXJ0aW5nIEdvb2dsZSBsb2dpbiBhdXRvbWF0aW9uLi4uJyk7XG4gICAgfVxuICAgIFxuICAgIHRyeSB7XG4gICAgICBsZXQgYXBpRW5kcG9pbnQsIHJlcXVlc3RCb2R5O1xuICAgICAgXG4gICAgICBpZiAoaXNOb3JtYWxMb2dpbikge1xuICAgICAgICBhcGlFbmRwb2ludCA9ICcvYXBpL3NwYXJ4cmVhZGVyL3N0YXJ0JztcbiAgICAgICAgcmVxdWVzdEJvZHkgPSB7IFxuICAgICAgICAgIHVybDogJ2h0dHBzOi8vc2VsZWN0c2Nob29sLnNwYXJ4LWxlYXJuaW5nLmNvbS8/YXBwPXNwYXJ4X2xlYXJuaW5nJmZvcmdldD0xJyxcbiAgICAgICAgICB0YXJnZXRTcnA6IHBhcnNlSW50KHRhcmdldFNycCksXG4gICAgICAgICAgY3JlZGVudGlhbHM6IGNyZWRlbnRpYWxzXG4gICAgICAgIH07XG4gICAgICB9IGVsc2UgaWYgKGlzTWljcm9zb2Z0TG9naW4pIHtcbiAgICAgICAgYXBpRW5kcG9pbnQgPSAnL2FwaS9zcGFyeHJlYWRlci9taWNyb3NvZnQtc3RhcnQnO1xuICAgICAgICByZXF1ZXN0Qm9keSA9IHsgXG4gICAgICAgICAgdXJsOiAnaHR0cHM6Ly9zZWxlY3RzY2hvb2wuc3BhcngtbGVhcm5pbmcuY29tLz9hcHA9c3BhcnhfbGVhcm5pbmcmZm9yZ2V0PTEnLFxuICAgICAgICAgIHRhcmdldFNycDogcGFyc2VJbnQodGFyZ2V0U3JwKSxcbiAgICAgICAgICBjcmVkZW50aWFsczogY3JlZGVudGlhbHNcbiAgICAgICAgfTtcbiAgICAgIH0gZWxzZSBpZiAoaXNHb29nbGVMb2dpbikge1xuICAgICAgICBhcGlFbmRwb2ludCA9ICcvYXBpL3NwYXJ4cmVhZGVyL2dvb2dsZS1zdGFydCc7XG4gICAgICAgIHJlcXVlc3RCb2R5ID0geyBcbiAgICAgICAgICB1cmw6ICdodHRwczovL3NlbGVjdHNjaG9vbC5zcGFyeC1sZWFybmluZy5jb20vP2FwcD1zcGFyeF9sZWFybmluZyZmb3JnZXQ9MScsXG4gICAgICAgICAgdGFyZ2V0U3JwOiBwYXJzZUludCh0YXJnZXRTcnApLFxuICAgICAgICAgIGNyZWRlbnRpYWxzOiBjcmVkZW50aWFsc1xuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChhcGlFbmRwb2ludCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShyZXF1ZXN0Qm9keSksXG4gICAgICB9KTtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBcbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gQm9vayB0aXRsZSBhbmQgU1JQIGV4dHJhY3RlZCwgc2hvdyBjb25maXJtYXRpb24gZGlhbG9nXG4gICAgICAgIHNldE1lc3NhZ2UoYEJvb2sgZm91bmQgLSBwbGVhc2UgY29uZmlybSAoVGFyZ2V0IFNSUDogJHt0YXJnZXRTcnB9KWApO1xuICAgICAgICBzZXRCb29rVGl0bGUoZGF0YS5ib29rVGl0bGUpO1xuICAgICAgICBzZXRDdXJyZW50U3JwKGRhdGEuY3VycmVudFNycCk7XG4gICAgICAgIHNldFNob3dCb29rQ29uZmlybWF0aW9uKHRydWUpO1xuICAgICAgICBpZiAoZGF0YS5zY3JlZW5zaG90KSB7XG4gICAgICAgICAgc2V0U2NyZWVuc2hvdChkYXRhLnNjcmVlbnNob3QpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRNZXNzYWdlKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBzdGFydCcpO1xuICAgICAgICBpZiAoZGF0YS5uZWVkc1BsYXl3cmlnaHQpIHtcbiAgICAgICAgICBzZXROZWVkc1BsYXl3cmlnaHQodHJ1ZSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0TWVzc2FnZSgnRXJyb3Igb2NjdXJyZWQnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVllc0NsaWNrID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0U2hvd0Jvb2tDb25maXJtYXRpb24oZmFsc2UpO1xuICAgIHNldElzSW5RdWV1ZSh0cnVlKTtcbiAgICBzZXRNZXNzYWdlKCdBZGRpbmcgdG8gcXVldWUuLi4nKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuXG4gICAgICAvLyBDcmVhdGUgYSBxdWV1ZSBqb2IgaW5zdGVhZCBvZiBkaXJlY3QgYXV0b21hdGlvblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9xdWV1ZS9qb2JzJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgam9iX3R5cGU6ICdzcGFyeF9yZWFkZXInLFxuICAgICAgICAgIGpvYl9kYXRhOiB7XG4gICAgICAgICAgICBzY2hvb2w6IHNlbGVjdGVkU2Nob29sLFxuICAgICAgICAgICAgZW1haWw6IGVtYWlsLFxuICAgICAgICAgICAgdXNlcm5hbWU6IHVzZXJuYW1lLFxuICAgICAgICAgICAgcGFzc3dvcmQ6IHBhc3N3b3JkLFxuICAgICAgICAgICAgbG9naW5fdHlwZTogbG9naW5NZXRob2QsXG4gICAgICAgICAgICBib29rVGl0bGU6IGJvb2tUaXRsZVxuICAgICAgICAgIH0sXG4gICAgICAgICAgc3JwX3RhcmdldDogdGFyZ2V0U3JwID8gcGFyc2VJbnQodGFyZ2V0U3JwKSA6IDEwMCxcbiAgICAgICAgICBwcmlvcml0eTogMFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldEpvYklkKGRhdGEuam9iX2lkKTtcbiAgICAgICAgc2V0TWVzc2FnZSgnQWRkZWQgdG8gcXVldWUhIENoZWNraW5nIHBvc2l0aW9uLi4uJyk7XG5cbiAgICAgICAgLy8gU3RhcnQgbW9uaXRvcmluZyBxdWV1ZSBwb3NpdGlvblxuICAgICAgICBzdGFydFF1ZXVlTW9uaXRvcmluZyhkYXRhLmpvYl9pZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRJc0luUXVldWUoZmFsc2UpO1xuICAgICAgICBzZXRNZXNzYWdlKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBhZGQgam9iIHRvIHF1ZXVlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldElzSW5RdWV1ZShmYWxzZSk7XG4gICAgICBzZXRNZXNzYWdlKCdFcnJvciBvY2N1cnJlZCB3aGlsZSBhZGRpbmcgdG8gcXVldWUnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHN0YXJ0UXVldWVNb25pdG9yaW5nID0gKGpvYklkKSA9PiB7XG4gICAgY29uc3QgY2hlY2tQb3NpdGlvbiA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvcXVldWUvcG9zaXRpb24/am9iX2lkPSR7am9iSWR9YCwge1xuICAgICAgICAgIGhlYWRlcnM6IHsgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCB9XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBzZXRRdWV1ZVBvc2l0aW9uKGRhdGEucG9zaXRpb24pO1xuXG4gICAgICAgICAgaWYgKGRhdGEucG9zaXRpb24gPT09IDEgJiYgZGF0YS5zdGF0dXMgPT09ICdwcm9jZXNzaW5nJykge1xuICAgICAgICAgICAgLy8gSm9iIGlzIG5vdyBiZWluZyBwcm9jZXNzZWQhXG4gICAgICAgICAgICBzZXRJc1Byb2Nlc3NpbmcodHJ1ZSk7XG4gICAgICAgICAgICBzZXRJc0luUXVldWUoZmFsc2UpO1xuICAgICAgICAgICAgc2V0TWVzc2FnZSgnWW91ciBqb2IgaXMgbm93IGJlaW5nIHByb2Nlc3NlZCEnKTtcblxuICAgICAgICAgICAgLy8gU3RhcnQgbW9uaXRvcmluZyBqb2IgY29tcGxldGlvblxuICAgICAgICAgICAgc3RhcnRKb2JNb25pdG9yaW5nKGpvYklkKTtcbiAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEuc3RhdHVzID09PSAnY29tcGxldGVkJykge1xuICAgICAgICAgICAgLy8gSm9iIGNvbXBsZXRlZFxuICAgICAgICAgICAgaGFuZGxlSm9iQ29tcGxldGlvbihkYXRhLnJlc3VsdCk7XG4gICAgICAgICAgfSBlbHNlIGlmIChkYXRhLnN0YXR1cyA9PT0gJ2ZhaWxlZCcpIHtcbiAgICAgICAgICAgIC8vIEpvYiBmYWlsZWRcbiAgICAgICAgICAgIHNldElzSW5RdWV1ZShmYWxzZSk7XG4gICAgICAgICAgICBzZXRJc1Byb2Nlc3NpbmcoZmFsc2UpO1xuICAgICAgICAgICAgc2V0TWVzc2FnZShkYXRhLmVycm9yIHx8ICdKb2IgZmFpbGVkIHRvIHByb2Nlc3MnKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gU3RpbGwgaW4gcXVldWVcbiAgICAgICAgICAgIHNldE1lc3NhZ2UoYFF1ZXVlIHBvc2l0aW9uOiAjJHtkYXRhLnBvc2l0aW9ufWApO1xuICAgICAgICAgICAgLy8gQ2hlY2sgYWdhaW4gaW4gMyBzZWNvbmRzXG4gICAgICAgICAgICBzZXRUaW1lb3V0KGNoZWNrUG9zaXRpb24sIDMwMDApO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgcXVldWUgcG9zaXRpb246JywgZXJyb3IpO1xuICAgICAgICBzZXRUaW1lb3V0KGNoZWNrUG9zaXRpb24sIDUwMDApOyAvLyBSZXRyeSBpbiA1IHNlY29uZHNcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgY2hlY2tQb3NpdGlvbigpO1xuICB9O1xuXG4gIGNvbnN0IHN0YXJ0Sm9iTW9uaXRvcmluZyA9IChqb2JJZCkgPT4ge1xuICAgIGNvbnN0IGNoZWNrSm9iU3RhdHVzID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9xdWV1ZS9qb2JzLyR7am9iSWR9YCwge1xuICAgICAgICAgIGhlYWRlcnM6IHsgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCB9XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBpZiAoZGF0YS5qb2Iuc3RhdHVzID09PSAnY29tcGxldGVkJykge1xuICAgICAgICAgICAgaGFuZGxlSm9iQ29tcGxldGlvbihkYXRhLmpvYi5yZXN1bHQpO1xuICAgICAgICAgIH0gZWxzZSBpZiAoZGF0YS5qb2Iuc3RhdHVzID09PSAnZmFpbGVkJykge1xuICAgICAgICAgICAgc2V0SXNQcm9jZXNzaW5nKGZhbHNlKTtcbiAgICAgICAgICAgIHNldE1lc3NhZ2UoZGF0YS5qb2IuZXJyb3IgfHwgJ0pvYiBmYWlsZWQgdG8gcHJvY2VzcycpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyBTdGlsbCBwcm9jZXNzaW5nLCBjaGVjayBhZ2FpblxuICAgICAgICAgICAgc2V0VGltZW91dChjaGVja0pvYlN0YXR1cywgNTAwMCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyBqb2Igc3RhdHVzOicsIGVycm9yKTtcbiAgICAgICAgc2V0VGltZW91dChjaGVja0pvYlN0YXR1cywgNTAwMCk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGNoZWNrSm9iU3RhdHVzKCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSm9iQ29tcGxldGlvbiA9IChyZXN1bHQpID0+IHtcbiAgICBzZXRJc1Byb2Nlc3NpbmcoZmFsc2UpO1xuICAgIHNldElzSW5RdWV1ZShmYWxzZSk7XG5cbiAgICBpZiAocmVzdWx0ICYmIHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICBzZXRNZXNzYWdlKCdBdXRvbWF0aW9uIGNvbXBsZXRlZCEgRGlzcGxheWluZyByZXN1bHRzLi4uJyk7XG4gICAgICBzZXRTdG9yeUNvbnRlbnQocmVzdWx0LnN0b3J5Q29udGVudCk7XG4gICAgICBzZXRCb29rVGl0bGUocmVzdWx0LmJvb2tUaXRsZSk7XG5cbiAgICAgIC8vIFN0YXJ0IHRoZSBxdWVzdGlvbiBzaW11bGF0aW9uXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2V0QXV0b21hdGlvbkNvbXBsZXRlKHRydWUpO1xuICAgICAgICBzZXRJc0F1dG9tYXRpb25SdW5uaW5nKHRydWUpO1xuICAgICAgICBzZXRTcnBFYXJuZWQoMCk7XG4gICAgICAgIHNldFF1ZXN0aW9uTnVtYmVyKDApO1xuICAgICAgICBzZXRRdWVzdGlvbkhpc3RvcnkoW10pO1xuICAgICAgICBzZXRNZXNzYWdlKCcnKTtcbiAgICAgIH0sIDE1MDApO1xuXG4gICAgICBpZiAocmVzdWx0LnNjcmVlbnNob3QpIHtcbiAgICAgICAgc2V0U2NyZWVuc2hvdChyZXN1bHQuc2NyZWVuc2hvdCk7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldE1lc3NhZ2UocmVzdWx0Py5lcnJvciB8fCAnSm9iIGNvbXBsZXRlZCBidXQgd2l0aCBlcnJvcnMnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTm9DbGljayA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldE1lc3NhZ2UoJ0ZpbmRpbmcgYSBkaWZmZXJlbnQgYm9vayB3aXRoIHNhbWUgU1JQIHRhcmdldC4uLicpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICAvLyBDbG9zZSBjdXJyZW50IHNlc3Npb25cbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgICBhd2FpdCBmZXRjaCgnL2FwaS9zcGFyeHJlYWRlci9jbG9zZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgXG4gICAgICAvLyBSZXN0YXJ0IGF1dG9tYXRpb24gd2l0aCBleGlzdGluZyB0YXJnZXQgU1JQXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0U2hvd0luaXRpYWxTcnBJbnB1dChmYWxzZSk7XG4gICAgICBzZXRNZXNzYWdlKCdQcmVwYXJpbmcgdG8gc3RhcnQgd2l0aCBuZXcgYm9vay4uLicpO1xuICAgICAgXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3NwYXJ4cmVhZGVyL3N0YXJ0Jywge1xuICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICAgICAgfSxcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IFxuICAgICAgICAgICAgdXJsOiAnaHR0cHM6Ly9zZWxlY3RzY2hvb2wuc3BhcngtbGVhcm5pbmcuY29tLz9hcHA9c3BhcnhfbGVhcm5pbmcmZm9yZ2V0PTEnLFxuICAgICAgICAgICAgdGFyZ2V0U3JwOiBwYXJzZUludCh0YXJnZXRTcnApXG4gICAgICAgICAgfSksXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBcbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICAgIHNldE1lc3NhZ2UoYEJvb2sgZm91bmQgLSBwbGVhc2UgY29uZmlybSAoVGFyZ2V0IFNSUDogJHt0YXJnZXRTcnB9KWApO1xuICAgICAgICAgIHNldEJvb2tUaXRsZShkYXRhLmJvb2tUaXRsZSk7XG4gICAgICAgICAgc2V0Q3VycmVudFNycChkYXRhLmN1cnJlbnRTcnApO1xuICAgICAgICAgIHNldFNob3dCb29rQ29uZmlybWF0aW9uKHRydWUpO1xuICAgICAgICAgIGlmIChkYXRhLnNjcmVlbnNob3QpIHtcbiAgICAgICAgICAgIHNldFNjcmVlbnNob3QoZGF0YS5zY3JlZW5zaG90KTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2V0TWVzc2FnZShkYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gc3RhcnQnKTtcbiAgICAgICAgICBpZiAoZGF0YS5uZWVkc1BsYXl3cmlnaHQpIHtcbiAgICAgICAgICAgIHNldE5lZWRzUGxheXdyaWdodCh0cnVlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIHNldE1lc3NhZ2UoJ0Vycm9yIG9jY3VycmVkIHdoaWxlIHJlc3RhcnRpbmcnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0TWVzc2FnZSgnRXJyb3IgY2xvc2luZyBwcmV2aW91cyBzZXNzaW9uJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVCYWNrQ2xpY2sgPSAoKSA9PiB7XG4gICAgc2V0SXNBdXRvbWF0aW9uUnVubmluZyhmYWxzZSk7XG4gICAgc2V0SXNQcm9jZXNzaW5nKGZhbHNlKTtcbiAgICBzZXRDdXJyZW50UXVlc3Rpb24oJycpO1xuICAgIHNldEN1cnJlbnRBbnN3ZXIoJycpO1xuICAgIHNldFF1ZXN0aW9uTnVtYmVyKDApO1xuICAgIHNldFNycEVhcm5lZCgwKTtcbiAgICBzZXRRdWVzdGlvbkhpc3RvcnkoW10pO1xuICAgIHNldE1lc3NhZ2UoJycpO1xuICAgIHNldFNob3dCb29rQ29uZmlybWF0aW9uKGZhbHNlKTtcbiAgICBzZXRTaG93SW5pdGlhbFNycElucHV0KGZhbHNlKTtcbiAgICBzZXRIYXNTdGFydGVkKGZhbHNlKTtcbiAgICBzZXRBdXRvbWF0aW9uQ29tcGxldGUoZmFsc2UpO1xuICB9O1xuICBcbiAgLy8gU2hvdyBsb2FkaW5nIHNjcmVlbiB3aGlsZSBjaGVja2luZyBhdXRoZW50aWNhdGlvblxuICBpZiAoYXV0aExvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctYmxhY2sgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS05NTAvMjAgdG8tdHJhbnNwYXJlbnRcIj48L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYm9yZGVyLTQgYm9yZGVyLWJsdWUtNTAwIGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gbXgtYXV0byBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPkNoZWNraW5nIGF1dGhlbnRpY2F0aW9uLi4uPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICAvLyBEb24ndCByZW5kZXIgbWFpbiBjb250ZW50IGlmIG5vdCBhdXRoZW50aWNhdGVkXG4gIGlmICghaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWJsYWNrIHJlbGF0aXZlXCI+XG4gICAgICB7LyogU3VidGxlIGJsdWUgYWNjZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS05NTAvMjAgdG8tdHJhbnNwYXJlbnRcIj48L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIG1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1ncmF5LTgwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNnhsIG14LWF1dG8gcmVsYXRpdmVcIj5cbiAgICAgICAgICAgIHsvKiBCYWNrIEJ1dHRvbiAtIE9ubHkgc2hvdyBkdXJpbmcgYXV0b21hdGlvbiBvciBwcm9jZXNzaW5nICovfVxuICAgICAgICAgICAgeyhpc0F1dG9tYXRpb25SdW5uaW5nIHx8IGlzUHJvY2Vzc2luZykgJiYgKFxuICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJhY2tDbGlja31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTAgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBweC00IHB5LTIgYmctZ3JheS04MDAgaG92ZXI6YmctZ3JheS03MDAgdGV4dC13aGl0ZSByb3VuZGVkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTJcIj4mbHQ7PC9zcGFuPlxuICAgICAgICAgICAgICAgIEJhY2tcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7LyogVXNlciBJbmZvIGFuZCBMb2dvdXQgLSBUb3AgcmlnaHQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+V2VsY29tZSwge3VzZXI/LnVzZXJuYW1lfTwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHNcIj57dXNlcj8ucm9sZSA9PT0gJ2FkbWluJyA/ICdBZG1pbmlzdHJhdG9yJyA6ICdVc2VyJ308L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAge3VzZXI/LnJvbGUgPT09ICdhZG1pbicgJiYgKFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2FkbWluJyl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIHRleHQtc20gcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIEFkbWluXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvcXVldWUnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiBiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDAgdGV4dC13aGl0ZSB0ZXh0LXNtIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIFF1ZXVlXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9nb3V0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJnLWdyYXktODAwIGhvdmVyOmJnLWdyYXktNzAwIHRleHQtd2hpdGUgdGV4dC1zbSByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBMb2dvdXRcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtbGlnaHQgdGV4dC13aGl0ZSB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICBTcGFyeCBSZWFkZXJcbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNDAwIG10LTEgdGV4dC1zbVwiPkF1dG9tYXRlZCBRdWVzdGlvbiBTb2x2aW5nPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgey8qIEVuaGFuY2VkIFF1ZXVlL1Byb2Nlc3NpbmcgU3RhdGUgKi99XG4gICAgICAgICAgICB7KGlzSW5RdWV1ZSB8fCBpc1Byb2Nlc3NpbmcpICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IGFuaW1hdGUtZmFkZS1pblwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTkwMCB2aWEtZ3JheS04MDAgdG8tZ3JheS05MDAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLXhsIHAtOCB0ZXh0LWNlbnRlciBzaGFkb3ctMnhsXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctMjAgaC0yMCBteC1hdXRvIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgey8qIE91dGVyIHJvdGF0aW5nIHJpbmcgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBib3JkZXItNCBib3JkZXItYmx1ZS01MDAvMzAgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICB7LyogSW5uZXIgcHVsc2luZyBjb3JlICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTIgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLXB1cnBsZS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctd2hpdGUgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcGluZ1wiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgey8qIEZsb2F0aW5nIHBhcnRpY2xlcyAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgdy0yIGgtMiBiZy1ibHVlLTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTEgLWxlZnQtMSB3LTIgaC0yIGJnLXB1cnBsZS00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3thbmltYXRpb25EZWxheTogJzAuNXMnfX0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIHtpc0luUXVldWUgPyAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTQwMCB0by1vcmFuZ2UtNDAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50IG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIO+/vSBJbiBRdWV1ZVxuICAgICAgICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBtYi02IHRleHQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtxdWV1ZVBvc2l0aW9uID8gYFF1ZXVlIFBvc2l0aW9uOiAjJHtxdWV1ZVBvc2l0aW9ufWAgOiAnQ2hlY2tpbmcgcXVldWUgcG9zaXRpb24uLi4nfVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBtYXgtdy1tZCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNiBoLTYgYm9yZGVyLTQgYm9yZGVyLXllbGxvdy01MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+V2FpdGluZyBmb3IgeW91ciB0dXJuLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTQwMCB0by1wdXJwbGUtNDAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50IG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIPCfmoAgUHJvY2Vzc2luZyBBdXRvbWF0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIG1iLTYgdGV4dC1sZ1wiPkFJIGlzIHNvbHZpbmcgcXVlc3Rpb25zIGluIHRoZSBiYWNrZ3JvdW5kLi4uPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG1heC13LW1kIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtNiBib3JkZXItNCBib3JkZXItYmx1ZS01MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+UHJvY2Vzc2luZyB5b3VyIHJlcXVlc3QuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHsvKiBFbmhhbmNlZCBTUlAgRGFzaGJvYXJkIC0gQWx3YXlzIHZpc2libGUgd2hlbiBhdXRvbWF0aW9uIGlzIHJ1bm5pbmcgKi99XG4gICAgICAgICAgICB7aXNBdXRvbWF0aW9uUnVubmluZyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBhbmltYXRlLWZhZGUtaW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS05MDAgdmlhLWdyYXktODAwIHRvLWdyYXktOTAwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC14bCBwLTYgc2hhZG93LTJ4bFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNDAwIHRvLWJsdWUtNDAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgICAgICAgICAg8J+TiiBMaXZlIFByb2dyZXNzIERhc2hib2FyZFxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgICAgICAgICAgICB7LyogRW5oYW5jZWQgU1JQIFByb2dyZXNzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTI0IGgtMjQgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogQmFja2dyb3VuZCBjaXJjbGUgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMjQgaC0yNCB0cmFuc2Zvcm0gLXJvdGF0ZS05MFwiIHZpZXdCb3g9XCIwIDAgMTAwIDEwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PVwiNTBcIiBjeT1cIjUwXCIgcj1cIjQwXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCI0XCIgZmlsbD1cInRyYW5zcGFyZW50XCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMFwiLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGNpcmNsZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN4PVwiNTBcIiBjeT1cIjUwXCIgcj1cIjQwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJ1cmwoI3NycEdyYWRpZW50KVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCI0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwidHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZURhc2hhcnJheT17YCR7KHNycEVhcm5lZCAvIHBhcnNlSW50KHRhcmdldFNycCB8fCAxKSkgKiAyNTEuMn0gMjUxLjJgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTEwMDAgZWFzZS1vdXQgZHJvcC1zaGFkb3ctbGdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkZWZzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD1cInNycEdyYWRpZW50XCIgeDE9XCIwJVwiIHkxPVwiMCVcIiB4Mj1cIjEwMCVcIiB5Mj1cIjEwMCVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjAlXCIgc3RvcENvbG9yPVwiIzEwQjk4MVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3RvcCBvZmZzZXQ9XCI1MCVcIiBzdG9wQ29sb3I9XCIjM0I4MkY2XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjEwMCVcIiBzdG9wQ29sb3I9XCIjOEI1Q0Y2XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpbmVhckdyYWRpZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2RlZnM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZSBibG9ja1wiPntzcnBFYXJuZWR9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlNSUDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBHbG93aW5nIGVmZmVjdCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCByb3VuZGVkLWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTUwMC8yMCB0by1ibHVlLTUwMC8yMCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTFcIj5TUlAgRWFybmVkPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgdGV4dC14c1wiPlRhcmdldDoge3RhcmdldFNycH08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge01hdGgucm91bmQoKHNycEVhcm5lZCAvIHBhcnNlSW50KHRhcmdldFNycCB8fCAxKSkgKiAxMDApfSUgQ29tcGxldGVcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIEVuaGFuY2VkIFF1ZXN0aW9uIFByb2dyZXNzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNDAwIG1iLTEgYW5pbWF0ZS1wdWxzZVwiPntxdWVzdGlvbk51bWJlcn08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIG1iLTNcIj5RdWVzdGlvbnMgU29sdmVkPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgaC0zIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLWN5YW4tNDAwIGgtMyByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMTAwMCBlYXNlLW91dCByZWxhdGl2ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke01hdGgubWluKChxdWVzdGlvbk51bWJlciAvIDEwKSAqIDEwMCwgMTAwKX0lYCB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLXdoaXRlLzMwIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge01hdGgubWluKE1hdGgucm91bmQoKHF1ZXN0aW9uTnVtYmVyIC8gMTApICogMTAwKSwgMTAwKX0lIG9mIGVzdGltYXRlZCBzZXNzaW9uXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1tZWRpdW1cIj7wn6egIEFJIFByb2Nlc3Npbmc8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBFbmhhbmNlZCBTdGF0dXMgJiBQZXJmb3JtYW5jZSAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MDAgdG8tYmx1ZS01MDAgcm91bmRlZC1mdWxsIG14LWF1dG8gbWItMyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyBiZy13aGl0ZSByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbWItM1wiPlN5c3RlbSBTdGF0dXM8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi00MDAgbWItMlwiPvCfn6IgQWN0aXZlICYgUHJvY2Vzc2luZzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtYi0xXCI+UGVyZm9ybWFuY2UgTWV0cmljczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIOKaoSB7cXVlc3Rpb25OdW1iZXIgPiAwID8gTWF0aC5yb3VuZChlbGFwc2VkVGltZSAvIHF1ZXN0aW9uTnVtYmVyKSA6IDB9cyBwZXIgcXVlc3Rpb25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+OryB7cXVlc3Rpb25OdW1iZXIgPiAwID8gTWF0aC5yb3VuZCgocXVlc3Rpb25OdW1iZXIgLyBlbGFwc2VkVGltZSkgKiA2MCkgOiAwfSBxdWVzdGlvbnMvbWluXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1wdXJwbGUtNDAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICDij7HvuI8ge2Zvcm1hdFRpbWUoZWxhcHNlZFRpbWUpfSBlbGFwc2VkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+8J+agCBBSSBFbmdpbmU8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIEVuaGFuY2VkIEN1cnJlbnQgUXVlc3Rpb24gRGlzcGxheSAqL31cbiAgICAgICAgICAgIHtjdXJyZW50UXVlc3Rpb24gJiYgaXNBdXRvbWF0aW9uUnVubmluZyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBhbmltYXRlLXNsaWRlLXVwXCIga2V5PXthbmltYXRpb25LZXl9PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTkwMCB2aWEtZ3JheS04MDAgdG8tZ3JheS05MDAgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkLXhsIHAtNiBzaGFkb3ctMnhsXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTUwMCB0by1ibHVlLTUwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC13aGl0ZSBmb250LWJvbGQgbXItNCBhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3F1ZXN0aW9uTnVtYmVyfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTQwMCB0by1ibHVlLTQwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg4pyFIFF1ZXN0aW9uIFNvbHZlZFxuICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+QUkgc3VjY2Vzc2Z1bGx5IHByb2Nlc3NlZCB0aGlzIHF1ZXN0aW9uPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC1hdXRvIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcGluZ1wiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmVlbi00MDAgZm9udC1tZWRpdW1cIj5DT01QTEVURUQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmxhY2svNjAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLXhsIHAtNiBtYi02IGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTUwMCB0by1vcmFuZ2UtNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBmbGV4LXNocmluay0wIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBmb250LWJvbGRcIj5RPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIHRleHQtYmFzZSBsZWFkaW5nLXJlbGF4ZWQgbWItM1wiPntjdXJyZW50UXVlc3Rpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMiBweS0xIGJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICt7TWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMykgKyAxfSBTUlBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj7igKIgU29sdmVkIGF1dG9tYXRpY2FsbHk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAge2N1cnJlbnRBbnN3ZXIgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMC8xMCB0by1wdXJwbGUtNTAwLzEwIGJvcmRlciBib3JkZXItYmx1ZS01MDAvMzAgcm91bmRlZC14bCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLXB1cnBsZS01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtYm9sZFwiPkFJPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIGZvbnQtbWVkaXVtIG1iLTJcIj7wn6SWIEFJIFNvbHV0aW9uOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTIwMCBsZWFkaW5nLXJlbGF4ZWRcIj57Y3VycmVudEFuc3dlcn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBFbmhhbmNlZCBTdGFydCBTY3JlZW4gLSBPbmx5IHNob3cgd2hlbiBub3Qgc3RhcnRlZCAqL31cbiAgICAgICAgICAgIHshaGFzU3RhcnRlZCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgYW5pbWF0ZS1mYWRlLWluXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWdyYXktOTAwIHZpYS1ncmF5LTgwMCB0by1ncmF5LTkwMCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQteGwgcC0xMCBtYXgtdy1sZyBteC1hdXRvIHNoYWRvdy0yeGxcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctMTYgaC0xNiBteC1hdXRvIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYW5pbWF0ZS1ib3VuY2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC0yeGxcIj7wn5qAPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0xIC1yaWdodC0xIHctMyBoLTMgYmctYmx1ZS00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcGluZ1wiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xIC1sZWZ0LTEgdy0zIGgtMyBiZy1wdXJwbGUtNDAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXBpbmdcIiBzdHlsZT17e2FuaW1hdGlvbkRlbGF5OiAnMC41cyd9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNDAwIHRvLXB1cnBsZS00MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnQgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIFN0YXJ0IEF1dG9tYXRpb25cbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LWJhc2VcIj7wn6SWIEFJLXBvd2VyZWQgcXVlc3Rpb24gc29sdmluZyBmb3IgU3BhcnggUmVhZGVyPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIHsvKiBFbmhhbmNlZCBMb2dpbiBPcHRpb25zICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTYgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNDAwIHRvLXB1cnBsZS00MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICDwn5SQIENob29zZSBMb2dpbiBNZXRob2RcbiAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVMb2dpbk1ldGhvZFNlbGVjdCgnbm9ybWFsJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweS00IHB4LTYgZm9udC1tZWRpdW0gcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dpbk1ldGhvZCA9PT0gJ25vcm1hbCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNjAwIHRvLWdyZWVuLTUwMCB0ZXh0LXdoaXRlIHJpbmctMiByaW5nLWdyZWVuLTQwMCBzaGFkb3ctbGcgc2hhZG93LWdyZWVuLTUwMC8yNSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNTAwIHRvLWdyZWVuLTYwMCBob3Zlcjpmcm9tLWdyZWVuLTYwMCBob3Zlcjp0by1ncmVlbi01MDAgdGV4dC13aGl0ZSBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LWdyZWVuLTUwMC8yNSdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTMgdGV4dC1sZ1wiPvCfkaQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4LTFcIj5Ob3JtYWwgTG9naW48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bG9naW5NZXRob2QgPT09ICdub3JtYWwnICYmIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTMgdGV4dC1sZ1wiPuKckzwvc3Bhbj59XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVMb2dpbk1ldGhvZFNlbGVjdCgnbWljcm9zb2Z0Jyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweS00IHB4LTYgZm9udC1tZWRpdW0gcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dpbk1ldGhvZCA9PT0gJ21pY3Jvc29mdCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tYmx1ZS01MDAgdGV4dC13aGl0ZSByaW5nLTIgcmluZy1ibHVlLTQwMCBzaGFkb3ctbGcgc2hhZG93LWJsdWUtNTAwLzI1J1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1ibHVlLTYwMCBob3Zlcjpmcm9tLWJsdWUtNjAwIGhvdmVyOnRvLWJsdWUtNTAwIHRleHQtd2hpdGUgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy1ibHVlLTUwMC8yNSdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTMgdGV4dC1sZ1wiPvCfj6I8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4LTFcIj5NaWNyb3NvZnQgTG9naW48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bG9naW5NZXRob2QgPT09ICdtaWNyb3NvZnQnICYmIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTMgdGV4dC1sZ1wiPuKckzwvc3Bhbj59XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVMb2dpbk1ldGhvZFNlbGVjdCgnZ29vZ2xlJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweS00IHB4LTYgZm9udC1tZWRpdW0gcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dpbk1ldGhvZCA9PT0gJ2dvb2dsZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tcmVkLTYwMCB0by1yZWQtNTAwIHRleHQtd2hpdGUgcmluZy0yIHJpbmctcmVkLTQwMCBzaGFkb3ctbGcgc2hhZG93LXJlZC01MDAvMjUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLXJlZC01MDAgdG8tcmVkLTYwMCBob3Zlcjpmcm9tLXJlZC02MDAgaG92ZXI6dG8tcmVkLTUwMCB0ZXh0LXdoaXRlIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3ctcmVkLTUwMC8yNSdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTMgdGV4dC1sZ1wiPvCflI08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4LTFcIj5Hb29nbGUgTG9naW48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bG9naW5NZXRob2QgPT09ICdnb29nbGUnICYmIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTJcIj7inJM8L3NwYW4+fVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTcwMCBwdC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMyB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgU2VsZWN0ZWQ6IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xvZ2luTWV0aG9kID09PSAnbm9ybWFsJyAmJiAnTm9ybWFsIExvZ2luJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xvZ2luTWV0aG9kID09PSAnbWljcm9zb2Z0JyAmJiAnTWljcm9zb2Z0IExvZ2luJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xvZ2luTWV0aG9kID09PSAnZ29vZ2xlJyAmJiAnR29vZ2xlIExvZ2luJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJlZ2luQ2xpY2t9XG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB5LTMgcHgtNiBiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHJvdW5kZWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIEJlZ2luXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIFNSUCBJbnB1dCBNb2RhbCAqL31cbiAgICAgICAgICAgIHtzaG93SW5pdGlhbFNycElucHV0ICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrLzcwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTAgYW5pbWF0ZS1mYWRlLWluXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCBib3JkZXIgYm9yZGVyLWdyYXktODAwIHJvdW5kZWQtbGcgcC02IG1heC13LW1kIHctZnVsbCBteC00IGFuaW1hdGUtc2NhbGUtaW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTUwMCByb3VuZGVkIG14LWF1dG8gbWItNCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC13aGl0ZVwiPio8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIG1iLTJcIj5TZXQgU1JQIFRhcmdldDwvaDI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPkhvdyBtdWNoIFNSUCBkbyB5b3Ugd2FudCB0byBlYXJuPzwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17dGFyZ2V0U3JwfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VGFyZ2V0U3JwKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbktleURvd249eyhlKSA9PiBlLmtleSA9PT0gJ0VudGVyJyAmJiBoYW5kbGVTcnBTdWJtaXQoKX1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHRhcmdldCAoZS5nLiwgNTApXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLWJsYWNrIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItYmx1ZS01MDAgdGV4dC1jZW50ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjFcIlxuICAgICAgICAgICAgICAgICAgICAgIGF1dG9Gb2N1c1xuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBBdXRvbWF0aW9uIHdpbGwgc3RvcCB3aGVuIHRhcmdldCBpcyByZWFjaGVkXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTcnBTdWJtaXR9XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCAhdGFyZ2V0U3JwfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB5LTIuNSBweC00IGJnLWJsdWUtNTAwIGhvdmVyOmJnLWJsdWUtNjAwIGRpc2FibGVkOmJnLWdyYXktNjAwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcm91bmRlZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBib3JkZXItMiBib3JkZXItd2hpdGUgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBtci0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgU3RhcnRpbmcuLi5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6ICdTdGFydCd9XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dJbml0aWFsU3JwSW5wdXQoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHktMi41IHB4LTQgYmctZ3JheS03MDAgaG92ZXI6YmctZ3JheS02MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCB0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIHJvdW5kZWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7LyogQ3JlZGVudGlhbCBJbnB1dCBNb2RhbCAqL31cbiAgICAgICAgICAgIHtzaG93Q3JlZGVudGlhbElucHV0ICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrLzcwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTAgYW5pbWF0ZS1mYWRlLWluXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCBib3JkZXIgYm9yZGVyLWdyYXktODAwIHJvdW5kZWQtbGcgcC02IG1heC13LW1kIHctZnVsbCBteC00IGFuaW1hdGUtc2NhbGUtaW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ibHVlLTUwMCByb3VuZGVkIG14LWF1dG8gbWItNCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC13aGl0ZVwiPvCflJA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIG1iLTJcIj5Mb2dpbiBDcmVkZW50aWFsczwvaDI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtsb2dpbk1ldGhvZCA9PT0gJ25vcm1hbCcgJiYgJ0VudGVyIHlvdXIgU3BhcnggTGVhcm5pbmcgY3JlZGVudGlhbHMnfVxuICAgICAgICAgICAgICAgICAgICAgIHtsb2dpbk1ldGhvZCA9PT0gJ21pY3Jvc29mdCcgJiYgJ0VudGVyIHlvdXIgTWljcm9zb2Z0IGFjY291bnQgY3JlZGVudGlhbHMnfVxuICAgICAgICAgICAgICAgICAgICAgIHtsb2dpbk1ldGhvZCA9PT0gJ2dvb2dsZScgJiYgJ0VudGVyIHlvdXIgR29vZ2xlIGFjY291bnQgY3JlZGVudGlhbHMnfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgey8qIENyZWRlbnRpYWwgTW9kZSBUb2dnbGUgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGJnLWdyYXktODAwIHJvdW5kZWQtbGcgcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q3JlZGVudGlhbE1vZGUoJ2VudGVyJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgcHktMiBweC0zIHJvdW5kZWQgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBjcmVkZW50aWFsTW9kZSA9PT0gJ2VudGVyJyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlJyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBFbnRlciBDcmVkZW50aWFsc1xuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldENyZWRlbnRpYWxNb2RlKCdrZXknKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweS0yIHB4LTMgcm91bmRlZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNyZWRlbnRpYWxNb2RlID09PSAna2V5JyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlJyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBVc2UgTG9naW4gS2V5XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHtjcmVkZW50aWFsTW9kZSA9PT0gJ2VudGVyJyA/IChcbiAgICAgICAgICAgICAgICAgICAgLyogRW50ZXIgQ3JlZGVudGlhbHMgTW9kZSAqL1xuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3VzZXJTY2hvb2x9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFVzZXJTY2hvb2woZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTY2hvb2wgbmFtZSAoZS5nLiwgdGhlYW5nbWVyaW5nc2Nob29sKVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLWJsYWNrIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0ZvY3VzXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17dXNlckVtYWlsfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRVc2VyRW1haWwoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYmctYmxhY2sgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS01MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17dXNlclBhc3N3b3JkfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRVc2VyUGFzc3dvcmQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJQYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLWJsYWNrIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICDwn5KhIFlvdXIgY3JlZGVudGlhbHMgd2lsbCBiZSBlbmNyeXB0ZWQgYW5kIHN0b3JlZCBzZWN1cmVseS4gWW91J2xsIHJlY2VpdmUgYSBsb2dpbiBrZXkgZm9yIGZ1dHVyZSB1c2UuXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIC8qIFVzZSBMb2dpbiBLZXkgTW9kZSAqL1xuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2xvZ2luS2V5fVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRMb2dpbktleShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgbG9naW4ga2V5IChlLmcuLCBTTEstQUJDMTIzNDUpXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYmctYmxhY2sgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCByb3VuZGVkIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS01MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOmJvcmRlci1ibHVlLTUwMCB0ZXh0LWNlbnRlciBmb250LW1vbm9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0ZvY3VzXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICB7c2F2ZWRDcmVkZW50aWFscy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtYi0yXCI+WW91ciBzYXZlZCBsb2dpbiBrZXlzOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbWF4LWgtMzIgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3NhdmVkQ3JlZGVudGlhbHMubWFwKChjcmVkLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRMb2dpbktleShjcmVkLmxvZ2luS2V5KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBweC0zIHB5LTIgYmctZ3JheS04MDAgaG92ZXI6YmctZ3JheS03MDAgcm91bmRlZCB0ZXh0LXNtIHRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LWJsdWUtNDAwXCI+e2NyZWQubG9naW5LZXl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQteHNcIj4oe2NyZWQubG9naW5NZXRob2R9KTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICDwn5SRIFVzZSB5b3VyIHByZXZpb3VzbHkgZ2VuZXJhdGVkIGxvZ2luIGtleSB0byBhY2Nlc3Mgc2F2ZWQgY3JlZGVudGlhbHMuXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMyBtdC02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ3JlZGVudGlhbFN1Ym1pdH1cbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHktMi41IHB4LTQgYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDAgZGlzYWJsZWQ6YmctZ3JheS02MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSByb3VuZGVkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBib3JkZXItMiBib3JkZXItd2hpdGUgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBtci0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFByb2Nlc3NpbmcuLi5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApIDogJ0NvbnRpbnVlJ31cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2hvd0NyZWRlbnRpYWxJbnB1dChmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRIYXNTdGFydGVkKGZhbHNlKTtcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweS0yLjUgcHgtNCBiZy1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTYwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcm91bmRlZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIEJvb2sgQ29uZmlybWF0aW9uICovfVxuICAgICAgICAgICAge3Nob3dCb29rQ29uZmlybWF0aW9uICYmIGJvb2tUaXRsZSAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay83MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwIGFuaW1hdGUtZmFkZS1pblwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAgYm9yZGVyIGJvcmRlci1ncmF5LTgwMCByb3VuZGVkLWxnIHAtNiBtYXgtdy1sZyB3LWZ1bGwgbXgtNCBhbmltYXRlLXNjYWxlLWluXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctYmx1ZS01MDAgcm91bmRlZCBteC1hdXRvIG1iLTQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtd2hpdGVcIj5CPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBtYi0yXCI+Qm9vayBGb3VuZDwvaDI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPkNvbmZpcm0gdG8gc3RhcnQgYXV0b21hdGlvbjwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsYWNrLzMwIHJvdW5kZWQgcC00IG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBtYi00XCI+e2Jvb2tUaXRsZX08L2gzPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXhzXCI+Q3VycmVudCBTUlA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS00MDBcIj57Y3VycmVudFNycH08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPlRhcmdldCBTUlA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS00MDBcIj57dGFyZ2V0U3JwfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlWWVzQ2xpY2t9XG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB5LTIuNSBweC00IGJnLWJsdWUtNTAwIGhvdmVyOmJnLWJsdWUtNjAwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcm91bmRlZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBib3JkZXItMiBib3JkZXItd2hpdGUgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpbiBtci0yXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFN0YXJ0aW5nLi4uXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgKSA6ICdZZXMsIFN0YXJ0J31cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTm9DbGlja31cbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHktMi41IHB4LTQgYmctZ3JheS03MDAgaG92ZXI6YmctZ3JheS02MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSByb3VuZGVkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nID8gJ0ZpbmRpbmcuLi4nIDogJ0ZpbmQgRGlmZmVyZW50J31cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7LyogUXVlc3Rpb24gSGlzdG9yeSAqL31cbiAgICAgICAgICAgIHtxdWVzdGlvbkhpc3RvcnkubGVuZ3RoID4gMCAmJiBpc0F1dG9tYXRpb25SdW5uaW5nICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLWZhZGUtaW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIGJvcmRlciBib3JkZXItZ3JheS04MDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0zXCI+SDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgUXVlc3Rpb24gSGlzdG9yeVxuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zIG1heC1oLTYwIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgICB7cXVlc3Rpb25IaXN0b3J5LnNsaWNlKC01KS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJiZy1ibGFjay8zMCByb3VuZGVkIHAtMyBib3JkZXItbC00IGJvcmRlci1ibHVlLTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgZm9udC1tZWRpdW0gdGV4dC1zbVwiPlF7aXRlbS5udW1iZXJ9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIHRleHQteHNcIj5Tb2x2ZWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBtYi0yXCI+e2l0ZW0ucXVlc3Rpb24uc3Vic3RyaW5nKDAsIDEwMCl9Li4uPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXhzXCI+QW5zd2VyOiB7aXRlbS5hbnN3ZXJ9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBTdGF0dXMgTWVzc2FnZXMgKi99XG4gICAgICAgICAgICB7bWVzc2FnZSAmJiAhc2hvd0luaXRpYWxTcnBJbnB1dCAmJiAhc2hvd0Jvb2tDb25maXJtYXRpb24gJiYgIWlzUHJvY2Vzc2luZyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiB0ZXh0LWNlbnRlciBhbmltYXRlLWZhZGUtaW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIGJvcmRlciBib3JkZXItZ3JheS04MDAgcm91bmRlZCBwLTQgaW5saW5lLWJsb2NrXCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+e21lc3NhZ2V9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBQbGF5d3JpZ2h0IEluc3RhbGxhdGlvbiAqL31cbiAgICAgICAgICAgIHtuZWVkc1BsYXl3cmlnaHQgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2svNzAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei01MCBhbmltYXRlLWZhZGUtaW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIGJvcmRlciBib3JkZXItZ3JheS04MDAgcm91bmRlZC1sZyBwLTYgbWF4LXctbWQgdy1mdWxsIG14LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1yZWQtNTAwIHJvdW5kZWQgbXgtYXV0byBtYi00IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LXdoaXRlXCI+ITwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgbWItMlwiPlNldHVwIFJlcXVpcmVkPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+UGxheXdyaWdodCBicm93c2VycyBuZWVkIHRvIGJlIGluc3RhbGxlZDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsYWNrLzUwIHJvdW5kZWQgcC00IG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIG1iLTJcIj5SdW4gdGhpcyBjb21tYW5kIGluIHlvdXIgdGVybWluYWw6PC9wPlxuICAgICAgICAgICAgICAgICAgICA8Y29kZSBjbGFzc05hbWU9XCJiZy1ibGFjayB0ZXh0LWJsdWUtNDAwIHAtMiByb3VuZGVkIGJsb2NrIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICBucHggcGxheXdyaWdodCBpbnN0YWxsIGNocm9taXVtXG4gICAgICAgICAgICAgICAgICAgIDwvY29kZT5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQteHMgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgQWZ0ZXIgaW5zdGFsbGF0aW9uLCByZWZyZXNoIHRoaXMgcGFnZSBhbmQgdHJ5IGFnYWluLlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBMaWNlbnNlIFJlbmV3YWwgTW9kYWwgKi99XG4gICAgICAgICAgICB7c2hvd0xpY2Vuc2VSZW5ld2FsICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrLzgwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTAgYW5pbWF0ZS1mYWRlLWluXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCBib3JkZXIgYm9yZGVyLWdyYXktODAwIHJvdW5kZWQtbGcgcC04IG1heC13LW1kIHctZnVsbCBteC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmcteWVsbG93LTUwMC8yMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNTAwIHRleHQtMnhsXCI+4pqg77iPPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBtYi0yXCI+TGljZW5zZSBSZW5ld2FsIFJlcXVpcmVkPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2xpY2Vuc2VTdGF0dXMgJiYgbGljZW5zZVN0YXR1cy5saWNlbnNlX3N0YXR1cyA9PT0gJ2V4cGlyZWQnICYmICdZb3VyIGxpY2Vuc2UgaGFzIGV4cGlyZWQuJ31cbiAgICAgICAgICAgICAgICAgICAgICB7bGljZW5zZVN0YXR1cyAmJiBsaWNlbnNlU3RhdHVzLmxpY2Vuc2Vfc3RhdHVzID09PSAnbWF4ZWRfb3V0JyAmJiAnWW91ciBsaWNlbnNlIGhhcyByZWFjaGVkIG1heGltdW0gdXNlcy4nfVxuICAgICAgICAgICAgICAgICAgICAgIHtsaWNlbnNlU3RhdHVzICYmIGxpY2Vuc2VTdGF0dXMubGljZW5zZV9zdGF0dXMgPT09ICdpbmFjdGl2ZScgJiYgJ1lvdXIgbGljZW5zZSBpcyBpbmFjdGl2ZS4nfVxuICAgICAgICAgICAgICAgICAgICAgIHsoIWxpY2Vuc2VTdGF0dXMgfHwgbGljZW5zZVN0YXR1cy5saWNlbnNlX3N0YXR1cyA9PT0gJ3ZhbGlkJykgJiYgJ1lvdXIgbGljZW5zZSBpcyBub3QgdmFsaWQuJ31cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc20gbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIFBsZWFzZSBlbnRlciBhIG5ldyBsaWNlbnNlIGtleSB0byBjb250aW51ZSB1c2luZyB0aGUgYm90LlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAge2xpY2Vuc2VTdGF0dXMgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsYWNrLzUwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZCBwLTQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtIG1iLTJcIj5DdXJyZW50IExpY2Vuc2UgSW5mbzo8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHA+S2V5OiB7bGljZW5zZVN0YXR1cy5rZXlfY29kZSB8fCAnTi9BJ308L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5TdGF0dXM6IDxzcGFuIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxpY2Vuc2VTdGF0dXMubGljZW5zZV9zdGF0dXMgPT09ICdleHBpcmVkJyA/ICd0ZXh0LXJlZC00MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbGljZW5zZVN0YXR1cy5saWNlbnNlX3N0YXR1cyA9PT0gJ21heGVkX291dCcgPyAndGV4dC1vcmFuZ2UtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxpY2Vuc2VTdGF0dXMubGljZW5zZV9zdGF0dXMgPT09ICdpbmFjdGl2ZScgPyAndGV4dC1ncmF5LTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC1ncmVlbi00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtsaWNlbnNlU3RhdHVzLmxpY2Vuc2Vfc3RhdHVzID09PSAnZXhwaXJlZCcgJiYgJ0V4cGlyZWQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bGljZW5zZVN0YXR1cy5saWNlbnNlX3N0YXR1cyA9PT0gJ21heGVkX291dCcgJiYgJ01heCBVc2VzIFJlYWNoZWQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bGljZW5zZVN0YXR1cy5saWNlbnNlX3N0YXR1cyA9PT0gJ2luYWN0aXZlJyAmJiAnSW5hY3RpdmUnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bGljZW5zZVN0YXR1cy5saWNlbnNlX3N0YXR1cyA9PT0gJ3ZhbGlkJyAmJiAnVmFsaWQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtsaWNlbnNlU3RhdHVzLmV4cGlyZXNfYXQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5FeHBpcmVzOiB7bmV3IERhdGUobGljZW5zZVN0YXR1cy5leHBpcmVzX2F0KS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAge2xpY2Vuc2VTdGF0dXMubWF4X3VzZXMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5Vc2VzOiB7bGljZW5zZVN0YXR1cy5jdXJyZW50X3VzZXMgfHwgMH0ve2xpY2Vuc2VTdGF0dXMubWF4X3VzZXN9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gbWItMlwiPk5ldyBMaWNlbnNlIEtleTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3TGljZW5zZUtleX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5ld0xpY2Vuc2VLZXkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBuZXcgbGljZW5zZSBrZXkgKGUuZy4sIFNSWC1YWFhYWFhYWC1YWFhYWFhYWC1YWFhYWFhYWC1YWFhYWFhYWFhYKVwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBiZy1ibGFjayBib3JkZXIgYm9yZGVyLWdyYXktNzAwIHJvdW5kZWQgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDAgZm9jdXM6b3V0bGluZS1ub25lXCJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bGljZW5zZVJlbmV3YWxMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUxpY2Vuc2VSZW5ld2FsfVxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsaWNlbnNlUmVuZXdhbExvYWRpbmcgfHwgIW5ld0xpY2Vuc2VLZXkudHJpbSgpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTYwMCBkaXNhYmxlZDpiZy1ncmF5LTYwMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdGV4dC13aGl0ZSBmb250LW1lZGl1bSBweS0zIHB4LTYgcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2xpY2Vuc2VSZW5ld2FsTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUmVuZXdpbmcuLi5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAnUmVuZXcgTGljZW5zZSdcbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dMaWNlbnNlUmVuZXdhbChmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXROZXdMaWNlbnNlS2V5KCcnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldE1lc3NhZ2UoJycpO1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xpY2Vuc2VSZW5ld2FsTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC02IHB5LTMgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ym9yZGVyLWdyYXktNTAwIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBTY3JlZW5zaG90IERpc3BsYXkgKi99XG4gICAgICAgICAgICB7c2NyZWVuc2hvdCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBhbmltYXRlLWZhZGUtaW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIGJvcmRlciBib3JkZXItZ3JheS04MDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0zXCI+Uzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgQnJvd3NlciBTdGF0ZVxuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm91bmRlZCBib3JkZXIgYm9yZGVyLWdyYXktNzAwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz17c2NyZWVuc2hvdH0gYWx0PVwiQnJvd3NlciBzY3JlZW5zaG90XCIgY2xhc3NOYW1lPVwidy1mdWxsIGgtYXV0b1wiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJTcGFyeFJlYWRlclBhZ2UiLCJpc0F1dGhlbnRpY2F0ZWQiLCJzZXRJc0F1dGhlbnRpY2F0ZWQiLCJ1c2VyIiwic2V0VXNlciIsImF1dGhMb2FkaW5nIiwic2V0QXV0aExvYWRpbmciLCJyb3V0ZXIiLCJtZXNzYWdlIiwic2V0TWVzc2FnZSIsImJvb2tUaXRsZSIsInNldEJvb2tUaXRsZSIsIm5lZWRzUGxheXdyaWdodCIsInNldE5lZWRzUGxheXdyaWdodCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2NyZWVuc2hvdCIsInNldFNjcmVlbnNob3QiLCJzdG9yeUNvbnRlbnQiLCJzZXRTdG9yeUNvbnRlbnQiLCJzaG93Qm9va0NvbmZpcm1hdGlvbiIsInNldFNob3dCb29rQ29uZmlybWF0aW9uIiwiY3VycmVudFNycCIsInNldEN1cnJlbnRTcnAiLCJ0YXJnZXRTcnAiLCJzZXRUYXJnZXRTcnAiLCJzaG93SW5pdGlhbFNycElucHV0Iiwic2V0U2hvd0luaXRpYWxTcnBJbnB1dCIsImN1cnJlbnRRdWVzdGlvbiIsInNldEN1cnJlbnRRdWVzdGlvbiIsImN1cnJlbnRBbnN3ZXIiLCJzZXRDdXJyZW50QW5zd2VyIiwicXVlc3Rpb25OdW1iZXIiLCJzZXRRdWVzdGlvbk51bWJlciIsInNycEVhcm5lZCIsInNldFNycEVhcm5lZCIsImlzQXV0b21hdGlvblJ1bm5pbmciLCJzZXRJc0F1dG9tYXRpb25SdW5uaW5nIiwicXVlc3Rpb25IaXN0b3J5Iiwic2V0UXVlc3Rpb25IaXN0b3J5IiwiYW5pbWF0aW9uS2V5Iiwic2V0QW5pbWF0aW9uS2V5IiwiaXNQcm9jZXNzaW5nIiwic2V0SXNQcm9jZXNzaW5nIiwiaGFzU3RhcnRlZCIsInNldEhhc1N0YXJ0ZWQiLCJhdXRvbWF0aW9uQ29tcGxldGUiLCJzZXRBdXRvbWF0aW9uQ29tcGxldGUiLCJxdWV1ZVBvc2l0aW9uIiwic2V0UXVldWVQb3NpdGlvbiIsImpvYklkIiwic2V0Sm9iSWQiLCJpc0luUXVldWUiLCJzZXRJc0luUXVldWUiLCJsb2dpbk1ldGhvZCIsInNldExvZ2luTWV0aG9kIiwic2hvd0NyZWRlbnRpYWxJbnB1dCIsInNldFNob3dDcmVkZW50aWFsSW5wdXQiLCJjcmVkZW50aWFsTW9kZSIsInNldENyZWRlbnRpYWxNb2RlIiwidXNlclNjaG9vbCIsInNldFVzZXJTY2hvb2wiLCJ1c2VyRW1haWwiLCJzZXRVc2VyRW1haWwiLCJ1c2VyUGFzc3dvcmQiLCJzZXRVc2VyUGFzc3dvcmQiLCJsb2dpbktleSIsInNldExvZ2luS2V5Iiwic2F2ZWRDcmVkZW50aWFscyIsInNldFNhdmVkQ3JlZGVudGlhbHMiLCJzaG93TGljZW5zZVJlbmV3YWwiLCJzZXRTaG93TGljZW5zZVJlbmV3YWwiLCJsaWNlbnNlU3RhdHVzIiwic2V0TGljZW5zZVN0YXR1cyIsIm5ld0xpY2Vuc2VLZXkiLCJzZXROZXdMaWNlbnNlS2V5IiwibGljZW5zZVJlbmV3YWxMb2FkaW5nIiwic2V0TGljZW5zZVJlbmV3YWxMb2FkaW5nIiwiY2hlY2tBdXRoZW50aWNhdGlvbiIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJwcmV2IiwibmV3TnVtIiwicHJldlNycCIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsInByZXZLZXkiLCJzYW1wbGVRdWVzdGlvbnMiLCJzYW1wbGVBbnN3ZXJzIiwicmFuZG9tSW5kZXgiLCJsZW5ndGgiLCJudW1iZXIiLCJxdWVzdGlvbiIsImFuc3dlciIsInBhcnNlSW50Iiwic2V0VGltZW91dCIsImNsZWFySW50ZXJ2YWwiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJwdXNoIiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJkYXRhIiwianNvbiIsInN1Y2Nlc3MiLCJ2YWxpZCIsInJlbW92ZUl0ZW0iLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVMb2dvdXQiLCJtZXRob2QiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiaGFuZGxlTG9naW5NZXRob2RTZWxlY3QiLCJjaGVja0xpY2Vuc2VWYWxpZGl0eSIsInJvbGUiLCJsaWNlbnNlX3N0YXR1cyIsImVycm9yTWVzc2FnZSIsInN0YXR1cyIsImhhbmRsZUxpY2Vuc2VSZW5ld2FsIiwidHJpbSIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiaGFuZGxlQmVnaW5DbGljayIsImxpY2Vuc2VDaGVjayIsImxvYWRTYXZlZENyZWRlbnRpYWxzIiwiY3JlZGVudGlhbHMiLCJoYW5kbGVDcmVkZW50aWFsU3VibWl0IiwiY29uZmlybSIsInNjaG9vbCIsImVtYWlsIiwicGFzc3dvcmQiLCJhbGVydCIsImhhbmRsZVNycFN1Ym1pdCIsImlzTmFOIiwiaXNOb3JtYWxMb2dpbiIsImlzTWljcm9zb2Z0TG9naW4iLCJpc0dvb2dsZUxvZ2luIiwiYXBpRW5kcG9pbnQiLCJyZXF1ZXN0Qm9keSIsInVybCIsImhhbmRsZVllc0NsaWNrIiwiam9iX3R5cGUiLCJqb2JfZGF0YSIsInNlbGVjdGVkU2Nob29sIiwidXNlcm5hbWUiLCJsb2dpbl90eXBlIiwic3JwX3RhcmdldCIsInByaW9yaXR5Iiwiam9iX2lkIiwic3RhcnRRdWV1ZU1vbml0b3JpbmciLCJjaGVja1Bvc2l0aW9uIiwicG9zaXRpb24iLCJzdGFydEpvYk1vbml0b3JpbmciLCJoYW5kbGVKb2JDb21wbGV0aW9uIiwicmVzdWx0IiwiY2hlY2tKb2JTdGF0dXMiLCJqb2IiLCJoYW5kbGVOb0NsaWNrIiwiaGFuZGxlQmFja0NsaWNrIiwiaGVhZGVyIiwiYnV0dG9uIiwib25DbGljayIsInNwYW4iLCJoMSIsIm1haW4iLCJzdHlsZSIsImFuaW1hdGlvbkRlbGF5IiwiaDIiLCJoMyIsInN2ZyIsInZpZXdCb3giLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwiZmlsbCIsInN0cm9rZURhc2hhcnJheSIsInN0cm9rZUxpbmVjYXAiLCJkZWZzIiwibGluZWFyR3JhZGllbnQiLCJpZCIsIngxIiwieTEiLCJ4MiIsInkyIiwic3RvcCIsIm9mZnNldCIsInN0b3BDb2xvciIsInJvdW5kIiwid2lkdGgiLCJtaW4iLCJlbGFwc2VkVGltZSIsImZvcm1hdFRpbWUiLCJkaXNhYmxlZCIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib25LZXlEb3duIiwia2V5IiwicGxhY2Vob2xkZXIiLCJhdXRvRm9jdXMiLCJtYXAiLCJjcmVkIiwiaW5kZXgiLCJzbGljZSIsIml0ZW0iLCJzdWJzdHJpbmciLCJjb2RlIiwia2V5X2NvZGUiLCJleHBpcmVzX2F0IiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsIm1heF91c2VzIiwiY3VycmVudF91c2VzIiwibGFiZWwiLCJpbWciLCJzcmMiLCJhbHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.jsx\n"));

/***/ })

});