"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/queue/status/route";
exports.ids = ["app/api/queue/status/route"];
exports.modules = {

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fstatus%2Froute&page=%2Fapi%2Fqueue%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fstatus%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fstatus%2Froute&page=%2Fapi%2Fqueue%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fstatus%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_queue_status_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/queue/status/route.js */ \"(rsc)/./app/api/queue/status/route.js\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/queue/status/route\",\n        pathname: \"/api/queue/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/queue/status/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\queue\\\\status\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_queue_status_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/queue/status/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZxdWV1ZSUyRnN0YXR1cyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGcXVldWUlMkZzdGF0dXMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZxdWV1ZSUyRnN0YXR1cyUyRnJvdXRlLmpzJmFwcERpcj1EJTNBJTVDcmVhZGVyLWF1dG8tbWFpbiU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q3JlYWRlci1hdXRvLW1haW4maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ3FCO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsdUdBQXVHO0FBQy9HO0FBQ2lKOztBQUVqSiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLz80MjhjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkQ6XFxcXHJlYWRlci1hdXRvLW1haW5cXFxcYXBwXFxcXGFwaVxcXFxxdWV1ZVxcXFxzdGF0dXNcXFxccm91dGUuanNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3F1ZXVlL3N0YXR1cy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3F1ZXVlL3N0YXR1c1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvcXVldWUvc3RhdHVzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiRDpcXFxccmVhZGVyLWF1dG8tbWFpblxcXFxhcHBcXFxcYXBpXFxcXHF1ZXVlXFxcXHN0YXR1c1xcXFxyb3V0ZS5qc1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9xdWV1ZS9zdGF0dXMvcm91dGVcIjtcbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgaGVhZGVySG9va3MsIHN0YXRpY0dlbmVyYXRpb25CYWlsb3V0LCBvcmlnaW5hbFBhdGhuYW1lLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fstatus%2Froute&page=%2Fapi%2Fqueue%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fstatus%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/queue/status/route.js":
/*!***************************************!*\
  !*** ./app/api/queue/status/route.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/database */ \"(rsc)/./lib/database.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_lib_database__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_webhook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/webhook */ \"(rsc)/./lib/webhook.js\");\n/* harmony import */ var _lib_webhook__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lib_webhook__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./lib/auth.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_lib_auth__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../lib/queueMiddleware */ \"(rsc)/./lib/queueMiddleware.js\");\n/* harmony import */ var _lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Middleware wrapper for Next.js API routes\nfunction withMiddleware(handler, middlewares) {\n    return async (request, context)=>{\n        const req = {\n            ...request,\n            body: await request.json().catch(()=>({})),\n            user: null,\n            licenseFeatures: null,\n            url: request.url\n        };\n        // Authenticate user first\n        try {\n            const auth = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthManager)();\n            const authHeader = request.headers.get(\"authorization\");\n            console.log(\"Auth header:\", authHeader ? `Present: ${authHeader.substring(0, 20)}...` : \"Missing\");\n            console.log(\"All headers:\", Object.fromEntries(request.headers.entries()));\n            if (!authHeader?.startsWith(\"Bearer \")) {\n                console.log(\"❌ No Bearer token found\");\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            const token = authHeader.substring(7);\n            const session = auth.validateSession(token);\n            req.user = {\n                id: session.userId,\n                username: session.username,\n                role: session.role\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error.message);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Authentication failed\"\n            }, {\n                status: 401\n            });\n        }\n        // Apply middlewares\n        for (const middleware of middlewares){\n            try {\n                let nextCalled = false;\n                const next = ()=>{\n                    nextCalled = true;\n                };\n                // Create a mock res object for Express-style middleware\n                const res = {\n                    status: (code)=>({\n                            json: (data)=>next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n                                    status: code\n                                })\n                        })\n                };\n                const result = await middleware(req, res, next);\n                if (result instanceof next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n                    return result;\n                }\n                if (!nextCalled) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Middleware blocked request\"\n                    }, {\n                        status: 403\n                    });\n                }\n            } catch (error) {\n                console.error(\"Middleware error:\", error);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Request processing failed\"\n                }, {\n                    status: 500\n                });\n            }\n        }\n        return handler(req, context);\n    };\n}\n// GET - Get queue status and statistics\nasync function GET(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const url = new URL(request.url);\n            const detailed = url.searchParams.get(\"detailed\") === \"true\";\n            const includeJobs = url.searchParams.get(\"include_jobs\") === \"true\";\n            // Get user's queue statistics\n            const userBatches = db.getQueueBatches(req.user.id, null, 10, 0);\n            const userJobs = db.db.prepare(`\r\n        SELECT * FROM queue_jobs \r\n        WHERE user_id = ? \r\n        ORDER BY created_at DESC \r\n        LIMIT 20\r\n      `).all(req.user.id);\n            // Get overall queue statistics (if admin or detailed view)\n            let globalStats = null;\n            if (req.user.role === \"admin\" || detailed) {\n                globalStats = db.getQueueStats();\n            }\n            // Get basic global queue overview for all users\n            const globalOverview = db.db.prepare(`\r\n        SELECT\r\n          COUNT(DISTINCT u.id) as total_users,\r\n          COUNT(DISTINCT qb.id) as total_batches,\r\n          COUNT(CASE WHEN qj.status = 'queued' THEN 1 END) as queued_jobs,\r\n          COUNT(CASE WHEN qj.status = 'processing' THEN 1 END) as processing_jobs,\r\n          COUNT(CASE WHEN qj.status = 'completed' THEN 1 END) as completed_jobs_today,\r\n          COUNT(CASE WHEN qj.status = 'failed' THEN 1 END) as failed_jobs_today\r\n        FROM users u\r\n        LEFT JOIN queue_batches qb ON u.id = qb.user_id\r\n          AND DATE(qb.created_at) = DATE('now')\r\n        LEFT JOIN queue_jobs qj ON qb.id = qj.batch_id\r\n          AND DATE(qj.created_at) = DATE('now')\r\n        WHERE u.role != 'admin'\r\n      `).get();\n            // Get user's position in queue for pending jobs\n            const userQueuePositions = db.db.prepare(`\r\n        SELECT \r\n          qj.id,\r\n          qj.status,\r\n          qj.effective_priority,\r\n          qj.created_at,\r\n          (\r\n            SELECT COUNT(*) \r\n            FROM queue_jobs qj2 \r\n            WHERE qj2.status = 'queued' \r\n            AND (\r\n              qj2.effective_priority > qj.effective_priority \r\n              OR (qj2.effective_priority = qj.effective_priority AND qj2.created_at < qj.created_at)\r\n            )\r\n          ) + 1 as queue_position\r\n        FROM queue_jobs qj\r\n        WHERE qj.user_id = ? AND qj.status = 'queued'\r\n        ORDER BY qj.effective_priority DESC, qj.created_at ASC\r\n      `).all(req.user.id);\n            // Calculate estimated wait times\n            const avgProcessingTime = db.db.prepare(`\r\n        SELECT AVG(julianday(completed_at) - julianday(started_at)) * 24 * 60 as avg_minutes\r\n        FROM queue_jobs \r\n        WHERE status = 'completed' AND started_at IS NOT NULL AND completed_at IS NOT NULL\r\n      `).get()?.avg_minutes || 5; // Default 5 minutes if no data\n            const queuedJobsAhead = db.db.prepare(`\r\n        SELECT COUNT(*) as count \r\n        FROM queue_jobs \r\n        WHERE status = 'queued'\r\n      `).get()?.count || 0;\n            const processingJobs = db.db.prepare(`\r\n        SELECT COUNT(*) as count \r\n        FROM queue_jobs \r\n        WHERE status = 'processing'\r\n      `).get()?.count || 0;\n            // Estimate wait time based on queue position and processing rate\n            const estimatedWaitMinutes = Math.max(0, (queuedJobsAhead - processingJobs) * avgProcessingTime);\n            // Get recent activity\n            const recentActivity = db.getActivityLogs(req.user.id, 10, 0);\n            const response = {\n                user_queue_status: {\n                    total_batches: userBatches.length,\n                    active_batches: userBatches.filter((b)=>[\n                            \"pending\",\n                            \"processing\"\n                        ].includes(b.status)).length,\n                    completed_batches: userBatches.filter((b)=>b.status === \"completed\").length,\n                    failed_batches: userBatches.filter((b)=>b.status === \"failed\").length,\n                    total_jobs: userJobs.length,\n                    queued_jobs: userJobs.filter((j)=>j.status === \"queued\").length,\n                    processing_jobs: userJobs.filter((j)=>j.status === \"processing\").length,\n                    completed_jobs: userJobs.filter((j)=>j.status === \"completed\").length,\n                    failed_jobs: userJobs.filter((j)=>j.status === \"failed\").length\n                },\n                queue_positions: userQueuePositions,\n                estimated_wait_time: {\n                    minutes: Math.round(estimatedWaitMinutes),\n                    formatted: formatWaitTime(estimatedWaitMinutes)\n                },\n                license_features: req.licenseFeatures,\n                recent_activity: recentActivity.slice(0, 5),\n                global_overview: {\n                    total_users: globalOverview.total_users || 0,\n                    total_batches_today: globalOverview.total_batches || 0,\n                    queue_status: {\n                        queued_jobs: globalOverview.queued_jobs || 0,\n                        processing_jobs: globalOverview.processing_jobs || 0,\n                        completed_jobs_today: globalOverview.completed_jobs_today || 0,\n                        failed_jobs_today: globalOverview.failed_jobs_today || 0\n                    }\n                }\n            };\n            if (detailed) {\n                response.detailed_batches = userBatches.map((batch)=>({\n                        ...batch,\n                        jobs: includeJobs ? db.getBatchJobs(batch.id) : []\n                    }));\n            }\n            if (globalStats && req.user.role === \"admin\") {\n                response.global_statistics = {\n                    ...globalStats,\n                    system_health: {\n                        queue_load: queuedJobsAhead > 100 ? \"high\" : queuedJobsAhead > 50 ? \"medium\" : \"low\",\n                        processing_capacity: processingJobs,\n                        average_processing_time_minutes: Math.round(avgProcessingTime),\n                        estimated_system_wait_minutes: Math.round(estimatedWaitMinutes)\n                    }\n                };\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response);\n        } catch (error) {\n            console.error(\"Get queue status error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to retrieve queue status\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().validateLicenseFeatures)\n    ]);\n    return handler(request);\n}\n// POST - Queue management actions (admin only)\nasync function POST(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            if (req.user.role !== \"admin\") {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Admin access required\"\n                }, {\n                    status: 403\n                });\n            }\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n            const { action, job_ids, batch_ids, priority_adjustment } = req.body;\n            if (!action) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"action is required\"\n                }, {\n                    status: 400\n                });\n            }\n            let result = {\n                success: true,\n                affected_items: 0\n            };\n            switch(action){\n                case \"apply_starvation_prevention\":\n                    const starvationResult = db.applyStarvationPrevention();\n                    result.affected_items = starvationResult.changes;\n                    result.message = `Applied starvation prevention to ${result.affected_items} jobs`;\n                    await webhook.sendQueueAlert(\"info\", \"Starvation prevention applied\", {\n                        admin_user: req.user.username,\n                        affected_jobs: result.affected_items\n                    });\n                    break;\n                case \"cancel_jobs\":\n                    if (!job_ids || !Array.isArray(job_ids)) {\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            error: \"job_ids array is required for cancel_jobs action\"\n                        }, {\n                            status: 400\n                        });\n                    }\n                    const cancelStmt = db.db.prepare(`\r\n            UPDATE queue_jobs \r\n            SET status = 'cancelled' \r\n            WHERE id = ? AND status IN ('queued', 'processing')\r\n          `);\n                    job_ids.forEach((jobId)=>{\n                        const cancelResult = cancelStmt.run(jobId);\n                        if (cancelResult.changes > 0) {\n                            result.affected_items++;\n                        }\n                    });\n                    result.message = `Cancelled ${result.affected_items} jobs`;\n                    await webhook.sendQueueAlert(\"info\", \"Jobs cancelled by admin\", {\n                        admin_user: req.user.username,\n                        cancelled_jobs: result.affected_items,\n                        total_requested: job_ids.length\n                    });\n                    break;\n                case \"cancel_batches\":\n                    if (!batch_ids || !Array.isArray(batch_ids)) {\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            error: \"batch_ids array is required for cancel_batches action\"\n                        }, {\n                            status: 400\n                        });\n                    }\n                    batch_ids.forEach((batchId)=>{\n                        const updateResult = db.updateBatchStatus(batchId, \"cancelled\");\n                        if (updateResult.changes > 0) {\n                            result.affected_items++;\n                            // Cancel all jobs in the batch\n                            const cancelJobsStmt = db.db.prepare(`\r\n                UPDATE queue_jobs \r\n                SET status = 'cancelled' \r\n                WHERE batch_id = ? AND status IN ('queued', 'processing')\r\n              `);\n                            cancelJobsStmt.run(batchId);\n                        }\n                    });\n                    result.message = `Cancelled ${result.affected_items} batches`;\n                    await webhook.sendQueueAlert(\"info\", \"Batches cancelled by admin\", {\n                        admin_user: req.user.username,\n                        cancelled_batches: result.affected_items,\n                        total_requested: batch_ids.length\n                    });\n                    break;\n                case \"adjust_priority\":\n                    if (!job_ids || !Array.isArray(job_ids) || priority_adjustment === undefined) {\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            error: \"job_ids array and priority_adjustment are required\"\n                        }, {\n                            status: 400\n                        });\n                    }\n                    if (priority_adjustment < 0 || priority_adjustment > 10) {\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            error: \"priority_adjustment must be between 0 and 10\"\n                        }, {\n                            status: 400\n                        });\n                    }\n                    job_ids.forEach((jobId)=>{\n                        const updateResult = db.updateJobPriority(jobId, priority_adjustment, true);\n                        if (updateResult.changes > 0) {\n                            result.affected_items++;\n                        }\n                    });\n                    result.message = `Adjusted priority for ${result.affected_items} jobs to level ${priority_adjustment}`;\n                    await webhook.sendPriorityAdjustment(`bulk_${result.affected_items}_jobs`, \"various\", priority_adjustment, \"Admin bulk priority adjustment\", req.user.username);\n                    break;\n                case \"clear_completed\":\n                    const clearStmt = db.db.prepare(`\r\n            DELETE FROM queue_jobs \r\n            WHERE status = 'completed' AND completed_at < datetime('now', '-7 days')\r\n          `);\n                    const clearResult = clearStmt.run();\n                    result.affected_items = clearResult.changes;\n                    result.message = `Cleared ${result.affected_items} completed jobs older than 7 days`;\n                    break;\n                case \"system_maintenance\":\n                    // Perform various maintenance tasks\n                    const maintenanceTasks = [\n                        {\n                            name: \"cleanup_expired_sessions\",\n                            result: db.cleanupExpiredSessions()\n                        },\n                        {\n                            name: \"cleanup_old_logs\",\n                            result: db.cleanupOldLogs(30)\n                        },\n                        {\n                            name: \"apply_starvation_prevention\",\n                            result: db.applyStarvationPrevention()\n                        }\n                    ];\n                    result.maintenance_results = maintenanceTasks.map((task)=>({\n                            task: task.name,\n                            affected_items: task.result.changes || 0\n                        }));\n                    result.affected_items = maintenanceTasks.reduce((sum, task)=>sum + (task.result.changes || 0), 0);\n                    result.message = `System maintenance completed, ${result.affected_items} items processed`;\n                    await webhook.sendQueueAlert(\"maintenance\", \"System maintenance completed\", {\n                        admin_user: req.user.username,\n                        tasks_completed: maintenanceTasks.length,\n                        total_items_processed: result.affected_items\n                    });\n                    break;\n                default:\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Invalid action\",\n                        valid_actions: [\n                            \"apply_starvation_prevention\",\n                            \"cancel_jobs\",\n                            \"cancel_batches\",\n                            \"adjust_priority\",\n                            \"clear_completed\",\n                            \"system_maintenance\"\n                        ]\n                    }, {\n                        status: 400\n                    });\n            }\n            // Log the admin action\n            db.logActivity(req.user.id, \"ADMIN_QUEUE_ACTION\", `Performed ${action}: ${result.message}`);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(result);\n        } catch (error) {\n            console.error(\"Queue management action error:\", error);\n            const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n            await webhook.sendQueueAlert(\"system_error\", \"Queue management action failed\", {\n                admin_user: req.user?.username || \"unknown\",\n                action: req.body?.action || \"unknown\",\n                error: error.message\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Queue management action failed\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().validateLicenseFeatures)\n    ]);\n    return handler(request);\n}\nfunction formatWaitTime(minutes) {\n    if (minutes < 1) {\n        return \"Less than 1 minute\";\n    } else if (minutes < 60) {\n        return `${Math.round(minutes)} minutes`;\n    } else if (minutes < 1440) {\n        const hours = Math.floor(minutes / 60);\n        const remainingMinutes = Math.round(minutes % 60);\n        return `${hours} hour${hours > 1 ? \"s\" : \"\"}${remainingMinutes > 0 ? ` ${remainingMinutes} minutes` : \"\"}`;\n    } else {\n        const days = Math.floor(minutes / 1440);\n        const remainingHours = Math.floor(minutes % 1440 / 60);\n        return `${days} day${days > 1 ? \"s\" : \"\"}${remainingHours > 0 ? ` ${remainingHours} hours` : \"\"}`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/queue/status/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-this-in-production\";\nconst TOKEN_EXPIRY = \"7d\"; // 7 days\nclass AuthManager {\n    constructor(){\n        this.db = getDatabase();\n    }\n    // Generate JWT token\n    generateToken(user) {\n        const payload = {\n            userId: user.id,\n            username: user.username,\n            role: user.role,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jwt.sign(payload, JWT_SECRET, {\n            expiresIn: TOKEN_EXPIRY\n        });\n    }\n    // Verify JWT token\n    verifyToken(token) {\n        try {\n            return jwt.verify(token, JWT_SECRET);\n        } catch (error) {\n            throw new Error(\"Invalid token\");\n        }\n    }\n    // Hash token for database storage\n    hashToken(token) {\n        return crypto.createHash(\"sha256\").update(token).digest(\"hex\");\n    }\n    // Create session with token\n    createSession(user, ipAddress = null, userAgent = null) {\n        const token = this.generateToken(user);\n        const tokenHash = this.hashToken(token);\n        // Calculate expiry date\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days\n        // Store session in database\n        this.db.createSession(user.id, tokenHash, expiresAt.toISOString(), ipAddress, userAgent);\n        // Log activity\n        this.db.logActivity(user.id, \"LOGIN\", `User logged in from ${ipAddress}`, ipAddress);\n        return {\n            token,\n            user: {\n                id: user.id,\n                username: user.username,\n                role: user.role,\n                lastLogin: user.last_login\n            },\n            expiresAt: expiresAt.toISOString()\n        };\n    }\n    // Validate session\n    validateSession(token) {\n        try {\n            // First verify JWT\n            const decoded = this.verifyToken(token);\n            // Then check database session\n            const tokenHash = this.hashToken(token);\n            const session = this.db.validateSession(tokenHash);\n            if (!session || !session.user_active) {\n                throw new Error(\"Session invalid or user inactive\");\n            }\n            return {\n                userId: session.user_id,\n                username: session.username,\n                role: session.role,\n                sessionId: session.id\n            };\n        } catch (error) {\n            throw new Error(\"Invalid session\");\n        }\n    }\n    // Logout user\n    logout(token, userId = null) {\n        const tokenHash = this.hashToken(token);\n        this.db.invalidateSession(tokenHash);\n        if (userId) {\n            this.db.logActivity(userId, \"LOGOUT\", \"User logged out\");\n        }\n    }\n    // Logout all sessions for user\n    logoutAllSessions(userId) {\n        this.db.invalidateAllUserSessions(userId);\n        this.db.logActivity(userId, \"LOGOUT_ALL\", \"All sessions invalidated\");\n    }\n    // Middleware for protecting routes\n    requireAuth(requiredRole = null) {\n        return async (req, res, next)=>{\n            try {\n                const authHeader = req.headers.authorization;\n                if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n                    return res.status(401).json({\n                        error: \"No token provided\"\n                    });\n                }\n                const token = authHeader.substring(7);\n                const session = await this.validateSession(token);\n                // Check role if required\n                if (requiredRole && session.role !== requiredRole) {\n                    return res.status(403).json({\n                        error: \"Insufficient permissions\"\n                    });\n                }\n                // Add user info to request\n                req.user = session;\n                req.token = token;\n                next();\n            } catch (error) {\n                return res.status(401).json({\n                    error: error.message\n                });\n            }\n        };\n    }\n    // Admin only middleware\n    requireAdmin() {\n        return this.requireAuth(\"admin\");\n    }\n    // Extract IP address from request (Next.js compatible)\n    getClientIP(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || req.ip || \"127.0.0.1\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"x-forwarded-for\"] || req.connection?.remoteAddress || req.socket?.remoteAddress || (req.connection?.socket ? req.connection.socket.remoteAddress : null) || \"127.0.0.1\";\n    }\n    // Extract user agent (Next.js compatible)\n    getUserAgent(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"user-agent\") || \"Unknown\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"user-agent\"] || \"Unknown\";\n    }\n    // Rate limiting helper\n    checkRateLimit(identifier, maxAttempts = 5, windowMinutes = 15) {\n        // This is a simple in-memory rate limiter\n        // In production, you might want to use Redis or database\n        if (!this.rateLimitStore) {\n            this.rateLimitStore = new Map();\n        }\n        const now = Date.now();\n        const windowMs = windowMinutes * 60 * 1000;\n        const key = `rate_limit_${identifier}`;\n        if (!this.rateLimitStore.has(key)) {\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        const record = this.rateLimitStore.get(key);\n        if (now > record.resetTime) {\n            // Reset the window\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        if (record.count >= maxAttempts) {\n            return {\n                allowed: false,\n                remaining: 0,\n                resetTime: record.resetTime\n            };\n        }\n        record.count++;\n        return {\n            allowed: true,\n            remaining: maxAttempts - record.count\n        };\n    }\n    // Clean up expired rate limit entries\n    cleanupRateLimit() {\n        if (!this.rateLimitStore) return;\n        const now = Date.now();\n        for (const [key, record] of this.rateLimitStore.entries()){\n            if (now > record.resetTime) {\n                this.rateLimitStore.delete(key);\n            }\n        }\n    }\n    // Password strength validation\n    validatePasswordStrength(password) {\n        const minLength = 8;\n        const hasUpperCase = /[A-Z]/.test(password);\n        const hasLowerCase = /[a-z]/.test(password);\n        const hasNumbers = /\\d/.test(password);\n        const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n        const errors = [];\n        if (password.length < minLength) {\n            errors.push(`Password must be at least ${minLength} characters long`);\n        }\n        if (!hasUpperCase) {\n            errors.push(\"Password must contain at least one uppercase letter\");\n        }\n        if (!hasLowerCase) {\n            errors.push(\"Password must contain at least one lowercase letter\");\n        }\n        if (!hasNumbers) {\n            errors.push(\"Password must contain at least one number\");\n        }\n        if (!hasSpecialChar) {\n            errors.push(\"Password must contain at least one special character\");\n        }\n        return {\n            isValid: errors.length === 0,\n            errors,\n            strength: this.calculatePasswordStrength(password)\n        };\n    }\n    calculatePasswordStrength(password) {\n        let score = 0;\n        // Length bonus\n        score += Math.min(password.length * 2, 20);\n        // Character variety bonus\n        if (/[a-z]/.test(password)) score += 5;\n        if (/[A-Z]/.test(password)) score += 5;\n        if (/[0-9]/.test(password)) score += 5;\n        if (/[^A-Za-z0-9]/.test(password)) score += 10;\n        // Penalty for common patterns\n        if (/(.)\\1{2,}/.test(password)) score -= 10; // Repeated characters\n        if (/123|abc|qwe/i.test(password)) score -= 10; // Sequential patterns\n        if (score < 30) return \"weak\";\n        if (score < 60) return \"medium\";\n        return \"strong\";\n    }\n}\n// Export singleton instance\nlet authInstance = null;\nfunction getAuthManager() {\n    if (!authInstance) {\n        authInstance = new AuthManager();\n    }\n    return authInstance;\n}\nmodule.exports = {\n    getAuthManager,\n    AuthManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/database.js":
/*!*************************!*\
  !*** ./lib/database.js ***!
  \*************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\nconst bcrypt = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/umd/index.js\");\nconst { v4: uuidv4 } = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/cjs/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nclass DatabaseManager {\n    constructor(){\n        const dbPath = path.join(process.cwd(), \"data\", \"app.db\");\n        this.db = new Database(dbPath);\n        this.initializeTables();\n        this.createDefaultAdmin();\n    }\n    initializeTables() {\n        // Users table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        username TEXT UNIQUE NOT NULL,\n        password_hash TEXT NOT NULL,\n        role TEXT DEFAULT 'user' CHECK(role IN ('user', 'admin')),\n        license_key_id INTEGER,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        last_login DATETIME,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id)\n      )\n    `);\n        // License keys table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_keys (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        key_code TEXT UNIQUE NOT NULL,\n        duration_days INTEGER NOT NULL,\n        max_uses INTEGER NOT NULL DEFAULT 1,\n        current_uses INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        expires_at DATETIME NOT NULL,\n        is_active BOOLEAN DEFAULT 1,\n        created_by INTEGER,\n        features TEXT DEFAULT '[]',\n        FOREIGN KEY (created_by) REFERENCES users (id)\n      )\n    `);\n        // User sessions table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS user_sessions (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        token_hash TEXT NOT NULL,\n        expires_at DATETIME NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        ip_address TEXT,\n        user_agent TEXT,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Activity logs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER,\n        action TEXT NOT NULL,\n        details TEXT,\n        ip_address TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // System settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        setting_key TEXT UNIQUE NOT NULL,\n        setting_value TEXT,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_by INTEGER,\n        FOREIGN KEY (updated_by) REFERENCES users (id)\n      )\n    `);\n        // Encrypted login credentials table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS encrypted_credentials (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        login_key TEXT UNIQUE NOT NULL,\n        login_method TEXT NOT NULL CHECK(login_method IN ('normal', 'microsoft', 'google')),\n        encrypted_school TEXT NOT NULL,\n        encrypted_email TEXT NOT NULL,\n        encrypted_password TEXT NOT NULL,\n        encryption_iv TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // License feature settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_feature_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        license_key_id INTEGER NOT NULL,\n        max_accounts_per_batch INTEGER DEFAULT 0,\n        priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),\n        scheduling_access BOOLEAN DEFAULT 0,\n        multi_user_access BOOLEAN DEFAULT 0,\n        max_batches_per_day INTEGER DEFAULT 1,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id),\n        UNIQUE(license_key_id)\n      )\n    `);\n        // Queue batches table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_batches (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        batch_name TEXT,\n        login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft')),\n        total_accounts INTEGER NOT NULL,\n        processed_accounts INTEGER DEFAULT 0,\n        failed_accounts INTEGER DEFAULT 0,\n        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        scheduled_time DATETIME,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Queue jobs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_jobs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        batch_id INTEGER,\n        user_id INTEGER NOT NULL,\n        job_type TEXT NOT NULL DEFAULT 'sparx_reader',\n        job_data TEXT NOT NULL,\n        status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        effective_priority INTEGER DEFAULT 0,\n        scheduled_time DATETIME,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        error_message TEXT,\n        retry_count INTEGER DEFAULT 0,\n        max_retries INTEGER DEFAULT 3,\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id),\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Queue schedules table for conflict detection\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_schedules (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        scheduled_time DATETIME NOT NULL,\n        duration_minutes INTEGER DEFAULT 30,\n        job_id INTEGER,\n        batch_id INTEGER,\n        status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (job_id) REFERENCES queue_jobs (id),\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id)\n      )\n    `);\n        // Create indexes for better performance\n        this.db.exec(`\n      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n      CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_code ON license_keys(key_code);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_expires ON license_keys(expires_at);\n      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);\n      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_created ON activity_logs(created_at);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_user ON encrypted_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_key ON encrypted_credentials(login_key);\n      CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time);\n    `);\n        // Migration: Add max_batches_per_day column if it doesn't exist\n        try {\n            const columns = this.db.prepare(\"PRAGMA table_info(license_feature_settings)\").all();\n            const hasMaxBatchesPerDay = columns.some((col)=>col.name === \"max_batches_per_day\");\n            if (!hasMaxBatchesPerDay) {\n                console.log(\"Adding max_batches_per_day column to license_feature_settings...\");\n                this.db.exec(`ALTER TABLE license_feature_settings ADD COLUMN max_batches_per_day INTEGER DEFAULT 1`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for max_batches_per_day:\", error);\n        }\n        // Migration: Add login_type column to queue_batches if it doesn't exist\n        try {\n            const batchColumns = this.db.prepare(\"PRAGMA table_info(queue_batches)\").all();\n            const hasLoginType = batchColumns.some((col)=>col.name === \"login_type\");\n            if (!hasLoginType) {\n                console.log(\"Adding login_type column to queue_batches...\");\n                this.db.exec(`ALTER TABLE queue_batches ADD COLUMN login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft'))`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for login_type:\", error);\n        }\n    }\n    createDefaultAdmin() {\n        const adminExists = this.db.prepare(\"SELECT id FROM users WHERE role = ? LIMIT 1\").get(\"admin\");\n        if (!adminExists) {\n            const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);\n            const stmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, role, is_active)\n        VALUES (?, ?, ?, ?)\n      `);\n            stmt.run(process.env.DEFAULT_ADMIN_USERNAME, hashedPassword, \"admin\", 1);\n            console.log(`Default admin user created: ${process.env.DEFAULT_ADMIN_USERNAME}/[hidden]`);\n        }\n    }\n    // User management methods\n    createUser(username, password, licenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // Validate license key\n            const licenseStmt = this.db.prepare(`\n        SELECT id, max_uses, current_uses, expires_at, is_active \n        FROM license_keys \n        WHERE key_code = ? AND is_active = 1\n      `);\n            const license = licenseStmt.get(licenseKey);\n            if (!license) {\n                throw new Error(\"Invalid license key\");\n            }\n            if (new Date(license.expires_at) < new Date()) {\n                throw new Error(\"License key has expired\");\n            }\n            if (license.current_uses >= license.max_uses) {\n                throw new Error(\"License key has reached maximum uses\");\n            }\n            // Check if username already exists\n            const userExists = this.db.prepare(\"SELECT id FROM users WHERE username = ?\").get(username);\n            if (userExists) {\n                throw new Error(\"Username already exists\");\n            }\n            // Create user\n            const hashedPassword = bcrypt.hashSync(password, 12);\n            const userStmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, license_key_id, role)\n        VALUES (?, ?, ?, ?)\n      `);\n            const result = userStmt.run(username, hashedPassword, license.id, \"user\");\n            // Update license key usage\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(license.id);\n            return result.lastInsertRowid;\n        });\n        return transaction();\n    }\n    authenticateUser(username, password) {\n        const stmt = this.db.prepare(`\n      SELECT u.*, lk.expires_at as license_expires, lk.is_active as license_active\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.username = ? AND u.is_active = 1\n    `);\n        const user = stmt.get(username);\n        if (!user) {\n            throw new Error(\"Invalid credentials\");\n        }\n        const isValidPassword = bcrypt.compareSync(password, user.password_hash);\n        if (!isValidPassword) {\n            throw new Error(\"Invalid credentials\");\n        }\n        // Check license validity for non-admin users\n        if (user.role !== \"admin\") {\n            if (!user.license_active || new Date(user.license_expires) < new Date()) {\n                throw new Error(\"License has expired\");\n            }\n        }\n        // Update last login\n        const updateStmt = this.db.prepare(\"UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?\");\n        updateStmt.run(user.id);\n        // Remove sensitive data\n        delete user.password_hash;\n        return user;\n    }\n    // License key management methods\n    createLicenseKey(durationDays, maxUses = 1, features = [], createdBy = null) {\n        const keyCode = this.generateLicenseKey();\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + durationDays);\n        const stmt = this.db.prepare(`\n      INSERT INTO license_keys (key_code, duration_days, max_uses, expires_at, created_by, features)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(keyCode, durationDays, maxUses, expiresAt.toISOString(), createdBy, JSON.stringify(features));\n        return {\n            id: result.lastInsertRowid,\n            keyCode,\n            durationDays,\n            maxUses,\n            expiresAt: expiresAt.toISOString(),\n            features\n        };\n    }\n    generateLicenseKey() {\n        const segments = [];\n        for(let i = 0; i < 4; i++){\n            segments.push(uuidv4().replace(/-/g, \"\").substring(0, 8).toUpperCase());\n        }\n        return `SRX-${segments.join(\"-\")}`;\n    }\n    getLicenseKeys(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        lk.*,\n        u.username as created_by_username,\n        COUNT(users.id) as users_count\n      FROM license_keys lk\n      LEFT JOIN users u ON lk.created_by = u.id\n      LEFT JOIN users ON users.license_key_id = lk.id\n      GROUP BY lk.id\n      ORDER BY lk.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    deactivateLicenseKey(keyId) {\n        const stmt = this.db.prepare(\"UPDATE license_keys SET is_active = 0 WHERE id = ?\");\n        return stmt.run(keyId);\n    }\n    // Get detailed license information for a user\n    getUserLicenseStatus(userId) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id as user_id,\n        u.username,\n        lk.id as license_id,\n        lk.key_code,\n        lk.max_uses,\n        lk.current_uses,\n        lk.expires_at,\n        lk.is_active,\n        CASE \n          WHEN lk.expires_at <= datetime('now') THEN 'expired'\n          WHEN lk.current_uses >= lk.max_uses THEN 'maxed_out'\n          WHEN lk.is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as license_status\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.id = ?\n    `);\n        return stmt.get(userId);\n    }\n    // Validate a license key for renewal (check if it's valid and has available uses)\n    validateLicenseForRenewal(licenseKey) {\n        const stmt = this.db.prepare(`\n      SELECT \n        id,\n        key_code,\n        max_uses,\n        current_uses,\n        expires_at,\n        is_active,\n        CASE \n          WHEN expires_at <= datetime('now') THEN 'expired'\n          WHEN current_uses >= max_uses THEN 'maxed_out'\n          WHEN is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as status\n      FROM license_keys \n      WHERE key_code = ?\n    `);\n        const license = stmt.get(licenseKey);\n        if (!license) {\n            return {\n                valid: false,\n                error: \"License key not found\"\n            };\n        }\n        if (license.status !== \"valid\") {\n            let errorMessage = \"License key is not valid\";\n            switch(license.status){\n                case \"expired\":\n                    errorMessage = \"License key has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"License key has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"License key is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage\n            };\n        }\n        return {\n            valid: true,\n            license\n        };\n    }\n    // Renew user's license with a new license key\n    renewUserLicense(userId, newLicenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // First validate the new license key\n            const validation = this.validateLicenseForRenewal(newLicenseKey);\n            if (!validation.valid) {\n                throw new Error(validation.error);\n            }\n            const newLicense = validation.license;\n            // Update user's license_key_id to the new license\n            const updateUserStmt = this.db.prepare(`\n        UPDATE users \n        SET license_key_id = ?, updated_at = CURRENT_TIMESTAMP \n        WHERE id = ?\n      `);\n            updateUserStmt.run(newLicense.id, userId);\n            // Increment the new license's current_uses\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(newLicense.id);\n            // Log the renewal activity\n            this.logActivity(userId, \"LICENSE_RENEWED\", `License renewed with key: ${newLicenseKey}`);\n            return {\n                success: true,\n                newLicenseId: newLicense.id,\n                newLicenseKey: newLicenseKey,\n                expiresAt: newLicense.expires_at,\n                maxUses: newLicense.max_uses,\n                currentUses: newLicense.current_uses + 1\n            };\n        });\n        return transaction();\n    }\n    // Session management\n    createSession(userId, tokenHash, expiresAt, ipAddress = null, userAgent = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, tokenHash, expiresAt, ipAddress, userAgent);\n    }\n    validateSession(tokenHash) {\n        const stmt = this.db.prepare(`\n      SELECT s.*, u.username, u.role, u.is_active as user_active\n      FROM user_sessions s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')\n    `);\n        return stmt.get(tokenHash);\n    }\n    invalidateSession(tokenHash) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?\");\n        return stmt.run(tokenHash);\n    }\n    invalidateAllUserSessions(userId) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE user_id = ?\");\n        return stmt.run(userId);\n    }\n    // Activity logging\n    logActivity(userId, action, details = null, ipAddress = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO activity_logs (user_id, action, details, ip_address)\n      VALUES (?, ?, ?, ?)\n    `);\n        return stmt.run(userId, action, details, ipAddress);\n    }\n    getActivityLogs(userId = null, limit = 100, offset = 0) {\n        let query = `\n      SELECT \n        al.*,\n        u.username\n      FROM activity_logs al\n      LEFT JOIN users u ON al.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE al.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY al.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Analytics methods\n    getSystemStats() {\n        const stats = {};\n        // Total users\n        stats.totalUsers = this.db.prepare(\"SELECT COUNT(*) as count FROM users WHERE role = 'user'\").get().count;\n        // Active users (logged in within last 30 days)\n        stats.activeUsers = this.db.prepare(`\n      SELECT COUNT(*) as count FROM users \n      WHERE role = 'user' AND last_login > datetime('now', '-30 days')\n    `).get().count;\n        // Total license keys\n        stats.totalLicenseKeys = this.db.prepare(\"SELECT COUNT(*) as count FROM license_keys\").get().count;\n        // Active license keys\n        stats.activeLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE is_active = 1 AND expires_at > datetime('now')\n    `).get().count;\n        // Expired license keys\n        stats.expiredLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE expires_at <= datetime('now')\n    `).get().count;\n        // Recent activity (last 24 hours)\n        stats.recentActivity = this.db.prepare(`\n      SELECT COUNT(*) as count FROM activity_logs \n      WHERE created_at > datetime('now', '-1 day')\n    `).get().count;\n        return stats;\n    }\n    // User management for admin\n    getUsers(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id,\n        u.username,\n        u.role,\n        u.created_at,\n        u.last_login,\n        u.is_active,\n        lk.key_code,\n        lk.expires_at as license_expires\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    toggleUserStatus(userId) {\n        const stmt = this.db.prepare(\"UPDATE users SET is_active = NOT is_active WHERE id = ?\");\n        return stmt.run(userId);\n    }\n    // Cleanup methods\n    cleanupExpiredSessions() {\n        const stmt = this.db.prepare('DELETE FROM user_sessions WHERE expires_at <= datetime(\"now\")');\n        return stmt.run();\n    }\n    cleanupOldLogs(daysToKeep = 90) {\n        const stmt = this.db.prepare(`\n      DELETE FROM activity_logs \n      WHERE created_at <= datetime('now', '-${daysToKeep} days')\n    `);\n        return stmt.run();\n    }\n    // Encrypted credentials methods\n    saveEncryptedCredentials(userId, loginMethod, school, email, password, encryptionKey) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        // Generate a unique login key\n        const loginKey = \"SLK-\" + crypto.randomBytes(8).toString(\"hex\").toUpperCase();\n        // Create encryption IV\n        const iv = crypto.randomBytes(16);\n        const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n        // Encrypt school, email and password\n        const cipher1 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedSchool = cipher1.update(school, \"utf8\", \"hex\") + cipher1.final(\"hex\");\n        const cipher2 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedEmail = cipher2.update(email, \"utf8\", \"hex\") + cipher2.final(\"hex\");\n        const cipher3 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedPassword = cipher3.update(password, \"utf8\", \"hex\") + cipher3.final(\"hex\");\n        const stmt = this.db.prepare(`\n      INSERT INTO encrypted_credentials (user_id, login_key, login_method, encrypted_school, encrypted_email, encrypted_password, encryption_iv)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        stmt.run(userId, loginKey, loginMethod, encryptedSchool, encryptedEmail, encryptedPassword, iv.toString(\"hex\"));\n        return loginKey;\n    }\n    getEncryptedCredentials(loginKey, encryptionKey) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM encrypted_credentials \n      WHERE login_key = ? AND is_active = 1\n    `);\n        const result = stmt.get(loginKey);\n        if (!result) return null;\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        try {\n            const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n            const iv = Buffer.from(result.encryption_iv, \"hex\");\n            // Decrypt school, email and password\n            const decipher1 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const school = result.encrypted_school ? decipher1.update(result.encrypted_school, \"hex\", \"utf8\") + decipher1.final(\"utf8\") : null;\n            const decipher2 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const email = result.encrypted_email ? decipher2.update(result.encrypted_email, \"hex\", \"utf8\") + decipher2.final(\"utf8\") : null;\n            const decipher3 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const password = result.encrypted_password ? decipher3.update(result.encrypted_password, \"hex\", \"utf8\") + decipher3.final(\"utf8\") : null;\n            return {\n                loginMethod: result.login_method,\n                school,\n                email,\n                password,\n                userId: result.user_id\n            };\n        } catch (error) {\n            console.error(\"Failed to decrypt credentials:\", error);\n            return null;\n        }\n    }\n    getUserCredentials(userId) {\n        const stmt = this.db.prepare(`\n      SELECT login_key, login_method, created_at FROM encrypted_credentials \n      WHERE user_id = ? AND is_active = 1\n      ORDER BY created_at DESC\n    `);\n        return stmt.all(userId);\n    }\n    deactivateCredentials(loginKey) {\n        const stmt = this.db.prepare(\"UPDATE encrypted_credentials SET is_active = 0 WHERE login_key = ?\");\n        return stmt.run(loginKey);\n    }\n    // License Feature Settings Methods\n    setLicenseFeatures(licenseKeyId, features) {\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO license_feature_settings\n      (license_key_id, max_accounts_per_batch, priority_level, scheduling_access, max_batches_per_day, updated_at)\n      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(licenseKeyId, features.max_accounts_per_batch || 0, features.priority_level || 0, features.scheduling_access ? 1 : 0, features.max_batches_per_day || 1);\n    }\n    getLicenseFeatures(licenseKeyId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM license_feature_settings WHERE license_key_id = ?\n    `);\n        const result = stmt.get(licenseKeyId);\n        if (!result) {\n            // Return default features if none set\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    getUserLicenseFeatures(userId) {\n        const stmt = this.db.prepare(`\n      SELECT lfs.* FROM license_feature_settings lfs\n      JOIN users u ON u.license_key_id = lfs.license_key_id\n      WHERE u.id = ?\n    `);\n        const result = stmt.get(userId);\n        if (!result) {\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    // Daily batch count check\n    getUserDailyBatchCount(userId, date = null) {\n        const targetDate = date || new Date().toISOString().split(\"T\")[0]; // YYYY-MM-DD format\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_batches\n      WHERE user_id = ?\n      AND DATE(created_at) = ?\n    `);\n        const result = stmt.get(userId, targetDate);\n        return result.count;\n    }\n    // Queue Batch Methods\n    createQueueBatch(userId, batchName, accounts, scheduledTime = null, loginType = \"normal\") {\n        const transaction = this.db.transaction(()=>{\n            // Get user's license features\n            const features = this.getUserLicenseFeatures(userId);\n            // Check daily batch limit\n            const dailyBatchCount = this.getUserDailyBatchCount(userId);\n            if (dailyBatchCount >= features.max_batches_per_day) {\n                throw new Error(`Daily batch limit reached (${features.max_batches_per_day} batches per day). Please try again tomorrow.`);\n            }\n            // Validate batch size against license limits\n            if (features.max_accounts_per_batch > 0 && accounts.length > features.max_accounts_per_batch) {\n                throw new Error(`Batch size (${accounts.length}) exceeds license limit (${features.max_accounts_per_batch})`);\n            }\n            // Validate scheduling access\n            if (scheduledTime && !features.scheduling_access) {\n                throw new Error(\"Scheduling access not available for this license\");\n            }\n            // Validate login type\n            if (![\n                \"normal\",\n                \"google\",\n                \"microsoft\"\n            ].includes(loginType)) {\n                throw new Error(\"Invalid login type specified\");\n            }\n            // Create batch\n            const batchStmt = this.db.prepare(`\n        INSERT INTO queue_batches (user_id, batch_name, login_type, total_accounts, priority_level, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?)\n      `);\n            const batchResult = batchStmt.run(userId, batchName, loginType, accounts.length, features.priority_level, scheduledTime);\n            const batchId = batchResult.lastInsertRowid;\n            // Create individual jobs for each account\n            const jobStmt = this.db.prepare(`\n        INSERT INTO queue_jobs (batch_id, user_id, job_data, priority_level, effective_priority, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?)\n      `);\n            accounts.forEach((account)=>{\n                const effectivePriority = this.calculateEffectivePriority(features.priority_level, scheduledTime);\n                jobStmt.run(batchId, userId, JSON.stringify(account), features.priority_level, effectivePriority, scheduledTime);\n            });\n            // Create schedule entry if scheduled\n            if (scheduledTime) {\n                this.createScheduleEntry(userId, scheduledTime, null, batchId);\n            }\n            // Log activity\n            this.logActivity(userId, \"BATCH_CREATED\", `Created batch: ${batchName} with ${accounts.length} accounts`);\n            return batchId;\n        });\n        return transaction();\n    }\n    calculateEffectivePriority(basePriority, scheduledTime) {\n        let effectivePriority = basePriority;\n        // Boost priority for scheduled jobs approaching their time\n        if (scheduledTime) {\n            const now = new Date();\n            const scheduled = new Date(scheduledTime);\n            const timeDiff = scheduled.getTime() - now.getTime();\n            const hoursUntil = timeDiff / (1000 * 60 * 60);\n            if (hoursUntil <= 1) {\n                effectivePriority += 5; // High boost for jobs due within an hour\n            } else if (hoursUntil <= 6) {\n                effectivePriority += 2; // Medium boost for jobs due within 6 hours\n            }\n        }\n        // Apply starvation prevention (boost priority for old jobs)\n        // This would be implemented in a background process\n        return Math.min(effectivePriority, 10); // Cap at maximum priority\n    }\n    getQueueBatches(userId = null, status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT qb.*, u.username\n      FROM queue_batches qb\n      JOIN users u ON qb.user_id = u.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (userId) {\n            query += \" AND qb.user_id = ?\";\n            params.push(userId);\n        }\n        if (status) {\n            query += \" AND qb.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY qb.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getBatchJobs(batchId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_jobs \n      WHERE batch_id = ? \n      ORDER BY effective_priority DESC, created_at ASC\n    `);\n        return stmt.all(batchId);\n    }\n    updateBatchStatus(batchId, status, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_batches \n      SET status = ?, completed_at = ?, updated_at = CURRENT_TIMESTAMP\n      WHERE id = ?\n    `);\n        return stmt.run(status, completedAt, batchId);\n    }\n    // Queue Job Methods\n    getNextQueueJob() {\n        const stmt = this.db.prepare(`\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n      WHERE qj.status = 'queued' \n      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))\n      ORDER BY qj.effective_priority DESC, qj.created_at ASC\n      LIMIT 1\n    `);\n        return stmt.get();\n    }\n    updateJobStatus(jobId, status, errorMessage = null, startedAt = null, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET status = ?, error_message = ?, started_at = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, errorMessage, startedAt, completedAt, jobId);\n    }\n    incrementJobRetry(jobId) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET retry_count = retry_count + 1, status = 'queued'\n      WHERE id = ? AND retry_count < max_retries\n    `);\n        return stmt.run(jobId);\n    }\n    // Scheduling Methods\n    createScheduleEntry(userId, scheduledTime, jobId = null, batchId = null, durationMinutes = 30) {\n        // Check for conflicts\n        const conflicts = this.checkScheduleConflicts(userId, scheduledTime, durationMinutes);\n        if (conflicts.length > 0) {\n            throw new Error(`Schedule conflict detected at ${scheduledTime}`);\n        }\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_schedules (user_id, scheduled_time, duration_minutes, job_id, batch_id)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, scheduledTime, durationMinutes, jobId, batchId);\n    }\n    checkScheduleConflicts(userId, scheduledTime, durationMinutes) {\n        const startTime = new Date(scheduledTime);\n        const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_schedules\n      WHERE user_id = ? \n      AND status IN ('scheduled', 'active')\n      AND (\n        (scheduled_time <= ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') > ?) OR\n        (scheduled_time < ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') >= ?)\n      )\n    `);\n        return stmt.all(userId, startTime.toISOString(), startTime.toISOString(), endTime.toISOString(), endTime.toISOString());\n    }\n    getUserSchedules(userId, startDate = null, endDate = null) {\n        let query = `\n      SELECT qs.*, qj.job_type, qb.batch_name\n      FROM queue_schedules qs\n      LEFT JOIN queue_jobs qj ON qs.job_id = qj.id\n      LEFT JOIN queue_batches qb ON qs.batch_id = qb.id\n      WHERE qs.user_id = ?\n    `;\n        const params = [\n            userId\n        ];\n        if (startDate) {\n            query += \" AND qs.scheduled_time >= ?\";\n            params.push(startDate);\n        }\n        if (endDate) {\n            query += \" AND qs.scheduled_time <= ?\";\n            params.push(endDate);\n        }\n        query += \" ORDER BY qs.scheduled_time ASC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Priority Management Methods\n    updateJobPriority(jobId, newPriority, adminOverride = false) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = ?, priority_level = ?\n      WHERE id = ?\n    `);\n        const result = stmt.run(newPriority, adminOverride ? newPriority : null, jobId);\n        if (adminOverride) {\n            this.logActivity(null, \"ADMIN_PRIORITY_OVERRIDE\", `Job ${jobId} priority set to ${newPriority}`);\n        }\n        return result;\n    }\n    applyStarvationPrevention() {\n        // Boost priority for jobs that have been waiting too long\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = CASE \n        WHEN datetime('now') > datetime(created_at, '+2 hours') THEN MIN(effective_priority + 2, 10)\n        WHEN datetime('now') > datetime(created_at, '+1 hour') THEN MIN(effective_priority + 1, 10)\n        ELSE effective_priority\n      END\n      WHERE status = 'queued'\n    `);\n        return stmt.run();\n    }\n    getQueueStats() {\n        const stats = {};\n        // Total jobs by status\n        const statusStmt = this.db.prepare(`\n      SELECT status, COUNT(*) as count \n      FROM queue_jobs \n      GROUP BY status\n    `);\n        stats.jobsByStatus = statusStmt.all();\n        // Jobs by priority level\n        const priorityStmt = this.db.prepare(`\n      SELECT effective_priority, COUNT(*) as count \n      FROM queue_jobs \n      WHERE status = 'queued'\n      GROUP BY effective_priority\n      ORDER BY effective_priority DESC\n    `);\n        stats.jobsByPriority = priorityStmt.all();\n        // Average wait time\n        const waitTimeStmt = this.db.prepare(`\n      SELECT AVG(julianday(started_at) - julianday(created_at)) * 24 * 60 as avg_wait_minutes\n      FROM queue_jobs \n      WHERE started_at IS NOT NULL\n    `);\n        stats.averageWaitTime = waitTimeStmt.get()?.avg_wait_minutes || 0;\n        return stats;\n    }\n    close() {\n        this.db.close();\n    }\n}\n// Create data directory if it doesn't exist\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst dataDir = path.join(process.cwd(), \"data\");\nif (!fs.existsSync(dataDir)) {\n    fs.mkdirSync(dataDir, {\n        recursive: true\n    });\n}\n// Export singleton instance\nlet dbInstance = null;\nfunction getDatabase() {\n    if (!dbInstance) {\n        dbInstance = new DatabaseManager();\n    }\n    return dbInstance;\n}\nmodule.exports = {\n    getDatabase,\n    DatabaseManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.js\n");

/***/ }),

/***/ "(rsc)/./lib/queueMiddleware.js":
/*!********************************!*\
  !*** ./lib/queueMiddleware.js ***!
  \********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst { getWebhookManager } = __webpack_require__(/*! ./webhook */ \"(rsc)/./lib/webhook.js\");\nclass QueueMiddleware {\n    static async validateLicenseFeatures(req, res, next) {\n        try {\n            const db = getDatabase();\n            const webhook = getWebhookManager();\n            const userId = req.user?.id;\n            if (!userId) {\n                return res.status(401).json({\n                    error: \"Authentication required\"\n                });\n            }\n            // Get user's license features\n            const features = db.getUserLicenseFeatures(userId);\n            req.licenseFeatures = features;\n            // Check specific feature requirements based on the endpoint\n            const endpoint = req.route?.path || req.path || req.url || \"\";\n            if (endpoint.includes(\"/batch\")) {\n                // Validate batch processing access\n                if (features.max_accounts_per_batch === 0) {\n                    await webhook.sendLicenseViolation(req.user.username, \"Batch Processing Access Denied\", \"User attempted to access batch processing without proper license\");\n                    return res.status(403).json({\n                        error: \"Batch processing not available for your license\",\n                        feature: \"max_accounts_per_batch\",\n                        current_limit: 0\n                    });\n                }\n            }\n            if (endpoint.includes(\"/schedule\")) {\n                // Validate scheduling access\n                if (!features.scheduling_access) {\n                    await webhook.sendLicenseViolation(req.user.username, \"Scheduling Access Denied\", \"User attempted to access scheduling without proper license\");\n                    return res.status(403).json({\n                        error: \"Scheduling not available for your license\",\n                        feature: \"scheduling_access\",\n                        current_access: false\n                    });\n                }\n            }\n            next();\n        } catch (error) {\n            console.error(\"License validation error:\", error);\n            res.status(500).json({\n                error: \"License validation failed\"\n            });\n        }\n    }\n    static async validateBatchSize(req, res, next) {\n        try {\n            const accounts = req.body.accounts || [];\n            const maxAccounts = req.licenseFeatures?.max_accounts_per_batch || 0;\n            if (maxAccounts > 0 && accounts.length > maxAccounts) {\n                const webhook = getWebhookManager();\n                await webhook.sendLicenseViolation(req.user.username, \"Batch Size Limit Exceeded\", `Attempted to submit ${accounts.length} accounts, limit is ${maxAccounts}`);\n                return res.status(403).json({\n                    error: \"Batch size exceeds license limit\",\n                    submitted_count: accounts.length,\n                    max_allowed: maxAccounts\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Batch size validation error:\", error);\n            res.status(500).json({\n                error: \"Batch size validation failed\"\n            });\n        }\n    }\n    static async validateDailyBatchLimit(req, res, next) {\n        try {\n            const db = getDatabase();\n            const userId = req.user.id;\n            const features = req.licenseFeatures;\n            // Check daily batch count\n            const dailyBatchCount = db.getUserDailyBatchCount(userId);\n            const maxBatchesPerDay = features.max_batches_per_day || 1;\n            if (dailyBatchCount >= maxBatchesPerDay) {\n                const webhook = getWebhookManager();\n                await webhook.sendLicenseViolation(req.user.username, \"Daily Batch Limit Exceeded\", `Attempted to create batch ${dailyBatchCount + 1}, daily limit is ${maxBatchesPerDay}`);\n                return res.status(403).json({\n                    error: \"Daily batch limit reached\",\n                    current_count: dailyBatchCount,\n                    max_allowed: maxBatchesPerDay,\n                    message: `You have reached your daily limit of ${maxBatchesPerDay} batch${maxBatchesPerDay > 1 ? \"es\" : \"\"}. Please try again tomorrow.`\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Daily batch limit validation error:\", error);\n            res.status(500).json({\n                error: \"Daily batch limit validation failed\"\n            });\n        }\n    }\n    static async validatePriorityLevel(req, res, next) {\n        try {\n            const requestedPriority = req.body.priority_level;\n            const maxPriority = req.licenseFeatures?.priority_level || 0;\n            if (requestedPriority !== undefined && requestedPriority > maxPriority) {\n                const webhook = getWebhookManager();\n                await webhook.sendLicenseViolation(req.user.username, \"Priority Level Exceeded\", `Attempted to set priority ${requestedPriority}, max allowed is ${maxPriority}`);\n                return res.status(403).json({\n                    error: \"Priority level exceeds license limit\",\n                    requested_priority: requestedPriority,\n                    max_allowed: maxPriority\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Priority validation error:\", error);\n            res.status(500).json({\n                error: \"Priority validation failed\"\n            });\n        }\n    }\n    static async checkScheduleConflicts(req, res, next) {\n        try {\n            const db = getDatabase();\n            const webhook = getWebhookManager();\n            const { scheduled_time, duration_minutes = 30 } = req.body;\n            const userId = req.user.id;\n            if (scheduled_time) {\n                const conflicts = db.checkScheduleConflicts(userId, scheduled_time, duration_minutes);\n                if (conflicts.length > 0) {\n                    await webhook.sendScheduleConflict(req.user.username, scheduled_time, `${conflicts.length} conflicting schedule(s) found`);\n                    return res.status(409).json({\n                        error: \"Schedule conflict detected\",\n                        requested_time: scheduled_time,\n                        conflicts: conflicts.map((c)=>({\n                                id: c.id,\n                                scheduled_time: c.scheduled_time,\n                                duration_minutes: c.duration_minutes\n                            }))\n                    });\n                }\n            }\n            next();\n        } catch (error) {\n            console.error(\"Schedule conflict check error:\", error);\n            res.status(500).json({\n                error: \"Schedule conflict check failed\"\n            });\n        }\n    }\n    static async logQueueActivity(req, res, next) {\n        try {\n            const db = getDatabase();\n            const originalSend = res.send;\n            res.send = function(data) {\n                // Log successful queue operations\n                if (res.statusCode >= 200 && res.statusCode < 300) {\n                    const action = req.method + \"_\" + req.route?.path?.replace(/[\\/\\:]/g, \"_\").toUpperCase();\n                    const details = {\n                        endpoint: req.originalUrl,\n                        method: req.method,\n                        status: res.statusCode\n                    };\n                    db.logActivity(req.user?.id, action, JSON.stringify(details));\n                }\n                originalSend.call(this, data);\n            };\n            next();\n        } catch (error) {\n            console.error(\"Activity logging error:\", error);\n            next(); // Don't block the request for logging errors\n        }\n    }\n    static gracefulDegradation(featureCheck) {\n        return (req, res, next)=>{\n            try {\n                const hasFeature = featureCheck(req.licenseFeatures);\n                if (!hasFeature) {\n                    // Instead of blocking, provide limited functionality\n                    req.degradedMode = true;\n                    req.degradationReason = \"License feature not available\";\n                }\n                next();\n            } catch (error) {\n                console.error(\"Graceful degradation error:\", error);\n                next();\n            }\n        };\n    }\n    static async rateLimitByLicense(req, res, next) {\n        try {\n            const db = getDatabase();\n            const userId = req.user.id;\n            const features = req.licenseFeatures;\n            // Implement rate limiting based on license features\n            const recentBatches = db.getQueueBatches(userId, null, 10, 0);\n            const recentBatchCount = recentBatches.filter((batch)=>{\n                const batchTime = new Date(batch.created_at);\n                const hourAgo = new Date(Date.now() - 60 * 60 * 1000);\n                return batchTime > hourAgo;\n            }).length;\n            // Basic rate limiting: higher priority licenses get more requests\n            const maxBatchesPerHour = Math.max(1, features.priority_level);\n            if (recentBatchCount >= maxBatchesPerHour) {\n                return res.status(429).json({\n                    error: \"Rate limit exceeded\",\n                    limit: maxBatchesPerHour,\n                    current: recentBatchCount,\n                    reset_time: new Date(Date.now() + 60 * 60 * 1000).toISOString()\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Rate limiting error:\", error);\n            next(); // Don't block for rate limiting errors\n        }\n    }\n}\nmodule.exports = QueueMiddleware;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/queueMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./lib/webhook.js":
/*!************************!*\
  !*** ./lib/webhook.js ***!
  \************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nclass WebhookManager {\n    constructor(){\n        this.discordWebhookUrl = \"https://discord.com/api/webhooks/1391133719351394314/VEY8LIMPErwXKx8ZGgsJvhLwbjfqHEtzhsbiZfwHb3aSUp9htUtWDy9mrW4N2LhuD6c9\";\n    }\n    async sendDiscordNotification(title, description, color = 0x3498db, fields = []) {\n        const embed = {\n            title,\n            description,\n            color,\n            timestamp: new Date().toISOString(),\n            fields,\n            footer: {\n                text: \"SparxReader Queue System\"\n            }\n        };\n        const payload = {\n            embeds: [\n                embed\n            ]\n        };\n        try {\n            await this.sendWebhook(this.discordWebhookUrl, payload);\n            console.log(\"Discord notification sent:\", title);\n        } catch (error) {\n            console.error(\"Failed to send Discord notification:\", error.message);\n        }\n    }\n    async sendLicenseViolation(username, violation, details) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDEAB License Violation Detected\", `User **${username}** attempted an unauthorized action`, 0xe74c3c, [\n            {\n                name: \"Violation Type\",\n                value: violation,\n                inline: true\n            },\n            {\n                name: \"Details\",\n                value: details,\n                inline: false\n            },\n            {\n                name: \"Timestamp\",\n                value: new Date().toLocaleString(),\n                inline: true\n            }\n        ]);\n    }\n    async sendScheduleConflict(username, scheduledTime, conflictDetails) {\n        await this.sendDiscordNotification(\"⚠️ Schedule Conflict Detected\", `Schedule conflict for user **${username}**`, 0xf39c12, [\n            {\n                name: \"Requested Time\",\n                value: new Date(scheduledTime).toLocaleString(),\n                inline: true\n            },\n            {\n                name: \"Conflict Details\",\n                value: conflictDetails,\n                inline: false\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            }\n        ]);\n    }\n    async sendPriorityAdjustment(jobId, oldPriority, newPriority, reason, adminUser = null) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDD04 Priority Adjustment\", `Job priority has been adjusted`, 0x9b59b6, [\n            {\n                name: \"Job ID\",\n                value: jobId.toString(),\n                inline: true\n            },\n            {\n                name: \"Old Priority\",\n                value: oldPriority.toString(),\n                inline: true\n            },\n            {\n                name: \"New Priority\",\n                value: newPriority.toString(),\n                inline: true\n            },\n            {\n                name: \"Reason\",\n                value: reason,\n                inline: false\n            },\n            ...adminUser ? [\n                {\n                    name: \"Admin User\",\n                    value: adminUser,\n                    inline: true\n                }\n            ] : []\n        ]);\n    }\n    async sendBatchCreated(username, batchName, accountCount, scheduledTime = null) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDCE6 New Batch Created\", `User **${username}** created a new batch`, 0x2ecc71, [\n            {\n                name: \"Batch Name\",\n                value: batchName,\n                inline: true\n            },\n            {\n                name: \"Account Count\",\n                value: accountCount.toString(),\n                inline: true\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            },\n            ...scheduledTime ? [\n                {\n                    name: \"Scheduled Time\",\n                    value: new Date(scheduledTime).toLocaleString(),\n                    inline: false\n                }\n            ] : []\n        ]);\n    }\n    async sendBatchCompleted(username, batchName, processedCount, failedCount, duration) {\n        await this.sendDiscordNotification(\"✅ Batch Completed\", `Batch processing completed for **${username}**`, 0x27ae60, [\n            {\n                name: \"Batch Name\",\n                value: batchName,\n                inline: true\n            },\n            {\n                name: \"Processed\",\n                value: processedCount.toString(),\n                inline: true\n            },\n            {\n                name: \"Failed\",\n                value: failedCount.toString(),\n                inline: true\n            },\n            {\n                name: \"Duration\",\n                value: `${Math.round(duration / 60)} minutes`,\n                inline: true\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            }\n        ]);\n    }\n    async sendQueueAlert(alertType, message, details = {}) {\n        const colors = {\n            \"high_load\": 0xe67e22,\n            \"system_error\": 0xe74c3c,\n            \"maintenance\": 0x3498db,\n            \"info\": 0x95a5a6 // Gray\n        };\n        const icons = {\n            \"high_load\": \"\\uD83D\\uDD25\",\n            \"system_error\": \"\\uD83D\\uDCA5\",\n            \"maintenance\": \"\\uD83D\\uDD27\",\n            \"info\": \"ℹ️\"\n        };\n        const fields = Object.entries(details).map(([key, value])=>({\n                name: key.replace(/_/g, \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                value: value.toString(),\n                inline: true\n            }));\n        await this.sendDiscordNotification(`${icons[alertType] || \"ℹ️\"} Queue System Alert`, message, colors[alertType] || 0x95a5a6, fields);\n    }\n    sendWebhook(webhookUrl, payload) {\n        return new Promise((resolve, reject)=>{\n            const url = new URL(webhookUrl);\n            const data = JSON.stringify(payload);\n            const options = {\n                hostname: url.hostname,\n                port: url.port || 443,\n                path: url.pathname + url.search,\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Content-Length\": Buffer.byteLength(data),\n                    \"User-Agent\": \"SparxReader-Queue-System/1.0\"\n                }\n            };\n            const req = https.request(options, (res)=>{\n                let responseData = \"\";\n                res.on(\"data\", (chunk)=>{\n                    responseData += chunk;\n                });\n                res.on(\"end\", ()=>{\n                    if (res.statusCode >= 200 && res.statusCode < 300) {\n                        resolve(responseData);\n                    } else {\n                        reject(new Error(`Webhook request failed with status ${res.statusCode}: ${responseData}`));\n                    }\n                });\n            });\n            req.on(\"error\", (error)=>{\n                reject(error);\n            });\n            req.on(\"timeout\", ()=>{\n                req.destroy();\n                reject(new Error(\"Webhook request timed out\"));\n            });\n            req.setTimeout(10000); // 10 second timeout\n            req.write(data);\n            req.end();\n        });\n    }\n}\n// Export singleton instance\nlet webhookInstance = null;\nfunction getWebhookManager() {\n    if (!webhookInstance) {\n        webhookInstance = new WebhookManager();\n    }\n    return webhookInstance;\n}\nmodule.exports = {\n    getWebhookManager,\n    WebhookManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvd2ViaG9vay5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsTUFBTUEsUUFBUUMsbUJBQU9BLENBQUM7QUFDdEIsTUFBTSxFQUFFQyxHQUFHLEVBQUUsR0FBR0QsbUJBQU9BLENBQUM7QUFFeEIsTUFBTUU7SUFDSkMsYUFBYztRQUNaLElBQUksQ0FBQ0MsaUJBQWlCLEdBQUc7SUFDM0I7SUFFQSxNQUFNQyx3QkFBd0JDLEtBQUssRUFBRUMsV0FBVyxFQUFFQyxRQUFRLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEVBQUU7UUFDL0UsTUFBTUMsUUFBUTtZQUNaSjtZQUNBQztZQUNBQztZQUNBRyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDakNKO1lBQ0FLLFFBQVE7Z0JBQ05DLE1BQU07WUFDUjtRQUNGO1FBRUEsTUFBTUMsVUFBVTtZQUNkQyxRQUFRO2dCQUFDUDthQUFNO1FBQ2pCO1FBRUEsSUFBSTtZQUNGLE1BQU0sSUFBSSxDQUFDUSxXQUFXLENBQUMsSUFBSSxDQUFDZCxpQkFBaUIsRUFBRVk7WUFDL0NHLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJkO1FBQzVDLEVBQUUsT0FBT2UsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsd0NBQXdDQSxNQUFNQyxPQUFPO1FBQ3JFO0lBQ0Y7SUFFQSxNQUFNQyxxQkFBcUJDLFFBQVEsRUFBRUMsU0FBUyxFQUFFQyxPQUFPLEVBQUU7UUFDdkQsTUFBTSxJQUFJLENBQUNyQix1QkFBdUIsQ0FDaEMsMkNBQ0EsQ0FBQyxPQUFPLEVBQUVtQixTQUFTLG1DQUFtQyxDQUFDLEVBQ3ZELFVBQ0E7WUFDRTtnQkFBRUcsTUFBTTtnQkFBa0JDLE9BQU9IO2dCQUFXSSxRQUFRO1lBQUs7WUFDekQ7Z0JBQUVGLE1BQU07Z0JBQVdDLE9BQU9GO2dCQUFTRyxRQUFRO1lBQU07WUFDakQ7Z0JBQUVGLE1BQU07Z0JBQWFDLE9BQU8sSUFBSWhCLE9BQU9rQixjQUFjO2dCQUFJRCxRQUFRO1lBQUs7U0FDdkU7SUFFTDtJQUVBLE1BQU1FLHFCQUFxQlAsUUFBUSxFQUFFUSxhQUFhLEVBQUVDLGVBQWUsRUFBRTtRQUNuRSxNQUFNLElBQUksQ0FBQzVCLHVCQUF1QixDQUNoQyxpQ0FDQSxDQUFDLDZCQUE2QixFQUFFbUIsU0FBUyxFQUFFLENBQUMsRUFDNUMsVUFDQTtZQUNFO2dCQUFFRyxNQUFNO2dCQUFrQkMsT0FBTyxJQUFJaEIsS0FBS29CLGVBQWVGLGNBQWM7Z0JBQUlELFFBQVE7WUFBSztZQUN4RjtnQkFBRUYsTUFBTTtnQkFBb0JDLE9BQU9LO2dCQUFpQkosUUFBUTtZQUFNO1lBQ2xFO2dCQUFFRixNQUFNO2dCQUFRQyxPQUFPSjtnQkFBVUssUUFBUTtZQUFLO1NBQy9DO0lBRUw7SUFFQSxNQUFNSyx1QkFBdUJDLEtBQUssRUFBRUMsV0FBVyxFQUFFQyxXQUFXLEVBQUVDLE1BQU0sRUFBRUMsWUFBWSxJQUFJLEVBQUU7UUFDdEYsTUFBTSxJQUFJLENBQUNsQyx1QkFBdUIsQ0FDaEMsb0NBQ0EsQ0FBQyw4QkFBOEIsQ0FBQyxFQUNoQyxVQUNBO1lBQ0U7Z0JBQUVzQixNQUFNO2dCQUFVQyxPQUFPTyxNQUFNSyxRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDeEQ7Z0JBQUVGLE1BQU07Z0JBQWdCQyxPQUFPUSxZQUFZSSxRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDcEU7Z0JBQUVGLE1BQU07Z0JBQWdCQyxPQUFPUyxZQUFZRyxRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDcEU7Z0JBQUVGLE1BQU07Z0JBQVVDLE9BQU9VO2dCQUFRVCxRQUFRO1lBQU07ZUFDM0NVLFlBQVk7Z0JBQUM7b0JBQUVaLE1BQU07b0JBQWNDLE9BQU9XO29CQUFXVixRQUFRO2dCQUFLO2FBQUUsR0FBRyxFQUFFO1NBQzlFO0lBRUw7SUFFQSxNQUFNWSxpQkFBaUJqQixRQUFRLEVBQUVrQixTQUFTLEVBQUVDLFlBQVksRUFBRVgsZ0JBQWdCLElBQUksRUFBRTtRQUM5RSxNQUFNLElBQUksQ0FBQzNCLHVCQUF1QixDQUNoQyxrQ0FDQSxDQUFDLE9BQU8sRUFBRW1CLFNBQVMsc0JBQXNCLENBQUMsRUFDMUMsVUFDQTtZQUNFO2dCQUFFRyxNQUFNO2dCQUFjQyxPQUFPYztnQkFBV2IsUUFBUTtZQUFLO1lBQ3JEO2dCQUFFRixNQUFNO2dCQUFpQkMsT0FBT2UsYUFBYUgsUUFBUTtnQkFBSVgsUUFBUTtZQUFLO1lBQ3RFO2dCQUFFRixNQUFNO2dCQUFRQyxPQUFPSjtnQkFBVUssUUFBUTtZQUFLO2VBQzFDRyxnQkFBZ0I7Z0JBQUM7b0JBQUVMLE1BQU07b0JBQWtCQyxPQUFPLElBQUloQixLQUFLb0IsZUFBZUYsY0FBYztvQkFBSUQsUUFBUTtnQkFBTTthQUFFLEdBQUcsRUFBRTtTQUN0SDtJQUVMO0lBRUEsTUFBTWUsbUJBQW1CcEIsUUFBUSxFQUFFa0IsU0FBUyxFQUFFRyxjQUFjLEVBQUVDLFdBQVcsRUFBRUMsUUFBUSxFQUFFO1FBQ25GLE1BQU0sSUFBSSxDQUFDMUMsdUJBQXVCLENBQ2hDLHFCQUNBLENBQUMsaUNBQWlDLEVBQUVtQixTQUFTLEVBQUUsQ0FBQyxFQUNoRCxVQUNBO1lBQ0U7Z0JBQUVHLE1BQU07Z0JBQWNDLE9BQU9jO2dCQUFXYixRQUFRO1lBQUs7WUFDckQ7Z0JBQUVGLE1BQU07Z0JBQWFDLE9BQU9pQixlQUFlTCxRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDcEU7Z0JBQUVGLE1BQU07Z0JBQVVDLE9BQU9rQixZQUFZTixRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDOUQ7Z0JBQUVGLE1BQU07Z0JBQVlDLE9BQU8sQ0FBQyxFQUFFb0IsS0FBS0MsS0FBSyxDQUFDRixXQUFXLElBQUksUUFBUSxDQUFDO2dCQUFFbEIsUUFBUTtZQUFLO1lBQ2hGO2dCQUFFRixNQUFNO2dCQUFRQyxPQUFPSjtnQkFBVUssUUFBUTtZQUFLO1NBQy9DO0lBRUw7SUFFQSxNQUFNcUIsZUFBZUMsU0FBUyxFQUFFN0IsT0FBTyxFQUFFSSxVQUFVLENBQUMsQ0FBQyxFQUFFO1FBQ3JELE1BQU0wQixTQUFTO1lBQ2IsYUFBYTtZQUNiLGdCQUFnQjtZQUNoQixlQUFlO1lBQ2YsUUFBUSxTQUFTLE9BQU87UUFDMUI7UUFFQSxNQUFNQyxRQUFRO1lBQ1osYUFBYTtZQUNiLGdCQUFnQjtZQUNoQixlQUFlO1lBQ2YsUUFBUTtRQUNWO1FBRUEsTUFBTTVDLFNBQVM2QyxPQUFPQyxPQUFPLENBQUM3QixTQUFTOEIsR0FBRyxDQUFDLENBQUMsQ0FBQ0MsS0FBSzdCLE1BQU0sR0FBTTtnQkFDNURELE1BQU04QixJQUFJQyxPQUFPLENBQUMsTUFBTSxLQUFLQSxPQUFPLENBQUMsU0FBU0MsQ0FBQUEsSUFBS0EsRUFBRUMsV0FBVztnQkFDaEVoQyxPQUFPQSxNQUFNWSxRQUFRO2dCQUNyQlgsUUFBUTtZQUNWO1FBRUEsTUFBTSxJQUFJLENBQUN4Qix1QkFBdUIsQ0FDaEMsQ0FBQyxFQUFFZ0QsS0FBSyxDQUFDRixVQUFVLElBQUksS0FBSyxtQkFBbUIsQ0FBQyxFQUNoRDdCLFNBQ0E4QixNQUFNLENBQUNELFVBQVUsSUFBSSxVQUNyQjFDO0lBRUo7SUFFQVMsWUFBWTJDLFVBQVUsRUFBRTdDLE9BQU8sRUFBRTtRQUMvQixPQUFPLElBQUk4QyxRQUFRLENBQUNDLFNBQVNDO1lBQzNCLE1BQU1DLE1BQU0sSUFBSWhFLElBQUk0RDtZQUNwQixNQUFNSyxPQUFPQyxLQUFLQyxTQUFTLENBQUNwRDtZQUU1QixNQUFNcUQsVUFBVTtnQkFDZEMsVUFBVUwsSUFBSUssUUFBUTtnQkFDdEJDLE1BQU1OLElBQUlNLElBQUksSUFBSTtnQkFDbEJDLE1BQU1QLElBQUlRLFFBQVEsR0FBR1IsSUFBSVMsTUFBTTtnQkFDL0JDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixrQkFBa0JDLE9BQU9DLFVBQVUsQ0FBQ1o7b0JBQ3BDLGNBQWM7Z0JBQ2hCO1lBQ0Y7WUFFQSxNQUFNYSxNQUFNaEYsTUFBTWlGLE9BQU8sQ0FBQ1gsU0FBUyxDQUFDWTtnQkFDbEMsSUFBSUMsZUFBZTtnQkFFbkJELElBQUlFLEVBQUUsQ0FBQyxRQUFRLENBQUNDO29CQUNkRixnQkFBZ0JFO2dCQUNsQjtnQkFFQUgsSUFBSUUsRUFBRSxDQUFDLE9BQU87b0JBQ1osSUFBSUYsSUFBSUksVUFBVSxJQUFJLE9BQU9KLElBQUlJLFVBQVUsR0FBRyxLQUFLO3dCQUNqRHRCLFFBQVFtQjtvQkFDVixPQUFPO3dCQUNMbEIsT0FBTyxJQUFJc0IsTUFBTSxDQUFDLG1DQUFtQyxFQUFFTCxJQUFJSSxVQUFVLENBQUMsRUFBRSxFQUFFSCxhQUFhLENBQUM7b0JBQzFGO2dCQUNGO1lBQ0Y7WUFFQUgsSUFBSUksRUFBRSxDQUFDLFNBQVMsQ0FBQzlEO2dCQUNmMkMsT0FBTzNDO1lBQ1Q7WUFFQTBELElBQUlJLEVBQUUsQ0FBQyxXQUFXO2dCQUNoQkosSUFBSVEsT0FBTztnQkFDWHZCLE9BQU8sSUFBSXNCLE1BQU07WUFDbkI7WUFFQVAsSUFBSVMsVUFBVSxDQUFDLFFBQVEsb0JBQW9CO1lBQzNDVCxJQUFJVSxLQUFLLENBQUN2QjtZQUNWYSxJQUFJVyxHQUFHO1FBQ1Q7SUFDRjtBQUNGO0FBRUEsNEJBQTRCO0FBQzVCLElBQUlDLGtCQUFrQjtBQUV0QixTQUFTQztJQUNQLElBQUksQ0FBQ0QsaUJBQWlCO1FBQ3BCQSxrQkFBa0IsSUFBSXpGO0lBQ3hCO0lBQ0EsT0FBT3lGO0FBQ1Q7QUFFQUUsT0FBT0MsT0FBTyxHQUFHO0lBQUVGO0lBQW1CMUY7QUFBZSIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vbGliL3dlYmhvb2suanM/ODQwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBodHRwcyA9IHJlcXVpcmUoJ2h0dHBzJyk7XHJcbmNvbnN0IHsgVVJMIH0gPSByZXF1aXJlKCd1cmwnKTtcclxuXHJcbmNsYXNzIFdlYmhvb2tNYW5hZ2VyIHtcclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHRoaXMuZGlzY29yZFdlYmhvb2tVcmwgPSAnaHR0cHM6Ly9kaXNjb3JkLmNvbS9hcGkvd2ViaG9va3MvMTM5MTEzMzcxOTM1MTM5NDMxNC9WRVk4TElNUEVyd1hLeDhaR2dzSnZoTHdiamZxSEV0emhzYmlaZndIYjNhU1VwOWh0VXRXRHk5bXJXNE4yTGh1RDZjOSc7XHJcbiAgfVxyXG5cclxuICBhc3luYyBzZW5kRGlzY29yZE5vdGlmaWNhdGlvbih0aXRsZSwgZGVzY3JpcHRpb24sIGNvbG9yID0gMHgzNDk4ZGIsIGZpZWxkcyA9IFtdKSB7XHJcbiAgICBjb25zdCBlbWJlZCA9IHtcclxuICAgICAgdGl0bGUsXHJcbiAgICAgIGRlc2NyaXB0aW9uLFxyXG4gICAgICBjb2xvcixcclxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIGZpZWxkcyxcclxuICAgICAgZm9vdGVyOiB7XHJcbiAgICAgICAgdGV4dDogJ1NwYXJ4UmVhZGVyIFF1ZXVlIFN5c3RlbSdcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBwYXlsb2FkID0ge1xyXG4gICAgICBlbWJlZHM6IFtlbWJlZF1cclxuICAgIH07XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgdGhpcy5zZW5kV2ViaG9vayh0aGlzLmRpc2NvcmRXZWJob29rVXJsLCBwYXlsb2FkKTtcclxuICAgICAgY29uc29sZS5sb2coJ0Rpc2NvcmQgbm90aWZpY2F0aW9uIHNlbnQ6JywgdGl0bGUpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNlbmQgRGlzY29yZCBub3RpZmljYXRpb246JywgZXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBzZW5kTGljZW5zZVZpb2xhdGlvbih1c2VybmFtZSwgdmlvbGF0aW9uLCBkZXRhaWxzKSB7XHJcbiAgICBhd2FpdCB0aGlzLnNlbmREaXNjb3JkTm90aWZpY2F0aW9uKFxyXG4gICAgICAn8J+aqyBMaWNlbnNlIFZpb2xhdGlvbiBEZXRlY3RlZCcsXHJcbiAgICAgIGBVc2VyICoqJHt1c2VybmFtZX0qKiBhdHRlbXB0ZWQgYW4gdW5hdXRob3JpemVkIGFjdGlvbmAsXHJcbiAgICAgIDB4ZTc0YzNjLCAvLyBSZWQgY29sb3JcclxuICAgICAgW1xyXG4gICAgICAgIHsgbmFtZTogJ1Zpb2xhdGlvbiBUeXBlJywgdmFsdWU6IHZpb2xhdGlvbiwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnRGV0YWlscycsIHZhbHVlOiBkZXRhaWxzLCBpbmxpbmU6IGZhbHNlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnVGltZXN0YW1wJywgdmFsdWU6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKSwgaW5saW5lOiB0cnVlIH1cclxuICAgICAgXVxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIGFzeW5jIHNlbmRTY2hlZHVsZUNvbmZsaWN0KHVzZXJuYW1lLCBzY2hlZHVsZWRUaW1lLCBjb25mbGljdERldGFpbHMpIHtcclxuICAgIGF3YWl0IHRoaXMuc2VuZERpc2NvcmROb3RpZmljYXRpb24oXHJcbiAgICAgICfimqDvuI8gU2NoZWR1bGUgQ29uZmxpY3QgRGV0ZWN0ZWQnLFxyXG4gICAgICBgU2NoZWR1bGUgY29uZmxpY3QgZm9yIHVzZXIgKioke3VzZXJuYW1lfSoqYCxcclxuICAgICAgMHhmMzljMTIsIC8vIE9yYW5nZSBjb2xvclxyXG4gICAgICBbXHJcbiAgICAgICAgeyBuYW1lOiAnUmVxdWVzdGVkIFRpbWUnLCB2YWx1ZTogbmV3IERhdGUoc2NoZWR1bGVkVGltZSkudG9Mb2NhbGVTdHJpbmcoKSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnQ29uZmxpY3QgRGV0YWlscycsIHZhbHVlOiBjb25mbGljdERldGFpbHMsIGlubGluZTogZmFsc2UgfSxcclxuICAgICAgICB7IG5hbWU6ICdVc2VyJywgdmFsdWU6IHVzZXJuYW1lLCBpbmxpbmU6IHRydWUgfVxyXG4gICAgICBdXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgc2VuZFByaW9yaXR5QWRqdXN0bWVudChqb2JJZCwgb2xkUHJpb3JpdHksIG5ld1ByaW9yaXR5LCByZWFzb24sIGFkbWluVXNlciA9IG51bGwpIHtcclxuICAgIGF3YWl0IHRoaXMuc2VuZERpc2NvcmROb3RpZmljYXRpb24oXHJcbiAgICAgICfwn5SEIFByaW9yaXR5IEFkanVzdG1lbnQnLFxyXG4gICAgICBgSm9iIHByaW9yaXR5IGhhcyBiZWVuIGFkanVzdGVkYCxcclxuICAgICAgMHg5YjU5YjYsIC8vIFB1cnBsZSBjb2xvclxyXG4gICAgICBbXHJcbiAgICAgICAgeyBuYW1lOiAnSm9iIElEJywgdmFsdWU6IGpvYklkLnRvU3RyaW5nKCksIGlubGluZTogdHJ1ZSB9LFxyXG4gICAgICAgIHsgbmFtZTogJ09sZCBQcmlvcml0eScsIHZhbHVlOiBvbGRQcmlvcml0eS50b1N0cmluZygpLCBpbmxpbmU6IHRydWUgfSxcclxuICAgICAgICB7IG5hbWU6ICdOZXcgUHJpb3JpdHknLCB2YWx1ZTogbmV3UHJpb3JpdHkudG9TdHJpbmcoKSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnUmVhc29uJywgdmFsdWU6IHJlYXNvbiwgaW5saW5lOiBmYWxzZSB9LFxyXG4gICAgICAgIC4uLihhZG1pblVzZXIgPyBbeyBuYW1lOiAnQWRtaW4gVXNlcicsIHZhbHVlOiBhZG1pblVzZXIsIGlubGluZTogdHJ1ZSB9XSA6IFtdKVxyXG4gICAgICBdXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgc2VuZEJhdGNoQ3JlYXRlZCh1c2VybmFtZSwgYmF0Y2hOYW1lLCBhY2NvdW50Q291bnQsIHNjaGVkdWxlZFRpbWUgPSBudWxsKSB7XHJcbiAgICBhd2FpdCB0aGlzLnNlbmREaXNjb3JkTm90aWZpY2F0aW9uKFxyXG4gICAgICAn8J+TpiBOZXcgQmF0Y2ggQ3JlYXRlZCcsXHJcbiAgICAgIGBVc2VyICoqJHt1c2VybmFtZX0qKiBjcmVhdGVkIGEgbmV3IGJhdGNoYCxcclxuICAgICAgMHgyZWNjNzEsIC8vIEdyZWVuIGNvbG9yXHJcbiAgICAgIFtcclxuICAgICAgICB7IG5hbWU6ICdCYXRjaCBOYW1lJywgdmFsdWU6IGJhdGNoTmFtZSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnQWNjb3VudCBDb3VudCcsIHZhbHVlOiBhY2NvdW50Q291bnQudG9TdHJpbmcoKSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnVXNlcicsIHZhbHVlOiB1c2VybmFtZSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgLi4uKHNjaGVkdWxlZFRpbWUgPyBbeyBuYW1lOiAnU2NoZWR1bGVkIFRpbWUnLCB2YWx1ZTogbmV3IERhdGUoc2NoZWR1bGVkVGltZSkudG9Mb2NhbGVTdHJpbmcoKSwgaW5saW5lOiBmYWxzZSB9XSA6IFtdKVxyXG4gICAgICBdXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgc2VuZEJhdGNoQ29tcGxldGVkKHVzZXJuYW1lLCBiYXRjaE5hbWUsIHByb2Nlc3NlZENvdW50LCBmYWlsZWRDb3VudCwgZHVyYXRpb24pIHtcclxuICAgIGF3YWl0IHRoaXMuc2VuZERpc2NvcmROb3RpZmljYXRpb24oXHJcbiAgICAgICfinIUgQmF0Y2ggQ29tcGxldGVkJyxcclxuICAgICAgYEJhdGNoIHByb2Nlc3NpbmcgY29tcGxldGVkIGZvciAqKiR7dXNlcm5hbWV9KipgLFxyXG4gICAgICAweDI3YWU2MCwgLy8gRGFyayBncmVlbiBjb2xvclxyXG4gICAgICBbXHJcbiAgICAgICAgeyBuYW1lOiAnQmF0Y2ggTmFtZScsIHZhbHVlOiBiYXRjaE5hbWUsIGlubGluZTogdHJ1ZSB9LFxyXG4gICAgICAgIHsgbmFtZTogJ1Byb2Nlc3NlZCcsIHZhbHVlOiBwcm9jZXNzZWRDb3VudC50b1N0cmluZygpLCBpbmxpbmU6IHRydWUgfSxcclxuICAgICAgICB7IG5hbWU6ICdGYWlsZWQnLCB2YWx1ZTogZmFpbGVkQ291bnQudG9TdHJpbmcoKSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnRHVyYXRpb24nLCB2YWx1ZTogYCR7TWF0aC5yb3VuZChkdXJhdGlvbiAvIDYwKX0gbWludXRlc2AsIGlubGluZTogdHJ1ZSB9LFxyXG4gICAgICAgIHsgbmFtZTogJ1VzZXInLCB2YWx1ZTogdXNlcm5hbWUsIGlubGluZTogdHJ1ZSB9XHJcbiAgICAgIF1cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBhc3luYyBzZW5kUXVldWVBbGVydChhbGVydFR5cGUsIG1lc3NhZ2UsIGRldGFpbHMgPSB7fSkge1xyXG4gICAgY29uc3QgY29sb3JzID0ge1xyXG4gICAgICAnaGlnaF9sb2FkJzogMHhlNjdlMjIsIC8vIE9yYW5nZVxyXG4gICAgICAnc3lzdGVtX2Vycm9yJzogMHhlNzRjM2MsIC8vIFJlZFxyXG4gICAgICAnbWFpbnRlbmFuY2UnOiAweDM0OThkYiwgLy8gQmx1ZVxyXG4gICAgICAnaW5mbyc6IDB4OTVhNWE2IC8vIEdyYXlcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgaWNvbnMgPSB7XHJcbiAgICAgICdoaWdoX2xvYWQnOiAn8J+UpScsXHJcbiAgICAgICdzeXN0ZW1fZXJyb3InOiAn8J+SpScsXHJcbiAgICAgICdtYWludGVuYW5jZSc6ICfwn5SnJyxcclxuICAgICAgJ2luZm8nOiAn4oS577iPJ1xyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBmaWVsZHMgPSBPYmplY3QuZW50cmllcyhkZXRhaWxzKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHtcclxuICAgICAgbmFtZToga2V5LnJlcGxhY2UoL18vZywgJyAnKS5yZXBsYWNlKC9cXGJcXHcvZywgbCA9PiBsLnRvVXBwZXJDYXNlKCkpLFxyXG4gICAgICB2YWx1ZTogdmFsdWUudG9TdHJpbmcoKSxcclxuICAgICAgaW5saW5lOiB0cnVlXHJcbiAgICB9KSk7XHJcblxyXG4gICAgYXdhaXQgdGhpcy5zZW5kRGlzY29yZE5vdGlmaWNhdGlvbihcclxuICAgICAgYCR7aWNvbnNbYWxlcnRUeXBlXSB8fCAn4oS577iPJ30gUXVldWUgU3lzdGVtIEFsZXJ0YCxcclxuICAgICAgbWVzc2FnZSxcclxuICAgICAgY29sb3JzW2FsZXJ0VHlwZV0gfHwgMHg5NWE1YTYsXHJcbiAgICAgIGZpZWxkc1xyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHNlbmRXZWJob29rKHdlYmhvb2tVcmwsIHBheWxvYWQpIHtcclxuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XHJcbiAgICAgIGNvbnN0IHVybCA9IG5ldyBVUkwod2ViaG9va1VybCk7XHJcbiAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnN0cmluZ2lmeShwYXlsb2FkKTtcclxuXHJcbiAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XHJcbiAgICAgICAgaG9zdG5hbWU6IHVybC5ob3N0bmFtZSxcclxuICAgICAgICBwb3J0OiB1cmwucG9ydCB8fCA0NDMsXHJcbiAgICAgICAgcGF0aDogdXJsLnBhdGhuYW1lICsgdXJsLnNlYXJjaCxcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgJ0NvbnRlbnQtTGVuZ3RoJzogQnVmZmVyLmJ5dGVMZW5ndGgoZGF0YSksXHJcbiAgICAgICAgICAnVXNlci1BZ2VudCc6ICdTcGFyeFJlYWRlci1RdWV1ZS1TeXN0ZW0vMS4wJ1xyXG4gICAgICAgIH1cclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IHJlcSA9IGh0dHBzLnJlcXVlc3Qob3B0aW9ucywgKHJlcykgPT4ge1xyXG4gICAgICAgIGxldCByZXNwb25zZURhdGEgPSAnJztcclxuICAgICAgICBcclxuICAgICAgICByZXMub24oJ2RhdGEnLCAoY2h1bmspID0+IHtcclxuICAgICAgICAgIHJlc3BvbnNlRGF0YSArPSBjaHVuaztcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgcmVzLm9uKCdlbmQnLCAoKSA9PiB7XHJcbiAgICAgICAgICBpZiAocmVzLnN0YXR1c0NvZGUgPj0gMjAwICYmIHJlcy5zdGF0dXNDb2RlIDwgMzAwKSB7XHJcbiAgICAgICAgICAgIHJlc29sdmUocmVzcG9uc2VEYXRhKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHJlamVjdChuZXcgRXJyb3IoYFdlYmhvb2sgcmVxdWVzdCBmYWlsZWQgd2l0aCBzdGF0dXMgJHtyZXMuc3RhdHVzQ29kZX06ICR7cmVzcG9uc2VEYXRhfWApKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICByZXEub24oJ2Vycm9yJywgKGVycm9yKSA9PiB7XHJcbiAgICAgICAgcmVqZWN0KGVycm9yKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICByZXEub24oJ3RpbWVvdXQnLCAoKSA9PiB7XHJcbiAgICAgICAgcmVxLmRlc3Ryb3koKTtcclxuICAgICAgICByZWplY3QobmV3IEVycm9yKCdXZWJob29rIHJlcXVlc3QgdGltZWQgb3V0JykpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHJlcS5zZXRUaW1lb3V0KDEwMDAwKTsgLy8gMTAgc2Vjb25kIHRpbWVvdXRcclxuICAgICAgcmVxLndyaXRlKGRhdGEpO1xyXG4gICAgICByZXEuZW5kKCk7XHJcbiAgICB9KTtcclxuICB9XHJcbn1cclxuXHJcbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcclxubGV0IHdlYmhvb2tJbnN0YW5jZSA9IG51bGw7XHJcblxyXG5mdW5jdGlvbiBnZXRXZWJob29rTWFuYWdlcigpIHtcclxuICBpZiAoIXdlYmhvb2tJbnN0YW5jZSkge1xyXG4gICAgd2ViaG9va0luc3RhbmNlID0gbmV3IFdlYmhvb2tNYW5hZ2VyKCk7XHJcbiAgfVxyXG4gIHJldHVybiB3ZWJob29rSW5zdGFuY2U7XHJcbn1cclxuXHJcbm1vZHVsZS5leHBvcnRzID0geyBnZXRXZWJob29rTWFuYWdlciwgV2ViaG9va01hbmFnZXIgfTsiXSwibmFtZXMiOlsiaHR0cHMiLCJyZXF1aXJlIiwiVVJMIiwiV2ViaG9va01hbmFnZXIiLCJjb25zdHJ1Y3RvciIsImRpc2NvcmRXZWJob29rVXJsIiwic2VuZERpc2NvcmROb3RpZmljYXRpb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiY29sb3IiLCJmaWVsZHMiLCJlbWJlZCIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImZvb3RlciIsInRleHQiLCJwYXlsb2FkIiwiZW1iZWRzIiwic2VuZFdlYmhvb2siLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJtZXNzYWdlIiwic2VuZExpY2Vuc2VWaW9sYXRpb24iLCJ1c2VybmFtZSIsInZpb2xhdGlvbiIsImRldGFpbHMiLCJuYW1lIiwidmFsdWUiLCJpbmxpbmUiLCJ0b0xvY2FsZVN0cmluZyIsInNlbmRTY2hlZHVsZUNvbmZsaWN0Iiwic2NoZWR1bGVkVGltZSIsImNvbmZsaWN0RGV0YWlscyIsInNlbmRQcmlvcml0eUFkanVzdG1lbnQiLCJqb2JJZCIsIm9sZFByaW9yaXR5IiwibmV3UHJpb3JpdHkiLCJyZWFzb24iLCJhZG1pblVzZXIiLCJ0b1N0cmluZyIsInNlbmRCYXRjaENyZWF0ZWQiLCJiYXRjaE5hbWUiLCJhY2NvdW50Q291bnQiLCJzZW5kQmF0Y2hDb21wbGV0ZWQiLCJwcm9jZXNzZWRDb3VudCIsImZhaWxlZENvdW50IiwiZHVyYXRpb24iLCJNYXRoIiwicm91bmQiLCJzZW5kUXVldWVBbGVydCIsImFsZXJ0VHlwZSIsImNvbG9ycyIsImljb25zIiwiT2JqZWN0IiwiZW50cmllcyIsIm1hcCIsImtleSIsInJlcGxhY2UiLCJsIiwidG9VcHBlckNhc2UiLCJ3ZWJob29rVXJsIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJ1cmwiLCJkYXRhIiwiSlNPTiIsInN0cmluZ2lmeSIsIm9wdGlvbnMiLCJob3N0bmFtZSIsInBvcnQiLCJwYXRoIiwicGF0aG5hbWUiLCJzZWFyY2giLCJtZXRob2QiLCJoZWFkZXJzIiwiQnVmZmVyIiwiYnl0ZUxlbmd0aCIsInJlcSIsInJlcXVlc3QiLCJyZXMiLCJyZXNwb25zZURhdGEiLCJvbiIsImNodW5rIiwic3RhdHVzQ29kZSIsIkVycm9yIiwiZGVzdHJveSIsInNldFRpbWVvdXQiLCJ3cml0ZSIsImVuZCIsIndlYmhvb2tJbnN0YW5jZSIsImdldFdlYmhvb2tNYW5hZ2VyIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/webhook.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs","vendor-chunks/uuid","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fstatus%2Froute&page=%2Fapi%2Fqueue%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fstatus%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();