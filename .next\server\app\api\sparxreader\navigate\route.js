"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sparxreader/navigate/route";
exports.ids = ["app/api/sparxreader/navigate/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fnavigate%2Froute&page=%2Fapi%2Fsparxreader%2Fnavigate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fnavigate%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fnavigate%2Froute&page=%2Fapi%2Fsparxreader%2Fnavigate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fnavigate%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_sparxreader_navigate_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/sparxreader/navigate/route.js */ \"(rsc)/./app/api/sparxreader/navigate/route.js\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sparxreader/navigate/route\",\n        pathname: \"/api/sparxreader/navigate\",\n        filename: \"route\",\n        bundlePath: \"app/api/sparxreader/navigate/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\sparxreader\\\\navigate\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_sparxreader_navigate_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/sparxreader/navigate/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fnavigate%2Froute&page=%2Fapi%2Fsparxreader%2Fnavigate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fnavigate%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/browser-context.js":
/*!************************************************!*\
  !*** ./app/api/sparxreader/browser-context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearGlobalBrowser: () => (/* binding */ clearGlobalBrowser),\n/* harmony export */   getGlobalBrowser: () => (/* binding */ getGlobalBrowser),\n/* harmony export */   getGlobalPage: () => (/* binding */ getGlobalPage),\n/* harmony export */   setGlobalBrowser: () => (/* binding */ setGlobalBrowser)\n/* harmony export */ });\n// Global browser context storage using globalThis for persistence\nif (!globalThis.sparxBrowserContext) {\n    globalThis.sparxBrowserContext = {\n        browser: null,\n        page: null\n    };\n}\nfunction setGlobalBrowser(browser, page) {\n    globalThis.sparxBrowserContext.browser = browser;\n    globalThis.sparxBrowserContext.page = page;\n    console.log(\"Browser context set:\", !!browser, !!page);\n}\nfunction getGlobalBrowser() {\n    return globalThis.sparxBrowserContext.browser;\n}\nfunction getGlobalPage() {\n    return globalThis.sparxBrowserContext.page;\n}\nfunction clearGlobalBrowser() {\n    globalThis.sparxBrowserContext.browser = null;\n    globalThis.sparxBrowserContext.page = null;\n    console.log(\"Browser context cleared\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/browser-context.js\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/navigate/route.js":
/*!***********************************************!*\
  !*** ./app/api/sparxreader/navigate/route.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _browser_context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../browser-context.js */ \"(rsc)/./app/api/sparxreader/browser-context.js\");\n/* harmony import */ var _realtime_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../realtime/route.js */ \"(rsc)/./app/api/sparxreader/realtime/route.js\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const { action, bookTitle, targetSrp } = await request.json();\n        if (action === \"confirm\") {\n            const page = (0,_browser_context_js__WEBPACK_IMPORTED_MODULE_2__.getGlobalPage)();\n            const browser = (0,_browser_context_js__WEBPACK_IMPORTED_MODULE_2__.getGlobalBrowser)();\n            console.log(\"Navigate route - checking browser session:\");\n            console.log(\"Page exists:\", !!page);\n            console.log(\"Browser exists:\", !!browser);\n            if (!page) {\n                console.log(\"No page found in global context\");\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"No browser session available. Please restart the application.\"\n                }, {\n                    status: 400\n                });\n            }\n            if (!browser) {\n                console.log(\"No browser found in global context\");\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"No browser session available. Please restart the application.\"\n                }, {\n                    status: 400\n                });\n            }\n            console.log(\"Browser session found, proceeding...\");\n            console.log(\"User confirmed book, extracting story content from current page...\");\n            // Extract the actual book title from the book page\n            let actualBookTitle = bookTitle; // fallback to passed title\n            try {\n                const bookTitleElement = await page.waitForSelector(\"h2.sr_942936b5.sr_b59a8fb2\", {\n                    timeout: 5000\n                });\n                if (bookTitleElement) {\n                    actualBookTitle = await bookTitleElement.textContent();\n                    actualBookTitle = actualBookTitle.trim();\n                    console.log(`Extracted actual book title: ${actualBookTitle}`);\n                }\n            } catch (error) {\n                console.log(\"Could not extract book title from book page, using fallback\");\n            }\n            //  just extract the content directly since its the second go around\n            // Wait a moment to ensure page is fully loaded\n            await page.waitForTimeout(1000);\n            // Extract the story content from the current page,\n            const storyContent = await page.evaluate(()=>{\n                const fullText = document.body.innerText;\n                // Find the start and end markers\n                const startMarker = \"Start reading here\";\n                const endMarker = \"Stop reading here\";\n                const startIndex = fullText.indexOf(startMarker);\n                const endIndex = fullText.indexOf(endMarker);\n                if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {\n                    // Extract content between the markers (excluding the markers themselves)\n                    return fullText.substring(startIndex + startMarker.length, endIndex).trim();\n                } else if (startIndex !== -1) {\n                    // If only start marker found, extract from start marker to end\n                    return fullText.substring(startIndex + startMarker.length).trim();\n                } else {\n                    // If no markers found, return full text as fallback\n                    return fullText;\n                }\n            });\n            // Log some of the story content for debugging\n            const storyPreview = storyContent.substring(0, 500);\n            console.log(\"Story content extracted (first 500 characters):\");\n            console.log(storyPreview);\n            console.log(`Total story length: ${storyContent.length} characters`);\n            // Store the story content in the session for use with AI\n            global.sessionStoryContent = storyContent;\n            console.log(\"Story content stored for AI context\");\n            // Store target SRP in session info and browser localStorage\n            if (targetSrp) {\n                global.sessionSrpInfo = global.sessionSrpInfo || {};\n                global.sessionSrpInfo.targetSrpNeeded = targetSrp;\n                console.log(`Target SRP set to: ${targetSrp}`);\n                // Store target SRP in browser localStorage so extension can access it\n                await page.evaluate((target)=>{\n                    localStorage.setItem(\"targetSrp\", target.toString());\n                }, targetSrp);\n            }\n            // Get initial SRP count before starting questions\n            const initialSrpInfo = global.sessionSrpInfo || {};\n            const initialUserTotalSrp = await page.evaluate(()=>{\n                const userTotalSrpElement = document.querySelector(\".sr_92b39de6\");\n                return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\\d,]/g, \"\").replace(\",\", \"\") : null;\n            }).catch(()=>null);\n            console.log(`Initial User Total SRP: ${initialUserTotalSrp}`);\n            // Now click \"I have read up to here\" button\n            try {\n                console.log('Looking for \"I have read up to here\" button...');\n                // Try multiple selectors for the \"I have read up to here\" button\n                const readUpToHereSelectors = [\n                    'xpath=//*[@id=\"book-scroll\"]/div/div/div/button[2]',\n                    \"#book-scroll div:nth-child(2) div:nth-child(3) div div div button\",\n                    'button:has-text(\"I have read up to here\")',\n                    'button:has-text(\"read up to here\")',\n                    '[data-test-id*=\"read-up-to-here\"]',\n                    'button[class*=\"read\"]'\n                ];\n                let readUpToHereClicked = false;\n                for (const selector of readUpToHereSelectors){\n                    try {\n                        await page.click(selector, {\n                            timeout: 3000\n                        });\n                        console.log(`Clicked \"I have read up to here\" button with selector: ${selector}`);\n                        readUpToHereClicked = true;\n                        break;\n                    } catch (error) {\n                    // Continue to next selector\n                    }\n                }\n                if (readUpToHereClicked) {\n                    // Wait for the page to respond\n                    await page.waitForTimeout(2000);\n                    // Check if we have the \"Did you read carefully?\" dialog or direct \"Start\" button\n                    const pageText = await page.textContent(\"body\");\n                    if (pageText.includes(\"Did you read carefully?\")) {\n                        console.log('Found \"Did you read carefully?\" dialog');\n                        // Click \"Yes, ask me the questions\" button\n                        try {\n                            // Try multiple selectors for \"Yes\" button with increased timeout\n                            const yesSelectors = [\n                                'xpath=//*[@id=\"book-scroll\"]/div/div/div/button[2]',\n                                'button:has-text(\"Yes, ask me the questions\")',\n                                \"#book-scroll div div div button:nth-child(2)\",\n                                'button:has-text(\"Yes\") >> nth=1' // General Yes button fallback\n                            ];\n                            let yesClicked = false;\n                            for (const selector of yesSelectors){\n                                try {\n                                    await page.click(selector, {\n                                        timeout: 5000\n                                    });\n                                    console.log(`Clicked \"Yes\" button with selector: ${selector}`);\n                                    yesClicked = true;\n                                    break;\n                                } catch (error) {\n                                    console.log(`Failed to click with selector ${selector}:`, error.message);\n                                }\n                            }\n                            if (!yesClicked) {\n                                throw new Error('Could not find \"Yes, ask me the questions\" button');\n                            }\n                            console.log('Clicked \"Yes, ask me the questions\" button');\n                            // Wait for questions to load\n                            await page.waitForTimeout(3000);\n                            // Start the question-solving loop\n                            console.log(\"Starting question-solving process...\");\n                            // Initialize real-time data - doesnt work idk \n                            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                                isRunning: true,\n                                currentQuestion: \"\",\n                                currentAnswer: \"\",\n                                questionNumber: 0,\n                                srpEarned: 0,\n                                questionHistory: [],\n                                progress: 0,\n                                status: \"starting\"\n                            });\n                            await solveQuestions(page, initialUserTotalSrp);\n                        } catch (error) {\n                            console.log('Could not find \"Yes, ask me the questions\" button:', error.message);\n                        }\n                    } else {\n                        // Look for direct \"Start\" button\n                        console.log('Looking for direct \"Start\" button...');\n                        const startSelectors = [\n                            \"#book-scroll div div div button\",\n                            'button:has-text(\"Start\")',\n                            'button:has-text(\"start\")',\n                            '[data-test-id*=\"start\"]',\n                            'button[class*=\"start\"]'\n                        ];\n                        let startClicked = false;\n                        for (const selector of startSelectors){\n                            try {\n                                await page.click(selector, {\n                                    timeout: 3000\n                                });\n                                console.log(`Clicked \"Start\" button with selector: ${selector}`);\n                                startClicked = true;\n                                break;\n                            } catch (error) {\n                            // Continue to next selector\n                            }\n                        }\n                        if (startClicked) {\n                            // Wait for questions to load\n                            await page.waitForTimeout(3000);\n                            // Start the question-solving loop  \n                            console.log(\"Starting question-solving process...\");\n                            // Initialize real-time data\n                            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                                isRunning: true,\n                                currentQuestion: \"\",\n                                currentAnswer: \"\",\n                                questionNumber: 0,\n                                srpEarned: 0,\n                                questionHistory: [],\n                                progress: 0,\n                                status: \"starting\"\n                            });\n                            await solveQuestions(page, initialUserTotalSrp);\n                        } else {\n                            console.log('Could not find \"Start\" button - checking if questions already appeared');\n                            // Check if questions are already visible\n                            const hasQuestions = await page.evaluate(()=>{\n                                return document.querySelector(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span\") !== null;\n                            });\n                            if (hasQuestions) {\n                                console.log(\"Questions found - starting solving process\");\n                                // Initialize real-time data\n                                (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                                    isRunning: true,\n                                    currentQuestion: \"\",\n                                    currentAnswer: \"\",\n                                    questionNumber: 0,\n                                    srpEarned: 0,\n                                    questionHistory: [],\n                                    progress: 0,\n                                    status: \"starting\"\n                                });\n                                await solveQuestions(page, initialUserTotalSrp);\n                            } else {\n                                console.log(\"No questions found after timeout\");\n                            }\n                        }\n                    }\n                } else {\n                    console.log('Could not find \"I have read up to here\" button');\n                }\n            } catch (error) {\n                console.log(\"Error in question flow:\", error.message);\n            }\n            // Take a final screenshot\n            const screenshotPath = path__WEBPACK_IMPORTED_MODULE_1___default().resolve(process.cwd(), \"public\", \"screenshot.png\");\n            await page.screenshot({\n                path: screenshotPath\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: \"Successfully extracted story content\",\n                bookTitle: actualBookTitle,\n                storyContent: storyContent,\n                screenshot: \"/screenshot.png\"\n            });\n        } else {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Invalid action\"\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in navigate endpoint:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Function to solve questions using AI\nasync function solveQuestions(page, initialUserTotalSrp) {\n    try {\n        let questionCount = 0;\n        let lastQuestionNumber = \"\";\n        const maxQuestions = 10; // Limit to prevent infinite loops\n        while(questionCount < maxQuestions){\n            console.log(`Processing question ${questionCount + 1}...`);\n            // Wait for question to load\n            await page.waitForTimeout(2000);\n            // Extract the question text and answer options using the specific selectors\n            const questionData = await page.evaluate(()=>{\n                // Get the question number from the span\n                const questionNumberElement = document.querySelector(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span\");\n                // Get the question text from the div\n                const questionTextElement = document.querySelector(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > div\");\n                // Get all answer option buttons\n                const answerButtons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                if (questionNumberElement && questionTextElement) {\n                    const questionNumber = questionNumberElement.textContent.trim();\n                    const questionText = questionTextElement.textContent.trim();\n                    // Extract answer options\n                    const answerOptions = [];\n                    answerButtons.forEach((button, index)=>{\n                        const buttonText = button.textContent.trim();\n                        if (buttonText) {\n                            answerOptions.push(`${index + 1}. ${buttonText}`);\n                        }\n                    });\n                    // Check if this is a valid question (has Q followed by number and dot)\n                    if (/Q\\d+\\./.test(questionNumber)) {\n                        return {\n                            found: true,\n                            questionNumber: questionNumber,\n                            questionText: questionText,\n                            answerOptions: answerOptions,\n                            fullQuestion: questionNumber + \" \" + questionText\n                        };\n                    }\n                }\n                return {\n                    found: false,\n                    questionText: \"\",\n                    questionNumber: \"\",\n                    fullQuestion: \"\",\n                    answerOptions: []\n                };\n            });\n            if (!questionData.found) {\n                console.log(\"No more questions found, ending question-solving process\");\n                break;\n            }\n            // Check if this is the same question as before (to avoid infinite loops)\n            if (questionData.questionNumber === lastQuestionNumber) {\n                console.log(\"Same question detected, might be stuck. Ending process.\");\n                break;\n            }\n            lastQuestionNumber = questionData.questionNumber;\n            console.log(\"Question found:\", questionData.questionNumber, \"-\", questionData.questionText.substring(0, 200) + \"...\");\n            console.log(\"Answer options:\", questionData.answerOptions);\n            // Update real-time data with current question\n            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                isRunning: true,\n                currentQuestion: questionData.questionText,\n                currentAnswer: \"\",\n                questionNumber: parseInt(questionData.questionNumber.replace(/\\D/g, \"\")),\n                status: \"solving\"\n            });\n            // Send question to AI with story context and answer options\n            const answer = await getAIAnswer(questionData.fullQuestion, questionData.answerOptions);\n            console.log(\"AI Answer:\", answer);\n            // Update real-time data with AI answer\n            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                currentAnswer: answer,\n                status: \"answering\"\n            });\n            // Try to select the answer using the specific button selectors\n            await selectAnswer(page, answer, questionData.questionNumber);\n            // Add to question history and update real-time data\n            const currentHistory = global.realtimeData?.questionHistory || [];\n            const newHistoryItem = {\n                number: parseInt(questionData.questionNumber.replace(/\\D/g, \"\")),\n                question: questionData.questionText,\n                answer: answer\n            };\n            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                questionHistory: [\n                    ...currentHistory,\n                    newHistoryItem\n                ],\n                status: \"completed\"\n            });\n            // Wait a bit after selecting answer\n            await page.waitForTimeout(2000);\n            // Check if there's a \"Next\" button or similar to move to next question\n            const nextButton = await page.$('button:has-text(\"Next\")') || await page.$('button:has-text(\"Continue\")') || await page.$('[data-test-id*=\"next\"]') || await page.$('button[class*=\"next\"]');\n            if (nextButton) {\n                await nextButton.click();\n                console.log(\"Clicked next button\");\n                await page.waitForTimeout(1000);\n            } else {\n                console.log(\"No next button found, waiting to see if question changes automatically\");\n                await page.waitForTimeout(3000);\n                // Check if the question number has changed\n                const newQuestionData = await page.evaluate(()=>{\n                    const questionNumberElement = document.querySelector(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span\");\n                    if (questionNumberElement) {\n                        return questionNumberElement.textContent.trim();\n                    }\n                    return \"\";\n                });\n                if (newQuestionData === lastQuestionNumber) {\n                    console.log(\"Question did not change, ending process\");\n                    break;\n                }\n            }\n            questionCount++;\n        }\n        console.log(`Question-solving process completed. Processed ${questionCount} questions.`);\n        // Update real-time data - automation completed\n        (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n            isRunning: false,\n            status: \"completed\"\n        });\n        // Check SRP earned and restart if needed\n        await checkSrpAndRestart(page, initialUserTotalSrp);\n    } catch (error) {\n        console.error(\"Error in question-solving process:\", error);\n    }\n}\n// Function to get AI answer using the story context\nasync function getAIAnswer(questionText, answerOptions) {\n    try {\n        const storyContent = global.sessionStoryContent || \"\";\n        // Create prompt with answer options\n        let prompt = `Here is the context from the story:\\n${storyContent}\\n\\nNow, please answer this question based on the story above:\\n${questionText}\\n\\n`;\n        if (answerOptions && answerOptions.length > 0) {\n            prompt += `You MUST choose from one of these options only:\\n${answerOptions.join(\"\\n\")}\\n\\nRespond with ONLY the exact text of the correct option (without the number). Do not add any explanation.`;\n        } else {\n            prompt += `Give ONLY the direct answer without explanation.`;\n        }\n        // Use the same API as the extension\n        const API_KEY = \"AIzaSyAdbFHKgcsOz9YweT0fZCwJbNODoEwSGzs\";\n        const API_ENDPOINT = \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent\";\n        const requestBody = {\n            contents: [\n                {\n                    parts: [\n                        {\n                            text: prompt\n                        }\n                    ]\n                }\n            ],\n            generationConfig: {\n                temperature: 0.1,\n                topK: 1,\n                topP: 1,\n                maxOutputTokens: 2048\n            }\n        };\n        const response = await fetch(`${API_ENDPOINT}?key=${API_KEY}`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new Error(data.error?.message || `HTTP error ${response.status}`);\n        }\n        if (!data.candidates || data.candidates.length === 0) {\n            throw new Error(\"No solution generated\");\n        }\n        return data.candidates[0].content.parts[0].text.trim();\n    } catch (error) {\n        console.error(\"Error getting AI answer:\", error);\n        return \"Error getting answer\";\n    }\n}\n// Function to select the answer on the page\nasync function selectAnswer(page, answer, questionNumber) {\n    try {\n        console.log(\"Attempting to select answer:\", answer);\n        // Wait for buttons to be stable\n        await page.waitForTimeout(1000);\n        // Get button count first\n        const buttonCount = await page.evaluate(()=>{\n            const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n            return buttons.length;\n        });\n        console.log(`Found ${buttonCount} answer buttons`);\n        if (buttonCount === 0) {\n            console.log(\"No answer buttons found\");\n            return;\n        }\n        // Check each button to find the one that matches the answer\n        for(let i = 0; i < buttonCount; i++){\n            try {\n                // Re-query the button each time to avoid detached DOM issues\n                const buttonText = await page.evaluate((index)=>{\n                    const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                    return buttons[index] ? buttons[index].textContent : null;\n                }, i);\n                console.log(`Button ${i + 1} text:`, buttonText);\n                // Check if this button contains the answer (exact match or partial match)\n                if (buttonText && (buttonText.toLowerCase().trim() === answer.toLowerCase().trim() || buttonText.toLowerCase().includes(answer.toLowerCase()) || answer.toLowerCase().includes(buttonText.toLowerCase()))) {\n                    // Try multiple click methods to handle intercepting elements\n                    let clicked = false;\n                    // Method 1: Direct DOM click (most reliable)\n                    try {\n                        clicked = await page.evaluate((index)=>{\n                            const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                            if (buttons[index]) {\n                                buttons[index].click();\n                                return true;\n                            }\n                            return false;\n                        }, i);\n                    } catch (error) {\n                        console.log(`DOM click failed for button ${i + 1}:`, error.message);\n                    }\n                    // Method 2: Force click if DOM click failed\n                    if (!clicked) {\n                        try {\n                            clicked = await page.evaluate((index)=>{\n                                const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                                if (buttons[index]) {\n                                    // Remove any intercepting elements temporarily\n                                    const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*=\"enter-down\"]');\n                                    interceptors.forEach((el)=>el.style.pointerEvents = \"none\");\n                                    // Trigger click event\n                                    const event = new MouseEvent(\"click\", {\n                                        bubbles: true,\n                                        cancelable: true\n                                    });\n                                    buttons[index].dispatchEvent(event);\n                                    // Restore pointer events\n                                    setTimeout(()=>{\n                                        interceptors.forEach((el)=>el.style.pointerEvents = \"\");\n                                    }, 100);\n                                    return true;\n                                }\n                                return false;\n                            }, i);\n                        } catch (error) {\n                            console.log(`Force click failed for button ${i + 1}:`, error.message);\n                        }\n                    }\n                    if (clicked) {\n                        console.log(`Selected answer button ${i + 1}: ${buttonText}`);\n                        return;\n                    }\n                }\n            } catch (error) {\n                console.log(`Error checking button ${i + 1}:`, error.message);\n            }\n        }\n        // If no exact match found, try to find the best partial match\n        let bestMatch = -1;\n        let bestMatchScore = 0;\n        for(let i = 0; i < buttonCount; i++){\n            try {\n                const buttonText = await page.evaluate((index)=>{\n                    const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                    return buttons[index] ? buttons[index].textContent : null;\n                }, i);\n                if (buttonText) {\n                    // (simple word matching)\n                    const answerWords = answer.toLowerCase().split(\" \");\n                    const buttonWords = buttonText.toLowerCase().split(\" \");\n                    let matchCount = 0;\n                    answerWords.forEach((word)=>{\n                        if (buttonWords.some((buttonWord)=>buttonWord.includes(word) || word.includes(buttonWord))) {\n                            matchCount++;\n                        }\n                    });\n                    const score = matchCount / Math.max(answerWords.length, buttonWords.length);\n                    if (score > bestMatchScore) {\n                        bestMatchScore = score;\n                        bestMatch = i;\n                    }\n                }\n            } catch (error) {\n                console.log(`Error calculating match for button ${i + 1}:`, error.message);\n            }\n        }\n        if (bestMatch >= 0 && bestMatchScore > 0.3) {\n            // Try  clicking shit for the best match\n            let clicked = false;\n            let buttonText = \"\";\n            try {\n                const result = await page.evaluate((index)=>{\n                    const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                    if (buttons[index]) {\n                        try {\n                            buttons[index].click();\n                            return {\n                                success: true,\n                                text: buttons[index].textContent\n                            };\n                        } catch (e) {\n                            // Force click if normal click fails\n                            const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*=\"enter-down\"]');\n                            interceptors.forEach((el)=>el.style.pointerEvents = \"none\");\n                            const event = new MouseEvent(\"click\", {\n                                bubbles: true,\n                                cancelable: true\n                            });\n                            buttons[index].dispatchEvent(event);\n                            setTimeout(()=>{\n                                interceptors.forEach((el)=>el.style.pointerEvents = \"\");\n                            }, 100);\n                            return {\n                                success: true,\n                                text: buttons[index].textContent\n                            };\n                        }\n                    }\n                    return {\n                        success: false,\n                        text: null\n                    };\n                }, bestMatch);\n                clicked = result.success;\n                buttonText = result.text;\n            } catch (error) {\n                console.log(`Error clicking best match button:`, error.message);\n            }\n            if (clicked) {\n                console.log(`Selected best match button ${bestMatch + 1}: ${buttonText} (score: ${bestMatchScore})`);\n                return;\n            }\n        }\n        // If no exact match found, try the first button as fallback\n        if (buttonCount > 0) {\n            console.log(\"No exact match found, clicking first button as fallback\");\n            let clicked = false;\n            let buttonText = \"\";\n            try {\n                const result = await page.evaluate(()=>{\n                    const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                    if (buttons[0]) {\n                        try {\n                            buttons[0].click();\n                            return {\n                                success: true,\n                                text: buttons[0].textContent\n                            };\n                        } catch (e) {\n                            // Force click if normal click fails\n                            const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*=\"enter-down\"]');\n                            interceptors.forEach((el)=>el.style.pointerEvents = \"none\");\n                            const event = new MouseEvent(\"click\", {\n                                bubbles: true,\n                                cancelable: true\n                            });\n                            buttons[0].dispatchEvent(event);\n                            setTimeout(()=>{\n                                interceptors.forEach((el)=>el.style.pointerEvents = \"\");\n                            }, 100);\n                            return {\n                                success: true,\n                                text: buttons[0].textContent\n                            };\n                        }\n                    }\n                    return {\n                        success: false,\n                        text: null\n                    };\n                });\n                clicked = result.success;\n                buttonText = result.text;\n            } catch (error) {\n                console.log(`Error clicking fallback button:`, error.message);\n            }\n            if (clicked) {\n                console.log(`Selected first button: ${buttonText}`);\n            }\n        } else {\n            console.log(\"Could not find any answer buttons\");\n        }\n    } catch (error) {\n        console.error(\"Error selecting answer:\", error);\n    }\n}\n// Function to check SRP earned and restart the flow if needed\nasync function checkSrpAndRestart(page, initialUserTotalSrp) {\n    try {\n        console.log(\"Checking SRP earned and determining next steps...\");\n        // Wait a moment for any final updates\n        await page.waitForTimeout(2000);\n        // Get current user total SRP\n        const currentUserTotalSrp = await page.evaluate(()=>{\n            const userTotalSrpElement = document.querySelector(\".sr_92b39de6\");\n            return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\\d,]/g, \"\").replace(\",\", \"\") : null;\n        }).catch(()=>null);\n        console.log(`Initial User Total SRP: ${initialUserTotalSrp}`);\n        console.log(`Current User Total SRP: ${currentUserTotalSrp}`);\n        // Calculate SRP earned\n        let srpEarned = 0;\n        if (initialUserTotalSrp && currentUserTotalSrp) {\n            const initialNum = parseInt(initialUserTotalSrp) || 0;\n            const currentNum = parseInt(currentUserTotalSrp) || 0;\n            srpEarned = currentNum - initialNum;\n        }\n        console.log(`SRP Earned: ${srpEarned}`);\n        // Update real-time SRP data\n        (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n            srpEarned: srpEarned\n        });\n        // Get the session SRP info\n        const srpInfo = global.sessionSrpInfo || {};\n        const initialSrp = parseInt(srpInfo.initialUserTotalSrp) || 0;\n        const targetSrp = parseInt(srpInfo.targetSrpNeeded) || 0;\n        const currentSrp = parseInt(currentUserTotalSrp) || 0;\n        const totalSrpEarned = currentSrp - initialSrp;\n        console.log(`Initial User Total SRP: ${initialSrp}`);\n        console.log(`Current User Total SRP: ${currentSrp}`);\n        console.log(`SRP Earned This Session: ${totalSrpEarned}`);\n        console.log(`Target SRP: ${targetSrp}`);\n        // Update global session tracking\n        global.sessionSrpGoal = totalSrpEarned;\n        if (targetSrp > 0 && totalSrpEarned < targetSrp) {\n            console.log(`Need more SRP (${totalSrpEarned}/${targetSrp}). Continuing with current book...`);\n            // Extract story content again from current page\n            const storyContent = await page.evaluate(()=>{\n                const fullText = document.body.innerText;\n                const startMarker = \"Start reading here\";\n                const endMarker = \"Stop reading here\";\n                const startIndex = fullText.indexOf(startMarker);\n                const endIndex = fullText.indexOf(endMarker);\n                if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {\n                    return fullText.substring(startIndex + startMarker.length, endIndex).trim();\n                } else if (startIndex !== -1) {\n                    return fullText.substring(startIndex + startMarker.length).trim();\n                } else {\n                    return fullText;\n                }\n            });\n            global.sessionStoryContent = storyContent;\n            // Continue directly with question flow\n            await continueQuestionFlow(page, initialUserTotalSrp);\n        } else {\n            console.log(`Target SRP reached! (${totalSrpEarned}/${targetSrp}). Session complete.`);\n        }\n    } catch (error) {\n        console.error(\"Error checking SRP and restarting:\", error);\n    }\n}\n// Function to restart the book selection flow\nasync function restartBookFlow(page) {\n    try {\n        console.log(\"Restarting book selection flow...\");\n        // Navigate back to the library/book selection page\n        // Try to find and click a back/home button\n        const backSelectors = [\n            'button:has-text(\"Back\")',\n            'button:has-text(\"Home\")',\n            'button:has-text(\"Library\")',\n            'a:has-text(\"Back\")',\n            'a:has-text(\"Home\")',\n            'a:has-text(\"Library\")',\n            '[data-test-id*=\"back\"]',\n            '[data-test-id*=\"home\"]'\n        ];\n        let backClicked = false;\n        for (const selector of backSelectors){\n            try {\n                await page.click(selector, {\n                    timeout: 2000\n                });\n                console.log(`Clicked back button with selector: ${selector}`);\n                backClicked = true;\n                break;\n            } catch (error) {\n            // Continue to next selector\n            }\n        }\n        if (!backClicked) {\n            console.log(\"No back button found, navigating to library URL...\");\n            await page.goto(\"https://www.sparxreader.com/library\", {\n                waitUntil: \"networkidle\"\n            });\n        }\n        // Wait for the page to load\n        await page.waitForTimeout(3000);\n        // Look for a new book \n        console.log(\"Looking for next book...\");\n        // Extract new book info\n        const newBookInfo = await page.evaluate(()=>{\n            const titleElement = document.querySelector(\"div.sr_ea851119\");\n            const srpNeededElement = document.querySelector(\"#root > div > div:nth-child(2) > div > div > div:nth-child(2) > div > div:nth-child(2) > div:nth-child(1) > div > div > div:nth-child(2) > div > div:nth-child(2)\");\n            return {\n                bookTitle: titleElement ? titleElement.textContent.trim() : null,\n                srpNeeded: srpNeededElement ? srpNeededElement.textContent.trim() : null\n            };\n        }).catch(()=>({\n                bookTitle: null,\n                srpNeeded: null\n            }));\n        if (newBookInfo.bookTitle) {\n            console.log(`Found new book: ${newBookInfo.bookTitle}`);\n            console.log(`SRP Needed: ${newBookInfo.srpNeeded}`);\n            // Update global session info\n            global.sessionSrpInfo = newBookInfo;\n            // Auto-confirm this book and continue \n            console.log(\"Auto-confirming new book and continuing...\");\n            // Extract story content and continue the flow\n            await page.waitForTimeout(1000);\n            const storyContent = await page.evaluate(()=>{\n                const fullText = document.body.innerText;\n                const startMarker = \"Start reading here\";\n                const endMarker = \"Stop reading here\";\n                const startIndex = fullText.indexOf(startMarker);\n                const endIndex = fullText.indexOf(endMarker);\n                if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {\n                    return fullText.substring(startIndex + startMarker.length, endIndex).trim();\n                } else if (startIndex !== -1) {\n                    return fullText.substring(startIndex + startMarker.length).trim();\n                } else {\n                    return fullText;\n                }\n            });\n            // Store new story content\n            global.sessionStoryContent = storyContent;\n            console.log(`New story content extracted (${storyContent.length} characters)`);\n            // Get new initial SRP count\n            const newInitialSrp = await page.evaluate(()=>{\n                const totalSrpElement = document.querySelector(\"#header-portal > div:nth-child(3) > div:nth-child(1) > div\");\n                return totalSrpElement ? totalSrpElement.textContent.trim() : null;\n            }).catch(()=>null);\n            // Continue with the question flow\n            await continueQuestionFlow(page, newInitialSrp);\n        } else {\n            console.log(\"No new book found. Session may be complete or there might be an issue.\");\n        }\n    } catch (error) {\n        console.error(\"Error restarting book flow:\", error);\n    }\n}\n// Function to continue with the question flow for a new book\nasync function continueQuestionFlow(page, initialUserTotalSrp) {\n    try {\n        console.log(\"Continuing with question flow for new book...\");\n        // Click \"I have read up to here\" button and continue the flow\n        const readUpToHereSelectors = [\n            \"#book-scroll div:nth-child(2) div:nth-child(3) div div div button\",\n            'button:has-text(\"I have read up to here\")',\n            'button:has-text(\"read up to here\")',\n            '[data-test-id*=\"read-up-to-here\"]',\n            'button[class*=\"read\"]'\n        ];\n        let readUpToHereClicked = false;\n        for (const selector of readUpToHereSelectors){\n            try {\n                await page.click(selector, {\n                    timeout: 3000\n                });\n                console.log(`Clicked \"I have read up to here\" button with selector: ${selector}`);\n                readUpToHereClicked = true;\n                break;\n            } catch (error) {\n            // Continue to next selector\n            }\n        }\n        if (readUpToHereClicked) {\n            await page.waitForTimeout(2000);\n            // Check for dialog and handle accordingly\n            const pageText = await page.textContent(\"body\");\n            if (pageText.includes(\"Did you read carefully?\")) {\n                console.log('Found \"Did you read carefully?\" dialog');\n                const yesSelectors = [\n                    'xpath=//*[@id=\"book-scroll\"]/div/div/div/button[2]',\n                    'button:has-text(\"Yes, ask me the questions\")',\n                    \"#book-scroll div div div button:nth-child(2)\",\n                    'button:has-text(\"Yes\") >> nth=1' // General Yes button fallback\n                ];\n                let yesClicked = false;\n                for (const selector of yesSelectors){\n                    try {\n                        await page.click(selector, {\n                            timeout: 5000\n                        });\n                        console.log(`Clicked \"Yes\" button with selector: ${selector}`);\n                        yesClicked = true;\n                        break;\n                    } catch (error) {\n                        console.log(`Failed to click with selector ${selector}:`, error.message);\n                    }\n                }\n                if (!yesClicked) {\n                    throw new Error('Could not find \"Yes, ask me the questions\" button');\n                }\n                console.log('Clicked \"Yes, ask me the questions\" button');\n                await page.waitForTimeout(3000);\n                await solveQuestions(page, initialUserTotalSrp);\n            } else {\n                // Look for direct \"Start\" button\n                const startSelectors = [\n                    \"#book-scroll div div div button\",\n                    'button:has-text(\"Start\")',\n                    'button:has-text(\"start\")',\n                    '[data-test-id*=\"start\"]',\n                    'button[class*=\"start\"]'\n                ];\n                for (const selector of startSelectors){\n                    try {\n                        await page.click(selector, {\n                            timeout: 3000\n                        });\n                        console.log(`Clicked \"Start\" button with selector: ${selector}`);\n                        await page.waitForTimeout(3000);\n                        await solveQuestions(page, initialUserTotalSrp);\n                        break;\n                    } catch (error) {\n                    // Continue to next selector\n                    }\n                }\n            }\n        }\n    } catch (error) {\n        console.error(\"Error continuing question flow:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/navigate/route.js\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/realtime/route.js":
/*!***********************************************!*\
  !*** ./app/api/sparxreader/realtime/route.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   updateRealtimeData: () => (/* binding */ updateRealtimeData)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n// Global state for real-time updates\nglobal.realtimeData = global.realtimeData || {\n    isRunning: false,\n    currentQuestion: \"\",\n    currentAnswer: \"\",\n    questionNumber: 0,\n    srpEarned: 0,\n    questionHistory: [],\n    progress: 0,\n    status: \"idle\"\n};\nasync function GET(request) {\n    try {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: global.realtimeData\n        });\n    } catch (error) {\n        console.error(\"Error getting realtime data:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const updates = await request.json();\n        // Update global state\n        global.realtimeData = {\n            ...global.realtimeData,\n            ...updates,\n            lastUpdated: new Date().toISOString()\n        };\n        console.log(\"Realtime data updated:\", global.realtimeData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: global.realtimeData\n        });\n    } catch (error) {\n        console.error(\"Error updating realtime data:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to update realtime data from other routes\nfunction updateRealtimeData(updates) {\n    global.realtimeData = {\n        ...global.realtimeData,\n        ...updates,\n        lastUpdated: new Date().toISOString()\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/realtime/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fnavigate%2Froute&page=%2Fapi%2Fsparxreader%2Fnavigate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fnavigate%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();