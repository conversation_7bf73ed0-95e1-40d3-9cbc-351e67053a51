"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sparxreader/navigate/route";
exports.ids = ["app/api/sparxreader/navigate/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fnavigate%2Froute&page=%2Fapi%2Fsparxreader%2Fnavigate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fnavigate%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fnavigate%2Froute&page=%2Fapi%2Fsparxreader%2Fnavigate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fnavigate%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_sparxreader_navigate_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/sparxreader/navigate/route.js */ \"(rsc)/./app/api/sparxreader/navigate/route.js\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sparxreader/navigate/route\",\n        pathname: \"/api/sparxreader/navigate\",\n        filename: \"route\",\n        bundlePath: \"app/api/sparxreader/navigate/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\sparxreader\\\\navigate\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_sparxreader_navigate_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/sparxreader/navigate/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fnavigate%2Froute&page=%2Fapi%2Fsparxreader%2Fnavigate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fnavigate%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/browser-context.js":
/*!************************************************!*\
  !*** ./app/api/sparxreader/browser-context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearGlobalBrowser: () => (/* binding */ clearGlobalBrowser),\n/* harmony export */   getGlobalBrowser: () => (/* binding */ getGlobalBrowser),\n/* harmony export */   getGlobalPage: () => (/* binding */ getGlobalPage),\n/* harmony export */   setGlobalBrowser: () => (/* binding */ setGlobalBrowser)\n/* harmony export */ });\n// Global browser context storage using globalThis for persistence\nif (!globalThis.sparxBrowserContext) {\n    globalThis.sparxBrowserContext = {\n        browser: null,\n        page: null\n    };\n}\nfunction setGlobalBrowser(browser, page) {\n    globalThis.sparxBrowserContext.browser = browser;\n    globalThis.sparxBrowserContext.page = page;\n    console.log(\"Browser context set:\", !!browser, !!page);\n}\nfunction getGlobalBrowser() {\n    return globalThis.sparxBrowserContext.browser;\n}\nfunction getGlobalPage() {\n    return globalThis.sparxBrowserContext.page;\n}\nfunction clearGlobalBrowser() {\n    globalThis.sparxBrowserContext.browser = null;\n    globalThis.sparxBrowserContext.page = null;\n    console.log(\"Browser context cleared\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/browser-context.js\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/navigate/route.js":
/*!***********************************************!*\
  !*** ./app/api/sparxreader/navigate/route.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _browser_context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../browser-context.js */ \"(rsc)/./app/api/sparxreader/browser-context.js\");\n/* harmony import */ var _realtime_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../realtime/route.js */ \"(rsc)/./app/api/sparxreader/realtime/route.js\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const { action, bookTitle, targetSrp } = await request.json();\n        if (action === \"confirm\") {\n            const page = (0,_browser_context_js__WEBPACK_IMPORTED_MODULE_2__.getGlobalPage)();\n            const browser = (0,_browser_context_js__WEBPACK_IMPORTED_MODULE_2__.getGlobalBrowser)();\n            console.log(\"Navigate route - checking browser session:\");\n            console.log(\"Page exists:\", !!page);\n            console.log(\"Browser exists:\", !!browser);\n            if (!page) {\n                console.log(\"No page found in global context\");\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"No browser session available. Please restart the application.\"\n                }, {\n                    status: 400\n                });\n            }\n            if (!browser) {\n                console.log(\"No browser found in global context\");\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"No browser session available. Please restart the application.\"\n                }, {\n                    status: 400\n                });\n            }\n            console.log(\"Browser session found, proceeding...\");\n            console.log(\"User confirmed book, extracting story content from current page...\");\n            // Extract the actual book title from the book page\n            let actualBookTitle = bookTitle; // fallback to passed title\n            try {\n                const bookTitleElement = await page.waitForSelector(\"h2.sr_942936b5.sr_b59a8fb2\", {\n                    timeout: 5000\n                });\n                if (bookTitleElement) {\n                    actualBookTitle = await bookTitleElement.textContent();\n                    actualBookTitle = actualBookTitle.trim();\n                    console.log(`Extracted actual book title: ${actualBookTitle}`);\n                }\n            } catch (error) {\n                console.log(\"Could not extract book title from book page, using fallback\");\n            }\n            //  just extract the content directly since its the second go around\n            // Wait a moment to ensure page is fully loaded\n            await page.waitForTimeout(1000);\n            // Extract the story content from the current page,\n            const storyContent = await page.evaluate(()=>{\n                const fullText = document.body.innerText;\n                // Find the start and end markers\n                const startMarker = \"Start reading here\";\n                const endMarker = \"Stop reading here\";\n                const startIndex = fullText.indexOf(startMarker);\n                const endIndex = fullText.indexOf(endMarker);\n                if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {\n                    // Extract content between the markers (excluding the markers themselves)\n                    return fullText.substring(startIndex + startMarker.length, endIndex).trim();\n                } else if (startIndex !== -1) {\n                    // If only start marker found, extract from start marker to end\n                    return fullText.substring(startIndex + startMarker.length).trim();\n                } else {\n                    // If no markers found, return full text as fallback\n                    return fullText;\n                }\n            });\n            // Log some of the story content for debugging\n            const storyPreview = storyContent.substring(0, 500);\n            console.log(\"Story content extracted (first 500 characters):\");\n            console.log(storyPreview);\n            console.log(`Total story length: ${storyContent.length} characters`);\n            // Store the story content in the session for use with AI\n            global.sessionStoryContent = storyContent;\n            console.log(\"Story content stored for AI context\");\n            // Store target SRP in session info and browser localStorage\n            if (targetSrp) {\n                global.sessionSrpInfo = global.sessionSrpInfo || {};\n                global.sessionSrpInfo.targetSrpNeeded = targetSrp;\n                console.log(`Target SRP set to: ${targetSrp}`);\n                // Store target SRP in browser localStorage so extension can access it\n                await page.evaluate((target)=>{\n                    localStorage.setItem(\"targetSrp\", target.toString());\n                }, targetSrp);\n            }\n            // Get initial SRP count before starting questions\n            const initialSrpInfo = global.sessionSrpInfo || {};\n            const initialUserTotalSrp = await page.evaluate(()=>{\n                const userTotalSrpElement = document.querySelector(\".sr_92b39de6\");\n                return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\\d,]/g, \"\").replace(\",\", \"\") : null;\n            }).catch(()=>null);\n            console.log(`Initial User Total SRP: ${initialUserTotalSrp}`);\n            // Now click \"I have read up to here\" button\n            try {\n                console.log('Looking for \"I have read up to here\" button...');\n                // Try multiple selectors for the \"I have read up to here\" button\n                const readUpToHereSelectors = [\n                    'xpath=//*[@id=\"book-scroll\"]/div/div/div/button[2]',\n                    \"#book-scroll div:nth-child(2) div:nth-child(3) div div div button\",\n                    'button:has-text(\"I have read up to here\")',\n                    'button:has-text(\"read up to here\")',\n                    '[data-test-id*=\"read-up-to-here\"]',\n                    'button[class*=\"read\"]'\n                ];\n                let readUpToHereClicked = false;\n                for (const selector of readUpToHereSelectors){\n                    try {\n                        await page.click(selector, {\n                            timeout: 3000\n                        });\n                        console.log(`Clicked \"I have read up to here\" button with selector: ${selector}`);\n                        readUpToHereClicked = true;\n                        break;\n                    } catch (error) {\n                    // Continue to next selector\n                    }\n                }\n                if (readUpToHereClicked) {\n                    // Wait for the page to respond\n                    await page.waitForTimeout(2000);\n                    // Check if we have the \"Did you read carefully?\" dialog or direct \"Start\" button\n                    const pageText = await page.textContent(\"body\");\n                    if (pageText.includes(\"Did you read carefully?\")) {\n                        console.log('Found \"Did you read carefully?\" dialog');\n                        // Click \"Yes, ask me the questions\" button\n                        try {\n                            // Try multiple selectors for \"Yes\" button with increased timeout\n                            const yesSelectors = [\n                                'xpath=//*[@id=\"book-scroll\"]/div/div/div/button[2]',\n                                'button:has-text(\"Yes, ask me the questions\")',\n                                \"#book-scroll div div div button:nth-child(2)\",\n                                'button:has-text(\"Yes\") >> nth=1' // General Yes button fallback\n                            ];\n                            let yesClicked = false;\n                            for (const selector of yesSelectors){\n                                try {\n                                    await page.click(selector, {\n                                        timeout: 5000\n                                    });\n                                    console.log(`Clicked \"Yes\" button with selector: ${selector}`);\n                                    yesClicked = true;\n                                    break;\n                                } catch (error) {\n                                    console.log(`Failed to click with selector ${selector}:`, error.message);\n                                }\n                            }\n                            if (!yesClicked) {\n                                throw new Error('Could not find \"Yes, ask me the questions\" button');\n                            }\n                            console.log('Clicked \"Yes, ask me the questions\" button');\n                            // Wait for questions to load\n                            await page.waitForTimeout(3000);\n                            // Start the question-solving loop\n                            console.log(\"Starting question-solving process...\");\n                            // Initialize real-time data - doesnt work idk \n                            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                                isRunning: true,\n                                currentQuestion: \"\",\n                                currentAnswer: \"\",\n                                questionNumber: 0,\n                                srpEarned: 0,\n                                questionHistory: [],\n                                progress: 0,\n                                status: \"starting\"\n                            });\n                            await solveQuestions(page, initialUserTotalSrp, targetSrp);\n                        } catch (error) {\n                            console.log('Could not find \"Yes, ask me the questions\" button:', error.message);\n                        }\n                    } else {\n                        // Look for direct \"Start\" button\n                        console.log('Looking for direct \"Start\" button...');\n                        const startSelectors = [\n                            \"#book-scroll div div div button\",\n                            'button:has-text(\"Start\")',\n                            'button:has-text(\"start\")',\n                            '[data-test-id*=\"start\"]',\n                            'button[class*=\"start\"]'\n                        ];\n                        let startClicked = false;\n                        for (const selector of startSelectors){\n                            try {\n                                await page.click(selector, {\n                                    timeout: 3000\n                                });\n                                console.log(`Clicked \"Start\" button with selector: ${selector}`);\n                                startClicked = true;\n                                break;\n                            } catch (error) {\n                            // Continue to next selector\n                            }\n                        }\n                        if (startClicked) {\n                            // Wait for questions to load\n                            await page.waitForTimeout(3000);\n                            // Start the question-solving loop  \n                            console.log(\"Starting question-solving process...\");\n                            // Initialize real-time data\n                            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                                isRunning: true,\n                                currentQuestion: \"\",\n                                currentAnswer: \"\",\n                                questionNumber: 0,\n                                srpEarned: 0,\n                                questionHistory: [],\n                                progress: 0,\n                                status: \"starting\"\n                            });\n                            await solveQuestions(page, initialUserTotalSrp, targetSrp);\n                        } else {\n                            console.log('Could not find \"Start\" button - checking if questions already appeared');\n                            // Check if questions are already visible\n                            const hasQuestions = await page.evaluate(()=>{\n                                return document.querySelector(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span\") !== null;\n                            });\n                            if (hasQuestions) {\n                                console.log(\"Questions found - starting solving process\");\n                                // Initialize real-time data\n                                (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                                    isRunning: true,\n                                    currentQuestion: \"\",\n                                    currentAnswer: \"\",\n                                    questionNumber: 0,\n                                    srpEarned: 0,\n                                    questionHistory: [],\n                                    progress: 0,\n                                    status: \"starting\"\n                                });\n                                await solveQuestions(page, initialUserTotalSrp, targetSrp);\n                            } else {\n                                console.log(\"No questions found after timeout\");\n                            }\n                        }\n                    }\n                } else {\n                    console.log('Could not find \"I have read up to here\" button');\n                }\n            } catch (error) {\n                console.log(\"Error in question flow:\", error.message);\n            }\n            // Take a final screenshot\n            const screenshotPath = path__WEBPACK_IMPORTED_MODULE_1___default().resolve(process.cwd(), \"public\", \"screenshot.png\");\n            await page.screenshot({\n                path: screenshotPath\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: \"Successfully extracted story content\",\n                bookTitle: actualBookTitle,\n                storyContent: storyContent,\n                screenshot: \"/screenshot.png\"\n            });\n        } else {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Invalid action\"\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in navigate endpoint:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Function to solve questions using AI\nasync function solveQuestions(page, initialUserTotalSrp, srpTarget = 100) {\n    try {\n        let questionCount = 0;\n        let lastQuestionNumber = \"\";\n        const maxQuestions = Math.max(10, Math.ceil(srpTarget / 10)); // Dynamic limit based on SRP target (roughly 10 SRP per question)\n        while(questionCount < maxQuestions){\n            console.log(`Processing question ${questionCount + 1}...`);\n            // Wait for question to load\n            await page.waitForTimeout(2000);\n            // Extract the question text and answer options using the specific selectors\n            const questionData = await page.evaluate(()=>{\n                // Get the question number from the span\n                const questionNumberElement = document.querySelector(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span\");\n                // Get the question text from the div\n                const questionTextElement = document.querySelector(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > div\");\n                // Get all answer option buttons\n                const answerButtons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                if (questionNumberElement && questionTextElement) {\n                    const questionNumber = questionNumberElement.textContent.trim();\n                    const questionText = questionTextElement.textContent.trim();\n                    // Extract answer options\n                    const answerOptions = [];\n                    answerButtons.forEach((button, index)=>{\n                        const buttonText = button.textContent.trim();\n                        if (buttonText) {\n                            answerOptions.push(`${index + 1}. ${buttonText}`);\n                        }\n                    });\n                    // Check if this is a valid question (has Q followed by number and dot)\n                    if (/Q\\d+\\./.test(questionNumber)) {\n                        return {\n                            found: true,\n                            questionNumber: questionNumber,\n                            questionText: questionText,\n                            answerOptions: answerOptions,\n                            fullQuestion: questionNumber + \" \" + questionText\n                        };\n                    }\n                }\n                return {\n                    found: false,\n                    questionText: \"\",\n                    questionNumber: \"\",\n                    fullQuestion: \"\",\n                    answerOptions: []\n                };\n            });\n            if (!questionData.found) {\n                console.log(\"No more questions found, ending question-solving process\");\n                break;\n            }\n            // Check if this is the same question as before (to avoid infinite loops)\n            if (questionData.questionNumber === lastQuestionNumber) {\n                console.log(\"Same question detected, might be stuck. Ending process.\");\n                break;\n            }\n            lastQuestionNumber = questionData.questionNumber;\n            console.log(\"Question found:\", questionData.questionNumber, \"-\", questionData.questionText.substring(0, 200) + \"...\");\n            console.log(\"Answer options:\", questionData.answerOptions);\n            // Update real-time data with current question\n            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                isRunning: true,\n                currentQuestion: questionData.questionText,\n                currentAnswer: \"\",\n                questionNumber: parseInt(questionData.questionNumber.replace(/\\D/g, \"\")),\n                status: \"solving\"\n            });\n            // Send question to AI with story context and answer options\n            const answer = await getAIAnswer(questionData.fullQuestion, questionData.answerOptions);\n            console.log(\"AI Answer:\", answer);\n            // Update real-time data with AI answer\n            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                currentAnswer: answer,\n                status: \"answering\"\n            });\n            // Try to select the answer using the specific button selectors\n            await selectAnswer(page, answer, questionData.questionNumber);\n            // Add to question history and update real-time data\n            const currentHistory = global.realtimeData?.questionHistory || [];\n            const newHistoryItem = {\n                number: parseInt(questionData.questionNumber.replace(/\\D/g, \"\")),\n                question: questionData.questionText,\n                answer: answer\n            };\n            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                questionHistory: [\n                    ...currentHistory,\n                    newHistoryItem\n                ],\n                status: \"completed\"\n            });\n            // Wait a bit after selecting answer\n            await page.waitForTimeout(2000);\n            // Check if there's a \"Next\" button or similar to move to next question\n            const nextButton = await page.$('button:has-text(\"Next\")') || await page.$('button:has-text(\"Continue\")') || await page.$('[data-test-id*=\"next\"]') || await page.$('button[class*=\"next\"]');\n            if (nextButton) {\n                await nextButton.click();\n                console.log(\"Clicked next button\");\n                await page.waitForTimeout(1000);\n            } else {\n                console.log(\"No next button found, waiting to see if question changes automatically\");\n                await page.waitForTimeout(3000);\n                // Check if the question number has changed\n                const newQuestionData = await page.evaluate(()=>{\n                    const questionNumberElement = document.querySelector(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span\");\n                    if (questionNumberElement) {\n                        return questionNumberElement.textContent.trim();\n                    }\n                    return \"\";\n                });\n                if (newQuestionData === lastQuestionNumber) {\n                    console.log(\"Question did not change, ending process\");\n                    break;\n                }\n            }\n            questionCount++;\n        }\n        console.log(`Question-solving process completed. Processed ${questionCount} questions.`);\n        // Update real-time data - automation completed\n        (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n            isRunning: false,\n            status: \"completed\"\n        });\n        // Check SRP earned and restart if needed\n        await checkSrpAndRestart(page, initialUserTotalSrp);\n    } catch (error) {\n        console.error(\"Error in question-solving process:\", error);\n    }\n}\n// Function to get AI answer using the story context\nasync function getAIAnswer(questionText, answerOptions) {\n    try {\n        const storyContent = global.sessionStoryContent || \"\";\n        // Create prompt with answer options\n        let prompt = `Here is the context from the story:\\n${storyContent}\\n\\nNow, please answer this question based on the story above:\\n${questionText}\\n\\n`;\n        if (answerOptions && answerOptions.length > 0) {\n            prompt += `You MUST choose from one of these options only:\\n${answerOptions.join(\"\\n\")}\\n\\nRespond with ONLY the exact text of the correct option (without the number). Do not add any explanation.`;\n        } else {\n            prompt += `Give ONLY the direct answer without explanation.`;\n        }\n        // Use the same API as the extension\n        const API_KEY = \"AIzaSyAdbFHKgcsOz9YweT0fZCwJbNODoEwSGzs\";\n        const API_ENDPOINT = \"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent\";\n        const requestBody = {\n            contents: [\n                {\n                    parts: [\n                        {\n                            text: prompt\n                        }\n                    ]\n                }\n            ],\n            generationConfig: {\n                temperature: 0.1,\n                topK: 1,\n                topP: 1,\n                maxOutputTokens: 2048\n            }\n        };\n        const response = await fetch(`${API_ENDPOINT}?key=${API_KEY}`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(requestBody)\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new Error(data.error?.message || `HTTP error ${response.status}`);\n        }\n        if (!data.candidates || data.candidates.length === 0) {\n            throw new Error(\"No solution generated\");\n        }\n        return data.candidates[0].content.parts[0].text.trim();\n    } catch (error) {\n        console.error(\"Error getting AI answer:\", error);\n        return \"Error getting answer\";\n    }\n}\n// Function to select the answer on the page\nasync function selectAnswer(page, answer, questionNumber) {\n    try {\n        console.log(\"Attempting to select answer:\", answer);\n        // Wait for buttons to be stable\n        await page.waitForTimeout(1000);\n        // Get button count first\n        const buttonCount = await page.evaluate(()=>{\n            const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n            return buttons.length;\n        });\n        console.log(`Found ${buttonCount} answer buttons`);\n        if (buttonCount === 0) {\n            console.log(\"No answer buttons found\");\n            return;\n        }\n        // Check each button to find the one that matches the answer\n        for(let i = 0; i < buttonCount; i++){\n            try {\n                // Re-query the button each time to avoid detached DOM issues\n                const buttonText = await page.evaluate((index)=>{\n                    const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                    return buttons[index] ? buttons[index].textContent : null;\n                }, i);\n                console.log(`Button ${i + 1} text:`, buttonText);\n                // Check if this button contains the answer (exact match or partial match)\n                if (buttonText && (buttonText.toLowerCase().trim() === answer.toLowerCase().trim() || buttonText.toLowerCase().includes(answer.toLowerCase()) || answer.toLowerCase().includes(buttonText.toLowerCase()))) {\n                    // Try multiple click methods to handle intercepting elements\n                    let clicked = false;\n                    // Method 1: Direct DOM click (most reliable)\n                    try {\n                        clicked = await page.evaluate((index)=>{\n                            const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                            if (buttons[index]) {\n                                buttons[index].click();\n                                return true;\n                            }\n                            return false;\n                        }, i);\n                    } catch (error) {\n                        console.log(`DOM click failed for button ${i + 1}:`, error.message);\n                    }\n                    // Method 2: Force click if DOM click failed\n                    if (!clicked) {\n                        try {\n                            clicked = await page.evaluate((index)=>{\n                                const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                                if (buttons[index]) {\n                                    // Remove any intercepting elements temporarily\n                                    const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*=\"enter-down\"]');\n                                    interceptors.forEach((el)=>el.style.pointerEvents = \"none\");\n                                    // Trigger click event\n                                    const event = new MouseEvent(\"click\", {\n                                        bubbles: true,\n                                        cancelable: true\n                                    });\n                                    buttons[index].dispatchEvent(event);\n                                    // Restore pointer events\n                                    setTimeout(()=>{\n                                        interceptors.forEach((el)=>el.style.pointerEvents = \"\");\n                                    }, 100);\n                                    return true;\n                                }\n                                return false;\n                            }, i);\n                        } catch (error) {\n                            console.log(`Force click failed for button ${i + 1}:`, error.message);\n                        }\n                    }\n                    if (clicked) {\n                        console.log(`Selected answer button ${i + 1}: ${buttonText}`);\n                        return;\n                    }\n                }\n            } catch (error) {\n                console.log(`Error checking button ${i + 1}:`, error.message);\n            }\n        }\n        // If no exact match found, try to find the best partial match\n        let bestMatch = -1;\n        let bestMatchScore = 0;\n        for(let i = 0; i < buttonCount; i++){\n            try {\n                const buttonText = await page.evaluate((index)=>{\n                    const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                    return buttons[index] ? buttons[index].textContent : null;\n                }, i);\n                if (buttonText) {\n                    // (simple word matching)\n                    const answerWords = answer.toLowerCase().split(\" \");\n                    const buttonWords = buttonText.toLowerCase().split(\" \");\n                    let matchCount = 0;\n                    answerWords.forEach((word)=>{\n                        if (buttonWords.some((buttonWord)=>buttonWord.includes(word) || word.includes(buttonWord))) {\n                            matchCount++;\n                        }\n                    });\n                    const score = matchCount / Math.max(answerWords.length, buttonWords.length);\n                    if (score > bestMatchScore) {\n                        bestMatchScore = score;\n                        bestMatch = i;\n                    }\n                }\n            } catch (error) {\n                console.log(`Error calculating match for button ${i + 1}:`, error.message);\n            }\n        }\n        if (bestMatch >= 0 && bestMatchScore > 0.3) {\n            // Try  clicking shit for the best match\n            let clicked = false;\n            let buttonText = \"\";\n            try {\n                const result = await page.evaluate((index)=>{\n                    const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                    if (buttons[index]) {\n                        try {\n                            buttons[index].click();\n                            return {\n                                success: true,\n                                text: buttons[index].textContent\n                            };\n                        } catch (e) {\n                            // Force click if normal click fails\n                            const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*=\"enter-down\"]');\n                            interceptors.forEach((el)=>el.style.pointerEvents = \"none\");\n                            const event = new MouseEvent(\"click\", {\n                                bubbles: true,\n                                cancelable: true\n                            });\n                            buttons[index].dispatchEvent(event);\n                            setTimeout(()=>{\n                                interceptors.forEach((el)=>el.style.pointerEvents = \"\");\n                            }, 100);\n                            return {\n                                success: true,\n                                text: buttons[index].textContent\n                            };\n                        }\n                    }\n                    return {\n                        success: false,\n                        text: null\n                    };\n                }, bestMatch);\n                clicked = result.success;\n                buttonText = result.text;\n            } catch (error) {\n                console.log(`Error clicking best match button:`, error.message);\n            }\n            if (clicked) {\n                console.log(`Selected best match button ${bestMatch + 1}: ${buttonText} (score: ${bestMatchScore})`);\n                return;\n            }\n        }\n        // If no exact match found, try the first button as fallback\n        if (buttonCount > 0) {\n            console.log(\"No exact match found, clicking first button as fallback\");\n            let clicked = false;\n            let buttonText = \"\";\n            try {\n                const result = await page.evaluate(()=>{\n                    const buttons = document.querySelectorAll(\"#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button\");\n                    if (buttons[0]) {\n                        try {\n                            buttons[0].click();\n                            return {\n                                success: true,\n                                text: buttons[0].textContent\n                            };\n                        } catch (e) {\n                            // Force click if normal click fails\n                            const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*=\"enter-down\"]');\n                            interceptors.forEach((el)=>el.style.pointerEvents = \"none\");\n                            const event = new MouseEvent(\"click\", {\n                                bubbles: true,\n                                cancelable: true\n                            });\n                            buttons[0].dispatchEvent(event);\n                            setTimeout(()=>{\n                                interceptors.forEach((el)=>el.style.pointerEvents = \"\");\n                            }, 100);\n                            return {\n                                success: true,\n                                text: buttons[0].textContent\n                            };\n                        }\n                    }\n                    return {\n                        success: false,\n                        text: null\n                    };\n                });\n                clicked = result.success;\n                buttonText = result.text;\n            } catch (error) {\n                console.log(`Error clicking fallback button:`, error.message);\n            }\n            if (clicked) {\n                console.log(`Selected first button: ${buttonText}`);\n            }\n        } else {\n            console.log(\"Could not find any answer buttons\");\n        }\n    } catch (error) {\n        console.error(\"Error selecting answer:\", error);\n    }\n}\n// Function to check SRP earned and restart the flow if needed\nasync function checkSrpAndRestart(page, initialUserTotalSrp) {\n    try {\n        console.log(\"Checking SRP earned and determining next steps...\");\n        // Wait a moment for any final updates\n        await page.waitForTimeout(2000);\n        // Get current user total SRP\n        const currentUserTotalSrp = await page.evaluate(()=>{\n            const userTotalSrpElement = document.querySelector(\".sr_92b39de6\");\n            return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\\d,]/g, \"\").replace(\",\", \"\") : null;\n        }).catch(()=>null);\n        console.log(`Initial User Total SRP: ${initialUserTotalSrp}`);\n        console.log(`Current User Total SRP: ${currentUserTotalSrp}`);\n        // Calculate SRP earned\n        let srpEarned = 0;\n        if (initialUserTotalSrp && currentUserTotalSrp) {\n            const initialNum = parseInt(initialUserTotalSrp) || 0;\n            const currentNum = parseInt(currentUserTotalSrp) || 0;\n            srpEarned = currentNum - initialNum;\n        }\n        console.log(`SRP Earned: ${srpEarned}`);\n        // Update real-time SRP data\n        (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n            srpEarned: srpEarned\n        });\n        // Get the session SRP info\n        const srpInfo = global.sessionSrpInfo || {};\n        const initialSrp = parseInt(srpInfo.initialUserTotalSrp) || 0;\n        const targetSrp = parseInt(srpInfo.targetSrpNeeded) || 0;\n        const currentSrp = parseInt(currentUserTotalSrp) || 0;\n        const totalSrpEarned = currentSrp - initialSrp;\n        // Check if SRP target is reached\n        if (targetSrp > 0 && totalSrpEarned >= targetSrp) {\n            console.log(`🎯 SRP target reached! Earned ${totalSrpEarned} SRP (target was ${targetSrp})`);\n            (0,_realtime_route_js__WEBPACK_IMPORTED_MODULE_3__.updateRealtimeData)({\n                isRunning: false,\n                status: \"target_reached\",\n                srpEarned: totalSrpEarned\n            });\n            // Close browser after a short delay\n            setTimeout(async ()=>{\n                try {\n                    const { getGlobalBrowser } = __webpack_require__(Object(function webpackMissingModule() { var e = new Error(\"Cannot find module '../../../lib/browserManager'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));\n                    const browser = getGlobalBrowser();\n                    if (browser) {\n                        console.log(\"Auto-closing browser - SRP target reached\");\n                        await browser.close();\n                    }\n                } catch (error) {\n                    console.error(\"Error auto-closing browser:\", error);\n                }\n            }, 3000);\n            return; // Exit early, don't restart\n        }\n        console.log(`Initial User Total SRP: ${initialSrp}`);\n        console.log(`Current User Total SRP: ${currentSrp}`);\n        console.log(`SRP Earned This Session: ${totalSrpEarned}`);\n        console.log(`Target SRP: ${targetSrp}`);\n        // Update global session tracking\n        global.sessionSrpGoal = totalSrpEarned;\n        if (targetSrp > 0 && totalSrpEarned < targetSrp) {\n            console.log(`Need more SRP (${totalSrpEarned}/${targetSrp}). Continuing with current book...`);\n            // Extract story content again from current page\n            const storyContent = await page.evaluate(()=>{\n                const fullText = document.body.innerText;\n                const startMarker = \"Start reading here\";\n                const endMarker = \"Stop reading here\";\n                const startIndex = fullText.indexOf(startMarker);\n                const endIndex = fullText.indexOf(endMarker);\n                if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {\n                    return fullText.substring(startIndex + startMarker.length, endIndex).trim();\n                } else if (startIndex !== -1) {\n                    return fullText.substring(startIndex + startMarker.length).trim();\n                } else {\n                    return fullText;\n                }\n            });\n            global.sessionStoryContent = storyContent;\n            // Continue directly with question flow\n            await continueQuestionFlow(page, initialUserTotalSrp);\n        } else {\n            console.log(`Target SRP reached! (${totalSrpEarned}/${targetSrp}). Session complete.`);\n        }\n    } catch (error) {\n        console.error(\"Error checking SRP and restarting:\", error);\n    }\n}\n// Function to restart the book selection flow\nasync function restartBookFlow(page) {\n    try {\n        console.log(\"Restarting book selection flow...\");\n        // Navigate back to the library/book selection page\n        // Try to find and click a back/home button\n        const backSelectors = [\n            'button:has-text(\"Back\")',\n            'button:has-text(\"Home\")',\n            'button:has-text(\"Library\")',\n            'a:has-text(\"Back\")',\n            'a:has-text(\"Home\")',\n            'a:has-text(\"Library\")',\n            '[data-test-id*=\"back\"]',\n            '[data-test-id*=\"home\"]'\n        ];\n        let backClicked = false;\n        for (const selector of backSelectors){\n            try {\n                await page.click(selector, {\n                    timeout: 2000\n                });\n                console.log(`Clicked back button with selector: ${selector}`);\n                backClicked = true;\n                break;\n            } catch (error) {\n            // Continue to next selector\n            }\n        }\n        if (!backClicked) {\n            console.log(\"No back button found, navigating to library URL...\");\n            await page.goto(\"https://www.sparxreader.com/library\", {\n                waitUntil: \"networkidle\"\n            });\n        }\n        // Wait for the page to load\n        await page.waitForTimeout(3000);\n        // Look for a new book \n        console.log(\"Looking for next book...\");\n        // Extract new book info\n        const newBookInfo = await page.evaluate(()=>{\n            const titleElement = document.querySelector(\"div.sr_ea851119\");\n            const srpNeededElement = document.querySelector(\"#root > div > div:nth-child(2) > div > div > div:nth-child(2) > div > div:nth-child(2) > div:nth-child(1) > div > div > div:nth-child(2) > div > div:nth-child(2)\");\n            return {\n                bookTitle: titleElement ? titleElement.textContent.trim() : null,\n                srpNeeded: srpNeededElement ? srpNeededElement.textContent.trim() : null\n            };\n        }).catch(()=>({\n                bookTitle: null,\n                srpNeeded: null\n            }));\n        if (newBookInfo.bookTitle) {\n            console.log(`Found new book: ${newBookInfo.bookTitle}`);\n            console.log(`SRP Needed: ${newBookInfo.srpNeeded}`);\n            // Update global session info\n            global.sessionSrpInfo = newBookInfo;\n            // Auto-confirm this book and continue \n            console.log(\"Auto-confirming new book and continuing...\");\n            // Extract story content and continue the flow\n            await page.waitForTimeout(1000);\n            const storyContent = await page.evaluate(()=>{\n                const fullText = document.body.innerText;\n                const startMarker = \"Start reading here\";\n                const endMarker = \"Stop reading here\";\n                const startIndex = fullText.indexOf(startMarker);\n                const endIndex = fullText.indexOf(endMarker);\n                if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {\n                    return fullText.substring(startIndex + startMarker.length, endIndex).trim();\n                } else if (startIndex !== -1) {\n                    return fullText.substring(startIndex + startMarker.length).trim();\n                } else {\n                    return fullText;\n                }\n            });\n            // Store new story content\n            global.sessionStoryContent = storyContent;\n            console.log(`New story content extracted (${storyContent.length} characters)`);\n            // Get new initial SRP count\n            const newInitialSrp = await page.evaluate(()=>{\n                const totalSrpElement = document.querySelector(\"#header-portal > div:nth-child(3) > div:nth-child(1) > div\");\n                return totalSrpElement ? totalSrpElement.textContent.trim() : null;\n            }).catch(()=>null);\n            // Continue with the question flow\n            await continueQuestionFlow(page, newInitialSrp);\n        } else {\n            console.log(\"No new book found. Session may be complete or there might be an issue.\");\n        }\n    } catch (error) {\n        console.error(\"Error restarting book flow:\", error);\n    }\n}\n// Function to continue with the question flow for a new book\nasync function continueQuestionFlow(page, initialUserTotalSrp) {\n    try {\n        console.log(\"Continuing with question flow for new book...\");\n        // Click \"I have read up to here\" button and continue the flow\n        const readUpToHereSelectors = [\n            \"#book-scroll div:nth-child(2) div:nth-child(3) div div div button\",\n            'button:has-text(\"I have read up to here\")',\n            'button:has-text(\"read up to here\")',\n            '[data-test-id*=\"read-up-to-here\"]',\n            'button[class*=\"read\"]'\n        ];\n        let readUpToHereClicked = false;\n        for (const selector of readUpToHereSelectors){\n            try {\n                await page.click(selector, {\n                    timeout: 3000\n                });\n                console.log(`Clicked \"I have read up to here\" button with selector: ${selector}`);\n                readUpToHereClicked = true;\n                break;\n            } catch (error) {\n            // Continue to next selector\n            }\n        }\n        if (readUpToHereClicked) {\n            await page.waitForTimeout(2000);\n            // Check for dialog and handle accordingly\n            const pageText = await page.textContent(\"body\");\n            if (pageText.includes(\"Did you read carefully?\")) {\n                console.log('Found \"Did you read carefully?\" dialog');\n                const yesSelectors = [\n                    'xpath=//*[@id=\"book-scroll\"]/div/div/div/button[2]',\n                    'button:has-text(\"Yes, ask me the questions\")',\n                    \"#book-scroll div div div button:nth-child(2)\",\n                    'button:has-text(\"Yes\") >> nth=1' // General Yes button fallback\n                ];\n                let yesClicked = false;\n                for (const selector of yesSelectors){\n                    try {\n                        await page.click(selector, {\n                            timeout: 5000\n                        });\n                        console.log(`Clicked \"Yes\" button with selector: ${selector}`);\n                        yesClicked = true;\n                        break;\n                    } catch (error) {\n                        console.log(`Failed to click with selector ${selector}:`, error.message);\n                    }\n                }\n                if (!yesClicked) {\n                    throw new Error('Could not find \"Yes, ask me the questions\" button');\n                }\n                console.log('Clicked \"Yes, ask me the questions\" button');\n                await page.waitForTimeout(3000);\n                await solveQuestions(page, initialUserTotalSrp);\n            } else {\n                // Look for direct \"Start\" button\n                const startSelectors = [\n                    \"#book-scroll div div div button\",\n                    'button:has-text(\"Start\")',\n                    'button:has-text(\"start\")',\n                    '[data-test-id*=\"start\"]',\n                    'button[class*=\"start\"]'\n                ];\n                for (const selector of startSelectors){\n                    try {\n                        await page.click(selector, {\n                            timeout: 3000\n                        });\n                        console.log(`Clicked \"Start\" button with selector: ${selector}`);\n                        await page.waitForTimeout(3000);\n                        await solveQuestions(page, initialUserTotalSrp);\n                        break;\n                    } catch (error) {\n                    // Continue to next selector\n                    }\n                }\n            }\n        }\n    } catch (error) {\n        console.error(\"Error continuing question flow:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/navigate/route.js\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/realtime/route.js":
/*!***********************************************!*\
  !*** ./app/api/sparxreader/realtime/route.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   updateRealtimeData: () => (/* binding */ updateRealtimeData)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n// Global state for real-time updates\nglobal.realtimeData = global.realtimeData || {\n    isRunning: false,\n    currentQuestion: \"\",\n    currentAnswer: \"\",\n    questionNumber: 0,\n    srpEarned: 0,\n    questionHistory: [],\n    progress: 0,\n    status: \"idle\"\n};\nasync function GET(request) {\n    try {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: global.realtimeData\n        });\n    } catch (error) {\n        console.error(\"Error getting realtime data:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const updates = await request.json();\n        // Update global state\n        global.realtimeData = {\n            ...global.realtimeData,\n            ...updates,\n            lastUpdated: new Date().toISOString()\n        };\n        console.log(\"Realtime data updated:\", global.realtimeData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: global.realtimeData\n        });\n    } catch (error) {\n        console.error(\"Error updating realtime data:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to update realtime data from other routes\nfunction updateRealtimeData(updates) {\n    global.realtimeData = {\n        ...global.realtimeData,\n        ...updates,\n        lastUpdated: new Date().toISOString()\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/realtime/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fnavigate%2Froute&page=%2Fapi%2Fsparxreader%2Fnavigate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fnavigate%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();