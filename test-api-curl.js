const { getDatabase } = require('./lib/database');
const { getAuthManager } = require('./lib/auth');
const { spawn } = require('child_process');

async function testApiWithCurl() {
  console.log('🌐 Testing API with curl...\n');
  
  try {
    const db = getDatabase();
    const auth = getAuthManager();

    // Find the test user
    const testUser = db.db.prepare(`
      SELECT * FROM users
      WHERE username LIKE 'queuetest_%'
      ORDER BY created_at DESC
      LIMIT 1
    `).get();

    if (!testUser) {
      console.log('❌ No test user found. Run test-queue-system.js first.');
      return;
    }

    console.log(`📋 Testing with user: ${testUser.username}`);

    // Create a session for the test user
    console.log('\n1. Creating test session...');
    const sessionData = auth.createSession(testUser, '127.0.0.1', 'test-agent');
    const token = sessionData.token;
    console.log(`   Session token created: ${token.substring(0, 20)}...`);
    
    // Test the queue status API with curl
    console.log('\n2. Testing /api/queue/status endpoint with curl...');
    
    return new Promise((resolve) => {
      const curl = spawn('curl', [
        '-X', 'GET',
        '-H', `Authorization: Bearer ${token}`,
        '-H', 'Content-Type: application/json',
        'http://localhost:3001/api/queue/status?detailed=true'
      ]);
      
      let output = '';
      let error = '';
      
      curl.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      curl.stderr.on('data', (data) => {
        error += data.toString();
      });
      
      curl.on('close', (code) => {
        if (code === 0) {
          try {
            const response = JSON.parse(output);
            console.log('   ✅ API call successful');
            
            console.log('   Full API Response:');
            console.log('  ', JSON.stringify(response, null, 2));

            if (response.license_features) {
              console.log('   License features found:');
              console.log(`   - scheduling_access: ${response.license_features.scheduling_access}`);
              console.log(`   - max_accounts_per_batch: ${response.license_features.max_accounts_per_batch}`);
              console.log(`   - priority_level: ${response.license_features.priority_level}`);
              console.log(`   - max_batches_per_day: ${response.license_features.max_batches_per_day}`);

              if (response.license_features.scheduling_access) {
                console.log('   ✅ scheduling_access is true - popup should NOT show');
              } else {
                console.log('   ❌ scheduling_access is false - popup WOULD show');
              }
            } else {
              console.log('   ❌ No license_features in response');
            }
          } catch (parseError) {
            console.log('   ❌ Failed to parse response:', parseError.message);
            console.log('   Raw response:', output);
          }
        } else {
          console.log(`   ❌ curl failed with code ${code}`);
          console.log('   Error:', error);
        }
        
        console.log('\n🎉 API test completed!');
        resolve();
      });
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testApiWithCurl();
}

module.exports = { testApiWithCurl };
