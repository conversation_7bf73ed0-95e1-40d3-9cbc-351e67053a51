import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getWebhookManager } from '../../../../lib/webhook';
import QueueMiddleware from '../../../../lib/queueMiddleware';

// Middleware wrapper for Next.js API routes
function withMiddleware(handler, middlewares) {
  return async (request, context) => {
    const req = {
      ...request,
      body: await request.json().catch(() => ({})),
      user: null,
      licenseFeatures: null,
      degradedMode: false
    };
    
    const res = {
      status: (code) => ({ json: (data) => NextResponse.json(data, { status: code }) }),
      json: (data) => NextResponse.json(data)
    };

    // Authenticate user first
    try {
      const db = getDatabase();
      const authHeader = request.headers.get('authorization');
      
      if (!authHeader?.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }
      
      const token = authHeader.substring(7);
      const session = db.validateSession(token);
      
      if (!session) {
        return NextResponse.json({ error: 'Invalid or expired session' }, { status: 401 });
      }
      
      req.user = {
        id: session.user_id,
        username: session.username,
        role: session.role
      };
    } catch (error) {
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }

    // Apply middlewares
    for (const middleware of middlewares) {
      try {
        let nextCalled = false;
        const next = () => { nextCalled = true; };
        
        const result = await middleware(req, res, next);
        
        if (result instanceof NextResponse) {
          return result;
        }
        
        if (!nextCalled) {
          return NextResponse.json({ error: 'Middleware blocked request' }, { status: 403 });
        }
      } catch (error) {
        console.error('Middleware error:', error);
        return NextResponse.json({ error: 'Request processing failed' }, { status: 500 });
      }
    }

    return handler(req, context);
  };
}

async function handleBatchSubmission(req, context) {
  try {
    const db = getDatabase();
    const webhook = getWebhookManager();
    const { batch_name, login_type, accounts, srp_target = 100, priority_override } = req.body;

    // Validate required fields
    if (!batch_name || !login_type || !accounts || !Array.isArray(accounts) || accounts.length === 0) {
      return NextResponse.json({
        error: 'Invalid request',
        details: 'batch_name, login_type, and accounts array are required'
      }, { status: 400 });
    }

    // Validate SRP target
    if (srp_target < 1 || srp_target > 400) {
      return NextResponse.json({
        error: 'Invalid SRP target',
        details: 'SRP target must be between 1 and 400'
      }, { status: 400 });
    }

    // Validate login type
    if (!['normal', 'google', 'microsoft'].includes(login_type)) {
      return NextResponse.json({
        error: 'Invalid login type',
        details: 'login_type must be one of: normal, google, microsoft'
      }, { status: 400 });
    }

    // Validate account data structure - only require school, allow flexible credentials
    const invalidAccounts = accounts.filter(account =>
      !account.school || (!account.email && !account.username)
    );

    if (invalidAccounts.length > 0) {
      return NextResponse.json({
        error: 'Invalid account data',
        details: 'Each account must have a school and either email or username'
      }, { status: 400 });
    }

    // Handle degraded mode
    if (req.degradedMode) {
      return NextResponse.json({
        error: 'Feature not available',
        reason: req.degradationReason,
        alternative: 'Please upgrade your license for batch processing'
      }, { status: 403 });
    }

    // Apply priority override if user has sufficient privileges
    let effectivePriority = req.licenseFeatures.priority_level;
    if (priority_override !== undefined && req.user.role === 'admin') {
      effectivePriority = Math.min(priority_override, 10);
      
      await webhook.sendPriorityAdjustment(
        null,
        req.licenseFeatures.priority_level,
        effectivePriority,
        'Admin override for batch submission',
        req.user.username
      );
    }

    // Create the batch - automatically queued (no scheduling)
    const batchId = db.createQueueBatch(
      req.user.id,
      batch_name,
      accounts,
      null, // No scheduled time - automatically queued
      login_type,
      srp_target
    );

    // Send webhook notification
    await webhook.sendBatchCreated(
      req.user.username,
      batch_name,
      accounts.length,
      null // No scheduled time
    );

    // Get batch details for response
    const batchDetails = db.getQueueBatches(req.user.id, null, 1, 0)[0];
    const jobs = db.getBatchJobs(batchId);

    return NextResponse.json({
      success: true,
      batch: {
        id: batchId,
        name: batch_name,
        total_accounts: accounts.length,
        status: batchDetails.status,
        priority_level: effectivePriority,
        scheduled_time: null, // Batches are automatically queued
        created_at: batchDetails.created_at,
        jobs: jobs.map(job => ({
          id: job.id,
          status: job.status,
          priority_level: job.priority_level,
          effective_priority: job.effective_priority
        }))
      },
      license_info: {
        max_accounts_per_batch: req.licenseFeatures.max_accounts_per_batch,
        priority_level: req.licenseFeatures.priority_level,
        scheduling_access: req.licenseFeatures.scheduling_access
      }
    });

  } catch (error) {
    console.error('Batch submission error:', error);
    
    // Send error webhook
    const webhook = getWebhookManager();
    await webhook.sendQueueAlert(
      'system_error',
      'Batch submission failed',
      {
        user: req.user?.username || 'unknown',
        error: error.message,
        batch_name: req.body?.batch_name || 'unknown'
      }
    );

    return NextResponse.json({
      error: 'Batch submission failed',
      details: error.message
    }, { status: 500 });
  }
}

// GET - Get user's batches
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const db = getDatabase();
      const url = new URL(request.url);
      const status = url.searchParams.get('status');
      const limit = parseInt(url.searchParams.get('limit')) || 20;
      const offset = parseInt(url.searchParams.get('offset')) || 0;

      const batches = db.getQueueBatches(req.user.id, status, limit, offset);
      
      // Get job details for each batch
      const batchesWithJobs = batches.map(batch => ({
        ...batch,
        jobs: db.getBatchJobs(batch.id)
      }));

      return NextResponse.json({
        batches: batchesWithJobs,
        pagination: {
          limit,
          offset,
          has_more: batches.length === limit
        }
      });

    } catch (error) {
      console.error('Get batches error:', error);
      return NextResponse.json({
        error: 'Failed to retrieve batches',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures,
    QueueMiddleware.logQueueActivity
  ]);

  return handler(request);
}

// POST - Create new batch
export async function POST(request) {
  const handler = withMiddleware(handleBatchSubmission, [
    QueueMiddleware.validateLicenseFeatures,
    QueueMiddleware.validateDailyBatchLimit,
    QueueMiddleware.validateBatchSize,
    QueueMiddleware.checkScheduleConflicts,
    QueueMiddleware.rateLimitByLicense,
    QueueMiddleware.logQueueActivity
  ]);

  return handler(request);
}

// PATCH - Update batch (cancel, modify priority, etc.)
export async function PATCH(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const db = getDatabase();
      const webhook = getWebhookManager();
      const { batch_id, action, priority_level } = req.body;

      if (!batch_id || !action) {
        return NextResponse.json({
          error: 'batch_id and action are required'
        }, { status: 400 });
      }

      // Verify batch ownership or admin privileges
      const batch = db.getQueueBatches(req.user.id, null, 1, 0).find(b => b.id === batch_id);
      if (!batch && req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Batch not found or access denied'
        }, { status: 404 });
      }

      let result;
      switch (action) {
        case 'cancel':
          result = db.updateBatchStatus(batch_id, 'cancelled');
          await webhook.sendQueueAlert(
            'info',
            'Batch cancelled',
            {
              batch_id,
              user: req.user.username,
              batch_name: batch.batch_name
            }
          );
          break;

        case 'update_priority':
          if (priority_level === undefined) {
            return NextResponse.json({
              error: 'priority_level is required for update_priority action'
            }, { status: 400 });
          }

          // Check if user can set this priority level
          if (priority_level > req.licenseFeatures.priority_level && req.user.role !== 'admin') {
            return NextResponse.json({
              error: 'Priority level exceeds license limit'
            }, { status: 403 });
          }

          // Update all jobs in the batch
          const jobs = db.getBatchJobs(batch_id);
          jobs.forEach(job => {
            db.updateJobPriority(job.id, priority_level, req.user.role === 'admin');
          });

          await webhook.sendPriorityAdjustment(
            batch_id,
            batch.priority_level,
            priority_level,
            'Batch priority updated',
            req.user.role === 'admin' ? req.user.username : null
          );
          break;

        default:
          return NextResponse.json({
            error: 'Invalid action',
            valid_actions: ['cancel', 'update_priority']
          }, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        action,
        batch_id,
        message: `Batch ${action} completed successfully`
      });

    } catch (error) {
      console.error('Batch update error:', error);
      return NextResponse.json({
        error: 'Batch update failed',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures,
    QueueMiddleware.logQueueActivity
  ]);

  return handler(request);
}