"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sparxreader/start/route";
exports.ids = ["app/api/sparxreader/start/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "playwright":
/*!*****************************!*\
  !*** external "playwright" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("playwright");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fstart%2Froute&page=%2Fapi%2Fsparxreader%2Fstart%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fstart%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fstart%2Froute&page=%2Fapi%2Fsparxreader%2Fstart%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fstart%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_sparxreader_start_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/sparxreader/start/route.js */ \"(rsc)/./app/api/sparxreader/start/route.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_reader_auto_main_app_api_sparxreader_start_route_js__WEBPACK_IMPORTED_MODULE_2__]);\nD_reader_auto_main_app_api_sparxreader_start_route_js__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sparxreader/start/route\",\n        pathname: \"/api/sparxreader/start\",\n        filename: \"route\",\n        bundlePath: \"app/api/sparxreader/start/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\sparxreader\\\\start\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_sparxreader_start_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/sparxreader/start/route\";\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fstart%2Froute&page=%2Fapi%2Fsparxreader%2Fstart%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fstart%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/browser-context.js":
/*!************************************************!*\
  !*** ./app/api/sparxreader/browser-context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearGlobalBrowser: () => (/* binding */ clearGlobalBrowser),\n/* harmony export */   getGlobalBrowser: () => (/* binding */ getGlobalBrowser),\n/* harmony export */   getGlobalPage: () => (/* binding */ getGlobalPage),\n/* harmony export */   setGlobalBrowser: () => (/* binding */ setGlobalBrowser)\n/* harmony export */ });\n// Global browser context storage using globalThis for persistence\nif (!globalThis.sparxBrowserContext) {\n    globalThis.sparxBrowserContext = {\n        browser: null,\n        page: null\n    };\n}\nfunction setGlobalBrowser(browser, page) {\n    globalThis.sparxBrowserContext.browser = browser;\n    globalThis.sparxBrowserContext.page = page;\n    console.log(\"Browser context set:\", !!browser, !!page);\n}\nfunction getGlobalBrowser() {\n    return globalThis.sparxBrowserContext.browser;\n}\nfunction getGlobalPage() {\n    return globalThis.sparxBrowserContext.page;\n}\nfunction clearGlobalBrowser() {\n    globalThis.sparxBrowserContext.browser = null;\n    globalThis.sparxBrowserContext.page = null;\n    console.log(\"Browser context cleared\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/browser-context.js\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/start/route.js":
/*!********************************************!*\
  !*** ./app/api/sparxreader/start/route.js ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   clearGlobalBrowser: () => (/* reexport safe */ _browser_context_js__WEBPACK_IMPORTED_MODULE_3__.clearGlobalBrowser),\n/* harmony export */   getGlobalBrowser: () => (/* reexport safe */ _browser_context_js__WEBPACK_IMPORTED_MODULE_3__.getGlobalBrowser),\n/* harmony export */   getGlobalPage: () => (/* reexport safe */ _browser_context_js__WEBPACK_IMPORTED_MODULE_3__.getGlobalPage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var playwright__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! playwright */ \"playwright\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _browser_context_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../browser-context.js */ \"(rsc)/./app/api/sparxreader/browser-context.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([playwright__WEBPACK_IMPORTED_MODULE_1__]);\nplaywright__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nasync function POST(request) {\n    try {\n        const { url, targetSrp, credentials } = await request.json();\n        const extensionPath = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), \"Sparxext reader\");\n        try {\n            // Always create new browser session for first try\n            const existingBrowser = (0,_browser_context_js__WEBPACK_IMPORTED_MODULE_3__.getGlobalBrowser)();\n            // Close any existing browser session\n            if (existingBrowser) {\n                try {\n                    await existingBrowser.close();\n                    console.log(\"Closed existing browser session\");\n                } catch (error) {\n                    console.log(\"Error closing existing browser:\", error.message);\n                }\n            }\n            // Create new browser session\n            console.log(\"Creating new browser session...\");\n            const browser = await playwright__WEBPACK_IMPORTED_MODULE_1__.chromium.launchPersistentContext(\"\", {\n                headless: false,\n                args: [\n                    `--disable-extensions-except=${extensionPath}`,\n                    `--load-extension=${extensionPath}`,\n                    \"--no-sandbox\",\n                    \"--disable-setuid-sandbox\"\n                ]\n            });\n            const page = await browser.newPage();\n            // Store globally for use in other endpoints\n            (0,_browser_context_js__WEBPACK_IMPORTED_MODULE_3__.setGlobalBrowser)(browser, page);\n            console.log(\"Navigating to Sparx Learning...\");\n            await page.goto(url, {\n                timeout: 15000\n            });\n            try {\n                await page.waitForLoadState(\"networkidle\", {\n                    timeout: 10000\n                });\n            } catch (loadError) {\n                console.log(\"Page load timeout, but continuing\");\n            }\n            // Login process with better error handling\n            try {\n                // Use school from credentials\n                if (!credentials || !credentials.school) {\n                    throw new Error(\"School name is required\");\n                }\n                await page.type('input[type=\"text\"], input[type=\"search\"], input', credentials.school);\n                await page.press('input[type=\"text\"], input[type=\"search\"], input', \"Enter\");\n                await page.waitForTimeout(1000);\n                try {\n                    await page.click('button:has-text(\"Continue\")', {\n                        timeout: 5000\n                    });\n                } catch (error) {\n                    console.log(\"Continue button not found, proceeding anyway\");\n                }\n                await page.waitForTimeout(2000);\n                try {\n                    await page.click(\"div#cookiescript_accept\", {\n                        timeout: 5000\n                    });\n                } catch (error) {\n                    console.log(\"Cookie accept button not found, proceeding anyway\");\n                }\n                await page.waitForTimeout(2000);\n                // Use credentials from request\n                if (!credentials || !credentials.email || !credentials.password) {\n                    throw new Error(\"Login credentials are required\");\n                }\n                await page.type('input.sm-input[name=\"username\"]', credentials.email);\n                await page.waitForTimeout(1000);\n                await page.type('input.sm-input[name=\"password\"]', credentials.password);\n                await page.waitForTimeout(1000);\n                await page.click('button.sm-button.login-button[type=\"submit\"]');\n                await page.waitForTimeout(3000);\n            } catch (loginError) {\n                console.log(\"Login process error:\", loginError.message);\n            // Continue anyway as user might already be logged in\n            }\n            await page.waitForTimeout(3000);\n            // COMPLETELY REWRITTEN APPROACH - EXTRACT SRP AND TITLE IMMEDIATELY\n            // First, navigate to the library page\n            console.log(\"Navigating to Sparx Reader library page...\");\n            try {\n                // Direct navigation to library page - most reliable approach\n                await page.goto(\"https://reader.sparx-learning.com/library\", {\n                    timeout: 15000,\n                    waitUntil: \"domcontentloaded\"\n                });\n                console.log(\"Navigation to library page initiated\");\n            } catch (navError) {\n                console.log(\"Direct navigation to library page failed, trying alternative method\");\n                try {\n                    await page.click('a[href=\"https://reader.sparx-learning.com\"]', {\n                        timeout: 5000\n                    });\n                } catch (clickError) {\n                    console.log(\"Both navigation methods failed, but continuing anyway\");\n                }\n            }\n            // Extract only the user's total SRP - we'll ask user for their target\n            console.log(\"Extracting user total SRP...\");\n            let userTotalSrp = null;\n            // Try to get user total SRP with multiple attempts\n            for(let attempt = 0; attempt < 5; attempt++){\n                try {\n                    userTotalSrp = await page.evaluate(()=>{\n                        // Get the user's total SRP\n                        const userTotalSrpElement = document.querySelector(\".sr_92b39de6\");\n                        return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\\d,]/g, \"\").replace(\",\", \"\") : null;\n                    });\n                    if (userTotalSrp) {\n                        console.log(`User Total SRP extracted on attempt ${attempt + 1}: ${userTotalSrp}`);\n                        break;\n                    }\n                    // If we didn't get the info, wait a short time and try again\n                    if (attempt < 4) {\n                        await page.waitForTimeout(200);\n                    }\n                } catch (error) {\n                    console.log(`SRP extraction attempt ${attempt + 1} failed:`, error.message);\n                    if (attempt < 4) {\n                        await page.waitForTimeout(200);\n                    }\n                }\n            }\n            // Store initial SRP info globally and in browser localStorage\n            global.sessionSrpInfo = {\n                initialUserTotalSrp: userTotalSrp,\n                targetSrpNeeded: targetSrp || null\n            };\n            // Store target SRP and initial SRP in browser localStorage so extension can access it\n            if (targetSrp) {\n                await page.evaluate(({ target, initial })=>{\n                    localStorage.setItem(\"targetSrp\", target.toString());\n                    localStorage.setItem(\"initialSrp\", initial || \"0\");\n                }, {\n                    target: targetSrp,\n                    initial: userTotalSrp\n                });\n                console.log(`Target SRP stored in localStorage: ${targetSrp}`);\n                console.log(`Initial SRP stored in localStorage: ${userTotalSrp}`);\n            }\n            // Book title will be extracted later when we're actually in the book\n            let bookTitle = \"Book Found\";\n            // Take a screenshot of the library page with shorter timeout\n            const screenshotPath = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), \"public\", \"screenshot.png\");\n            try {\n                await page.screenshot({\n                    path: screenshotPath,\n                    timeout: 10000\n                });\n                console.log(\"Screenshot taken successfully\");\n            } catch (screenshotError) {\n                console.log(\"Screenshot failed, continuing without it:\", screenshotError.message);\n            }\n            // Return the response with the book title and current SRP for user confirmation\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: \"Successfully logged in and extracted book title\",\n                bookTitle: bookTitle || \"No book found\",\n                currentSrp: userTotalSrp || \"Unknown\",\n                targetSrp: targetSrp || null,\n                screenshot: \"/screenshot.png\"\n            });\n        } catch (playwrightError) {\n            if (playwrightError.message.includes(\"Executable doesn't exist\")) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"Playwright browsers not installed. Please run 'npx playwright install chromium' in your terminal.\",\n                    needsPlaywright: true\n                }, {\n                    status: 500\n                });\n            } else {\n                throw playwrightError;\n            }\n        }\n    } catch (error) {\n        console.error(\"Error opening Sparx Reader with extension:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Re-export functions for backward compatibility\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/start/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fstart%2Froute&page=%2Fapi%2Fsparxreader%2Fstart%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fstart%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();