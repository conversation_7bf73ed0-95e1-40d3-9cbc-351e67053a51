"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/queue/batch/route";
exports.ids = ["app/api/queue/batch/route"];
exports.modules = {

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fbatch%2Froute&page=%2Fapi%2Fqueue%2Fbatch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fbatch%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fbatch%2Froute&page=%2Fapi%2Fqueue%2Fbatch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fbatch%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_queue_batch_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/queue/batch/route.js */ \"(rsc)/./app/api/queue/batch/route.js\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/queue/batch/route\",\n        pathname: \"/api/queue/batch\",\n        filename: \"route\",\n        bundlePath: \"app/api/queue/batch/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\queue\\\\batch\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_queue_batch_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/queue/batch/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fbatch%2Froute&page=%2Fapi%2Fqueue%2Fbatch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fbatch%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/queue/batch/route.js":
/*!**************************************!*\
  !*** ./app/api/queue/batch/route.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/database */ \"(rsc)/./lib/database.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_lib_database__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_webhook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/webhook */ \"(rsc)/./lib/webhook.js\");\n/* harmony import */ var _lib_webhook__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lib_webhook__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./lib/auth.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_lib_auth__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../lib/queueMiddleware */ \"(rsc)/./lib/queueMiddleware.js\");\n/* harmony import */ var _lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Middleware wrapper for Next.js API routes\nfunction withMiddleware(handler, middlewares) {\n    return async (request, context)=>{\n        const req = {\n            ...request,\n            body: await request.json().catch(()=>({})),\n            user: null,\n            licenseFeatures: null,\n            degradedMode: false\n        };\n        const res = {\n            status: (code)=>({\n                    json: (data)=>next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n                            status: code\n                        })\n                }),\n            json: (data)=>next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data)\n        };\n        // Authenticate user first\n        try {\n            const auth = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthManager)();\n            const authHeader = request.headers.get(\"authorization\");\n            if (!authHeader?.startsWith(\"Bearer \")) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            const token = authHeader.substring(7);\n            const session = auth.validateSession(token);\n            if (!session) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Invalid or expired session\"\n                }, {\n                    status: 401\n                });\n            }\n            req.user = {\n                id: session.userId,\n                username: session.username,\n                role: session.role\n            };\n        } catch (error) {\n            console.error(\"❌ Batch API - Authentication error:\", error.message);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Authentication failed\"\n            }, {\n                status: 401\n            });\n        }\n        // Apply middlewares\n        for (const middleware of middlewares){\n            try {\n                let nextCalled = false;\n                const next = ()=>{\n                    nextCalled = true;\n                };\n                const result = await middleware(req, res, next);\n                if (result instanceof next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n                    return result;\n                }\n                if (!nextCalled) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Middleware blocked request\"\n                    }, {\n                        status: 403\n                    });\n                }\n            } catch (error) {\n                console.error(\"Middleware error:\", error);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Request processing failed\"\n                }, {\n                    status: 500\n                });\n            }\n        }\n        return handler(req, context);\n    };\n}\nasync function handleBatchSubmission(req, context) {\n    try {\n        const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n        const { batch_name, login_type, accounts, srp_target = 100, priority_override } = req.body;\n        // Validate required fields\n        if (!batch_name || !login_type || !accounts || !Array.isArray(accounts) || accounts.length === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid request\",\n                details: \"batch_name, login_type, and accounts array are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate SRP target\n        if (srp_target < 1 || srp_target > 400) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid SRP target\",\n                details: \"SRP target must be between 1 and 400\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate login type\n        if (![\n            \"normal\",\n            \"google\",\n            \"microsoft\"\n        ].includes(login_type)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid login type\",\n                details: \"login_type must be one of: normal, google, microsoft\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate account data structure - only require school, allow flexible credentials\n        const invalidAccounts = accounts.filter((account)=>!account.school || !account.email && !account.username);\n        if (invalidAccounts.length > 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid account data\",\n                details: \"Each account must have a school and either email or username\"\n            }, {\n                status: 400\n            });\n        }\n        // Handle degraded mode\n        if (req.degradedMode) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Feature not available\",\n                reason: req.degradationReason,\n                alternative: \"Please upgrade your license for batch processing\"\n            }, {\n                status: 403\n            });\n        }\n        // Apply priority override if user has sufficient privileges\n        let effectivePriority = req.licenseFeatures.priority_level;\n        if (priority_override !== undefined && req.user.role === \"admin\") {\n            effectivePriority = Math.min(priority_override, 10);\n            await webhook.sendPriorityAdjustment(null, req.licenseFeatures.priority_level, effectivePriority, \"Admin override for batch submission\", req.user.username);\n        }\n        // Create the batch - automatically queued (no scheduling)\n        const batchId = db.createQueueBatch(req.user.id, batch_name, accounts, null, login_type, srp_target);\n        // Send webhook notification\n        await webhook.sendBatchCreated(req.user.username, batch_name, accounts.length, null // No scheduled time\n        );\n        // Get batch details for response\n        const batchDetails = db.getQueueBatches(req.user.id, null, 1, 0)[0];\n        const jobs = db.getBatchJobs(batchId);\n        console.log(`✅ Batch ${batchId} created successfully with ${accounts.length} jobs`);\n        console.log(\"\\uD83D\\uDCCB Jobs are now queued and will be processed by the queue processor\");\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            batch: {\n                id: batchId,\n                name: batch_name,\n                total_accounts: accounts.length,\n                status: batchDetails.status,\n                priority_level: effectivePriority,\n                scheduled_time: null,\n                created_at: batchDetails.created_at,\n                jobs: jobs.map((job)=>({\n                        id: job.id,\n                        status: job.status,\n                        priority_level: job.priority_level,\n                        effective_priority: job.effective_priority\n                    }))\n            },\n            license_info: {\n                max_accounts_per_batch: req.licenseFeatures.max_accounts_per_batch,\n                priority_level: req.licenseFeatures.priority_level,\n                scheduling_access: req.licenseFeatures.scheduling_access\n            }\n        });\n    } catch (error) {\n        console.error(\"Batch submission error:\", error);\n        // Send error webhook\n        const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n        await webhook.sendQueueAlert(\"system_error\", \"Batch submission failed\", {\n            user: req.user?.username || \"unknown\",\n            error: error.message,\n            batch_name: req.body?.batch_name || \"unknown\"\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Batch submission failed\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - Get user's batches\nasync function GET(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const url = new URL(request.url);\n            const status = url.searchParams.get(\"status\");\n            const limit = parseInt(url.searchParams.get(\"limit\")) || 20;\n            const offset = parseInt(url.searchParams.get(\"offset\")) || 0;\n            const batches = db.getQueueBatches(req.user.id, status, limit, offset);\n            // Get job details for each batch\n            const batchesWithJobs = batches.map((batch)=>({\n                    ...batch,\n                    jobs: db.getBatchJobs(batch.id)\n                }));\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                batches: batchesWithJobs,\n                pagination: {\n                    limit,\n                    offset,\n                    has_more: batches.length === limit\n                }\n            });\n        } catch (error) {\n            console.error(\"Get batches error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to retrieve batches\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().validateLicenseFeatures),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().logQueueActivity)\n    ]);\n    return handler(request);\n}\n// POST - Create new batch\nasync function POST(request) {\n    const handler = withMiddleware(handleBatchSubmission, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().validateLicenseFeatures),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().validateDailyBatchLimit),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().validateBatchSize),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().checkScheduleConflicts),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().rateLimitByLicense),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().logQueueActivity)\n    ]);\n    return handler(request);\n}\n// PATCH - Update batch (cancel, modify priority, etc.)\nasync function PATCH(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n            const { batch_id, action, priority_level } = req.body;\n            if (!batch_id || !action) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"batch_id and action are required\"\n                }, {\n                    status: 400\n                });\n            }\n            // Verify batch ownership or admin privileges\n            const batch = db.getQueueBatches(req.user.id, null, 1, 0).find((b)=>b.id === batch_id);\n            if (!batch && req.user.role !== \"admin\") {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Batch not found or access denied\"\n                }, {\n                    status: 404\n                });\n            }\n            let result;\n            switch(action){\n                case \"cancel\":\n                    result = db.updateBatchStatus(batch_id, \"cancelled\");\n                    await webhook.sendQueueAlert(\"info\", \"Batch cancelled\", {\n                        batch_id,\n                        user: req.user.username,\n                        batch_name: batch.batch_name\n                    });\n                    break;\n                case \"update_priority\":\n                    if (priority_level === undefined) {\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            error: \"priority_level is required for update_priority action\"\n                        }, {\n                            status: 400\n                        });\n                    }\n                    // Check if user can set this priority level\n                    if (priority_level > req.licenseFeatures.priority_level && req.user.role !== \"admin\") {\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            error: \"Priority level exceeds license limit\"\n                        }, {\n                            status: 403\n                        });\n                    }\n                    // Update all jobs in the batch\n                    const jobs = db.getBatchJobs(batch_id);\n                    jobs.forEach((job)=>{\n                        db.updateJobPriority(job.id, priority_level, req.user.role === \"admin\");\n                    });\n                    await webhook.sendPriorityAdjustment(batch_id, batch.priority_level, priority_level, \"Batch priority updated\", req.user.role === \"admin\" ? req.user.username : null);\n                    break;\n                default:\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Invalid action\",\n                        valid_actions: [\n                            \"cancel\",\n                            \"update_priority\"\n                        ]\n                    }, {\n                        status: 400\n                    });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                action,\n                batch_id,\n                message: `Batch ${action} completed successfully`\n            });\n        } catch (error) {\n            console.error(\"Batch update error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Batch update failed\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().validateLicenseFeatures),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().logQueueActivity)\n    ]);\n    return handler(request);\n}\n// DELETE - Delete batch (only if not currently processing)\nasync function DELETE(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n            const url = new URL(request.url);\n            const batchId = url.searchParams.get(\"batch_id\");\n            if (!batchId) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"batch_id parameter is required\"\n                }, {\n                    status: 400\n                });\n            }\n            // Verify batch ownership or admin privileges\n            const batch = db.getQueueBatches(req.user.id, null, 1, 0).find((b)=>b.id === parseInt(batchId));\n            if (!batch && req.user.role !== \"admin\") {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Batch not found or access denied\"\n                }, {\n                    status: 404\n                });\n            }\n            // Check if batch is currently processing\n            if (batch.status === \"processing\") {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Cannot delete batch that is currently being processed\",\n                    details: \"Please wait for the batch to complete or cancel it first\"\n                }, {\n                    status: 409\n                });\n            }\n            // Get all jobs in the batch to check their status\n            const batchJobs = db.getBatchJobs(parseInt(batchId));\n            const processingJobs = batchJobs.filter((job)=>job.status === \"processing\");\n            if (processingJobs.length > 0) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Cannot delete batch with jobs currently being processed\",\n                    details: `${processingJobs.length} job(s) are currently processing`\n                }, {\n                    status: 409\n                });\n            }\n            // Delete all jobs in the batch first (due to foreign key constraints)\n            const deleteJobsStmt = db.db.prepare(\"DELETE FROM queue_jobs WHERE batch_id = ?\");\n            const jobsResult = deleteJobsStmt.run(parseInt(batchId));\n            // Delete the batch\n            const deleteBatchStmt = db.db.prepare(\"DELETE FROM queue_batches WHERE id = ?\");\n            const batchResult = deleteBatchStmt.run(parseInt(batchId));\n            if (batchResult.changes === 0) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Batch not found or already deleted\"\n                }, {\n                    status: 404\n                });\n            }\n            // Log activity\n            db.logActivity(req.user.id, \"BATCH_DELETED\", `Deleted batch \"${batch.batch_name}\" with ${jobsResult.changes} jobs`);\n            // Send webhook notification\n            await webhook.sendQueueAlert(\"info\", \"Batch deleted\", {\n                batch_id: parseInt(batchId),\n                user: req.user.username,\n                batch_name: batch.batch_name,\n                jobs_deleted: jobsResult.changes\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: `Batch \"${batch.batch_name}\" deleted successfully`,\n                jobs_deleted: jobsResult.changes\n            });\n        } catch (error) {\n            console.error(\"Batch deletion error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Batch deletion failed\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().validateLicenseFeatures),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_4___default().logQueueActivity)\n    ]);\n    return handler(request);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/queue/batch/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-this-in-production\";\nconst TOKEN_EXPIRY = \"7d\"; // 7 days\nclass AuthManager {\n    constructor(){\n        this.db = getDatabase();\n    }\n    // Generate JWT token\n    generateToken(user) {\n        const payload = {\n            userId: user.id,\n            username: user.username,\n            role: user.role,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jwt.sign(payload, JWT_SECRET, {\n            expiresIn: TOKEN_EXPIRY\n        });\n    }\n    // Verify JWT token\n    verifyToken(token) {\n        try {\n            return jwt.verify(token, JWT_SECRET);\n        } catch (error) {\n            throw new Error(\"Invalid token\");\n        }\n    }\n    // Hash token for database storage\n    hashToken(token) {\n        return crypto.createHash(\"sha256\").update(token).digest(\"hex\");\n    }\n    // Create session with token\n    createSession(user, ipAddress = null, userAgent = null) {\n        const token = this.generateToken(user);\n        const tokenHash = this.hashToken(token);\n        // Calculate expiry date\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days\n        // Store session in database\n        this.db.createSession(user.id, tokenHash, expiresAt.toISOString(), ipAddress, userAgent);\n        // Log activity\n        this.db.logActivity(user.id, \"LOGIN\", `User logged in from ${ipAddress}`, ipAddress);\n        return {\n            token,\n            user: {\n                id: user.id,\n                username: user.username,\n                role: user.role,\n                lastLogin: user.last_login\n            },\n            expiresAt: expiresAt.toISOString()\n        };\n    }\n    // Validate session\n    validateSession(token) {\n        try {\n            // First verify JWT\n            const decoded = this.verifyToken(token);\n            // Then check database session\n            const tokenHash = this.hashToken(token);\n            const session = this.db.validateSession(tokenHash);\n            if (!session || !session.user_active) {\n                throw new Error(\"Session invalid or user inactive\");\n            }\n            return {\n                userId: session.user_id,\n                username: session.username,\n                role: session.role,\n                sessionId: session.id\n            };\n        } catch (error) {\n            throw new Error(\"Invalid session\");\n        }\n    }\n    // Logout user\n    logout(token, userId = null) {\n        const tokenHash = this.hashToken(token);\n        this.db.invalidateSession(tokenHash);\n        if (userId) {\n            this.db.logActivity(userId, \"LOGOUT\", \"User logged out\");\n        }\n    }\n    // Logout all sessions for user\n    logoutAllSessions(userId) {\n        this.db.invalidateAllUserSessions(userId);\n        this.db.logActivity(userId, \"LOGOUT_ALL\", \"All sessions invalidated\");\n    }\n    // Middleware for protecting routes\n    requireAuth(requiredRole = null) {\n        return async (req, res, next)=>{\n            try {\n                const authHeader = req.headers.authorization;\n                if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n                    return res.status(401).json({\n                        error: \"No token provided\"\n                    });\n                }\n                const token = authHeader.substring(7);\n                const session = await this.validateSession(token);\n                // Check role if required\n                if (requiredRole && session.role !== requiredRole) {\n                    return res.status(403).json({\n                        error: \"Insufficient permissions\"\n                    });\n                }\n                // Add user info to request\n                req.user = session;\n                req.token = token;\n                next();\n            } catch (error) {\n                return res.status(401).json({\n                    error: error.message\n                });\n            }\n        };\n    }\n    // Admin only middleware\n    requireAdmin() {\n        return this.requireAuth(\"admin\");\n    }\n    // Extract IP address from request (Next.js compatible)\n    getClientIP(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || req.ip || \"127.0.0.1\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"x-forwarded-for\"] || req.connection?.remoteAddress || req.socket?.remoteAddress || (req.connection?.socket ? req.connection.socket.remoteAddress : null) || \"127.0.0.1\";\n    }\n    // Extract user agent (Next.js compatible)\n    getUserAgent(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"user-agent\") || \"Unknown\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"user-agent\"] || \"Unknown\";\n    }\n    // Rate limiting helper\n    checkRateLimit(identifier, maxAttempts = 5, windowMinutes = 15) {\n        // This is a simple in-memory rate limiter\n        // In production, you might want to use Redis or database\n        if (!this.rateLimitStore) {\n            this.rateLimitStore = new Map();\n        }\n        const now = Date.now();\n        const windowMs = windowMinutes * 60 * 1000;\n        const key = `rate_limit_${identifier}`;\n        if (!this.rateLimitStore.has(key)) {\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        const record = this.rateLimitStore.get(key);\n        if (now > record.resetTime) {\n            // Reset the window\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        if (record.count >= maxAttempts) {\n            return {\n                allowed: false,\n                remaining: 0,\n                resetTime: record.resetTime\n            };\n        }\n        record.count++;\n        return {\n            allowed: true,\n            remaining: maxAttempts - record.count\n        };\n    }\n    // Clean up expired rate limit entries\n    cleanupRateLimit() {\n        if (!this.rateLimitStore) return;\n        const now = Date.now();\n        for (const [key, record] of this.rateLimitStore.entries()){\n            if (now > record.resetTime) {\n                this.rateLimitStore.delete(key);\n            }\n        }\n    }\n    // Password strength validation\n    validatePasswordStrength(password) {\n        const minLength = 8;\n        const hasUpperCase = /[A-Z]/.test(password);\n        const hasLowerCase = /[a-z]/.test(password);\n        const hasNumbers = /\\d/.test(password);\n        const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n        const errors = [];\n        if (password.length < minLength) {\n            errors.push(`Password must be at least ${minLength} characters long`);\n        }\n        if (!hasUpperCase) {\n            errors.push(\"Password must contain at least one uppercase letter\");\n        }\n        if (!hasLowerCase) {\n            errors.push(\"Password must contain at least one lowercase letter\");\n        }\n        if (!hasNumbers) {\n            errors.push(\"Password must contain at least one number\");\n        }\n        if (!hasSpecialChar) {\n            errors.push(\"Password must contain at least one special character\");\n        }\n        return {\n            isValid: errors.length === 0,\n            errors,\n            strength: this.calculatePasswordStrength(password)\n        };\n    }\n    calculatePasswordStrength(password) {\n        let score = 0;\n        // Length bonus\n        score += Math.min(password.length * 2, 20);\n        // Character variety bonus\n        if (/[a-z]/.test(password)) score += 5;\n        if (/[A-Z]/.test(password)) score += 5;\n        if (/[0-9]/.test(password)) score += 5;\n        if (/[^A-Za-z0-9]/.test(password)) score += 10;\n        // Penalty for common patterns\n        if (/(.)\\1{2,}/.test(password)) score -= 10; // Repeated characters\n        if (/123|abc|qwe/i.test(password)) score -= 10; // Sequential patterns\n        if (score < 30) return \"weak\";\n        if (score < 60) return \"medium\";\n        return \"strong\";\n    }\n}\n// Export singleton instance\nlet authInstance = null;\nfunction getAuthManager() {\n    if (!authInstance) {\n        authInstance = new AuthManager();\n    }\n    return authInstance;\n}\nmodule.exports = {\n    getAuthManager,\n    AuthManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/database.js":
/*!*************************!*\
  !*** ./lib/database.js ***!
  \*************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\nconst bcrypt = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/umd/index.js\");\nconst { v4: uuidv4 } = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/cjs/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nclass DatabaseManager {\n    constructor(){\n        const dbPath = path.join(process.cwd(), \"data\", \"app.db\");\n        this.db = new Database(dbPath);\n        this.initializeTables();\n        this.createDefaultAdmin();\n    }\n    initializeTables() {\n        // Users table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        username TEXT UNIQUE NOT NULL,\n        password_hash TEXT NOT NULL,\n        role TEXT DEFAULT 'user' CHECK(role IN ('user', 'admin')),\n        license_key_id INTEGER,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        last_login DATETIME,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id)\n      )\n    `);\n        // License keys table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_keys (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        key_code TEXT UNIQUE NOT NULL,\n        duration_days INTEGER NOT NULL,\n        max_uses INTEGER NOT NULL DEFAULT 1,\n        current_uses INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        expires_at DATETIME NOT NULL,\n        is_active BOOLEAN DEFAULT 1,\n        created_by INTEGER,\n        features TEXT DEFAULT '[]',\n        FOREIGN KEY (created_by) REFERENCES users (id)\n      )\n    `);\n        // User sessions table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS user_sessions (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        token_hash TEXT NOT NULL,\n        expires_at DATETIME NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        ip_address TEXT,\n        user_agent TEXT,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Activity logs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER,\n        action TEXT NOT NULL,\n        details TEXT,\n        ip_address TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // System settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        setting_key TEXT UNIQUE NOT NULL,\n        setting_value TEXT,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_by INTEGER,\n        FOREIGN KEY (updated_by) REFERENCES users (id)\n      )\n    `);\n        // Encrypted login credentials table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS encrypted_credentials (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        login_key TEXT UNIQUE NOT NULL,\n        login_method TEXT NOT NULL CHECK(login_method IN ('normal', 'microsoft', 'google')),\n        encrypted_school TEXT NOT NULL,\n        encrypted_email TEXT NOT NULL,\n        encrypted_password TEXT NOT NULL,\n        encryption_iv TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // License feature settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_feature_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        license_key_id INTEGER NOT NULL,\n        max_accounts_per_batch INTEGER DEFAULT 0,\n        priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),\n        scheduling_access BOOLEAN DEFAULT 0,\n        multi_user_access BOOLEAN DEFAULT 0,\n        max_batches_per_day INTEGER DEFAULT 1,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id),\n        UNIQUE(license_key_id)\n      )\n    `);\n        // Queue batches table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_batches (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        batch_name TEXT,\n        login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft')),\n        total_accounts INTEGER NOT NULL,\n        processed_accounts INTEGER DEFAULT 0,\n        failed_accounts INTEGER DEFAULT 0,\n        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        scheduled_time DATETIME,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_batches if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_batches ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue jobs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_jobs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        batch_id INTEGER,\n        user_id INTEGER NOT NULL,\n        job_type TEXT NOT NULL DEFAULT 'sparx_reader',\n        job_data TEXT NOT NULL,\n        status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        effective_priority INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        scheduled_time DATETIME,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        error_message TEXT,\n        retry_count INTEGER DEFAULT 0,\n        max_retries INTEGER DEFAULT 3,\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id),\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_jobs if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_jobs ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue schedules table for conflict detection\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_schedules (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        scheduled_time DATETIME NOT NULL,\n        duration_minutes INTEGER DEFAULT 30,\n        srp_target INTEGER DEFAULT 100,\n        job_id INTEGER,\n        batch_id INTEGER,\n        status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (job_id) REFERENCES queue_jobs (id),\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id)\n      )\n    `);\n        // Add srp_target column if it doesn't exist (for existing databases)\n        try {\n            this.db.exec(`ALTER TABLE queue_schedules ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Create indexes for better performance\n        this.db.exec(`\n      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n      CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_code ON license_keys(key_code);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_expires ON license_keys(expires_at);\n      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);\n      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_created ON activity_logs(created_at);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_user ON encrypted_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_key ON encrypted_credentials(login_key);\n      CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time);\n    `);\n        // Migration: Add max_batches_per_day column if it doesn't exist\n        try {\n            const columns = this.db.prepare(\"PRAGMA table_info(license_feature_settings)\").all();\n            const hasMaxBatchesPerDay = columns.some((col)=>col.name === \"max_batches_per_day\");\n            if (!hasMaxBatchesPerDay) {\n                console.log(\"Adding max_batches_per_day column to license_feature_settings...\");\n                this.db.exec(`ALTER TABLE license_feature_settings ADD COLUMN max_batches_per_day INTEGER DEFAULT 1`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for max_batches_per_day:\", error);\n        }\n        // Migration: Add login_type column to queue_batches if it doesn't exist\n        try {\n            const batchColumns = this.db.prepare(\"PRAGMA table_info(queue_batches)\").all();\n            const hasLoginType = batchColumns.some((col)=>col.name === \"login_type\");\n            if (!hasLoginType) {\n                console.log(\"Adding login_type column to queue_batches...\");\n                this.db.exec(`ALTER TABLE queue_batches ADD COLUMN login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft'))`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for login_type:\", error);\n        }\n    }\n    createDefaultAdmin() {\n        const adminExists = this.db.prepare(\"SELECT id FROM users WHERE role = ? LIMIT 1\").get(\"admin\");\n        if (!adminExists) {\n            const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);\n            const stmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, role, is_active)\n        VALUES (?, ?, ?, ?)\n      `);\n            stmt.run(process.env.DEFAULT_ADMIN_USERNAME, hashedPassword, \"admin\", 1);\n            console.log(`Default admin user created: ${process.env.DEFAULT_ADMIN_USERNAME}/[hidden]`);\n        }\n    }\n    // User management methods\n    createUser(username, password, licenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // Validate license key\n            const licenseStmt = this.db.prepare(`\n        SELECT id, max_uses, current_uses, expires_at, is_active \n        FROM license_keys \n        WHERE key_code = ? AND is_active = 1\n      `);\n            const license = licenseStmt.get(licenseKey);\n            if (!license) {\n                throw new Error(\"Invalid license key\");\n            }\n            if (new Date(license.expires_at) < new Date()) {\n                throw new Error(\"License key has expired\");\n            }\n            if (license.current_uses >= license.max_uses) {\n                throw new Error(\"License key has reached maximum uses\");\n            }\n            // Check if username already exists\n            const userExists = this.db.prepare(\"SELECT id FROM users WHERE username = ?\").get(username);\n            if (userExists) {\n                throw new Error(\"Username already exists\");\n            }\n            // Create user\n            const hashedPassword = bcrypt.hashSync(password, 12);\n            const userStmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, license_key_id, role)\n        VALUES (?, ?, ?, ?)\n      `);\n            const result = userStmt.run(username, hashedPassword, license.id, \"user\");\n            // Update license key usage\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(license.id);\n            return result.lastInsertRowid;\n        });\n        return transaction();\n    }\n    authenticateUser(username, password) {\n        const stmt = this.db.prepare(`\n      SELECT u.*, lk.expires_at as license_expires, lk.is_active as license_active\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.username = ? AND u.is_active = 1\n    `);\n        const user = stmt.get(username);\n        if (!user) {\n            throw new Error(\"Invalid credentials\");\n        }\n        const isValidPassword = bcrypt.compareSync(password, user.password_hash);\n        if (!isValidPassword) {\n            throw new Error(\"Invalid credentials\");\n        }\n        // Check license validity for non-admin users\n        if (user.role !== \"admin\") {\n            if (!user.license_active || new Date(user.license_expires) < new Date()) {\n                throw new Error(\"License has expired\");\n            }\n        }\n        // Update last login\n        const updateStmt = this.db.prepare(\"UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?\");\n        updateStmt.run(user.id);\n        // Remove sensitive data\n        delete user.password_hash;\n        return user;\n    }\n    // License key management methods\n    createLicenseKey(durationDays, maxUses = 1, features = [], createdBy = null) {\n        const keyCode = this.generateLicenseKey();\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + durationDays);\n        const stmt = this.db.prepare(`\n      INSERT INTO license_keys (key_code, duration_days, max_uses, expires_at, created_by, features)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(keyCode, durationDays, maxUses, expiresAt.toISOString(), createdBy, JSON.stringify(features));\n        return {\n            id: result.lastInsertRowid,\n            keyCode,\n            durationDays,\n            maxUses,\n            expiresAt: expiresAt.toISOString(),\n            features\n        };\n    }\n    generateLicenseKey() {\n        const segments = [];\n        for(let i = 0; i < 4; i++){\n            segments.push(uuidv4().replace(/-/g, \"\").substring(0, 8).toUpperCase());\n        }\n        return `SRX-${segments.join(\"-\")}`;\n    }\n    getLicenseKeys(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        lk.*,\n        u.username as created_by_username,\n        COUNT(users.id) as users_count\n      FROM license_keys lk\n      LEFT JOIN users u ON lk.created_by = u.id\n      LEFT JOIN users ON users.license_key_id = lk.id\n      GROUP BY lk.id\n      ORDER BY lk.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    deactivateLicenseKey(keyId) {\n        const stmt = this.db.prepare(\"UPDATE license_keys SET is_active = 0 WHERE id = ?\");\n        return stmt.run(keyId);\n    }\n    // Get detailed license information for a user\n    getUserLicenseStatus(userId) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id as user_id,\n        u.username,\n        lk.id as license_id,\n        lk.key_code,\n        lk.max_uses,\n        lk.current_uses,\n        lk.expires_at,\n        lk.is_active,\n        CASE \n          WHEN lk.expires_at <= datetime('now') THEN 'expired'\n          WHEN lk.current_uses >= lk.max_uses THEN 'maxed_out'\n          WHEN lk.is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as license_status\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.id = ?\n    `);\n        return stmt.get(userId);\n    }\n    // Validate a license key for renewal (check if it's valid and has available uses)\n    validateLicenseForRenewal(licenseKey) {\n        const stmt = this.db.prepare(`\n      SELECT \n        id,\n        key_code,\n        max_uses,\n        current_uses,\n        expires_at,\n        is_active,\n        CASE \n          WHEN expires_at <= datetime('now') THEN 'expired'\n          WHEN current_uses >= max_uses THEN 'maxed_out'\n          WHEN is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as status\n      FROM license_keys \n      WHERE key_code = ?\n    `);\n        const license = stmt.get(licenseKey);\n        if (!license) {\n            return {\n                valid: false,\n                error: \"License key not found\"\n            };\n        }\n        if (license.status !== \"valid\") {\n            let errorMessage = \"License key is not valid\";\n            switch(license.status){\n                case \"expired\":\n                    errorMessage = \"License key has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"License key has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"License key is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage\n            };\n        }\n        return {\n            valid: true,\n            license\n        };\n    }\n    // Renew user's license with a new license key\n    renewUserLicense(userId, newLicenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // First validate the new license key\n            const validation = this.validateLicenseForRenewal(newLicenseKey);\n            if (!validation.valid) {\n                throw new Error(validation.error);\n            }\n            const newLicense = validation.license;\n            // Update user's license_key_id to the new license\n            const updateUserStmt = this.db.prepare(`\n        UPDATE users \n        SET license_key_id = ?, updated_at = CURRENT_TIMESTAMP \n        WHERE id = ?\n      `);\n            updateUserStmt.run(newLicense.id, userId);\n            // Increment the new license's current_uses\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(newLicense.id);\n            // Log the renewal activity\n            this.logActivity(userId, \"LICENSE_RENEWED\", `License renewed with key: ${newLicenseKey}`);\n            return {\n                success: true,\n                newLicenseId: newLicense.id,\n                newLicenseKey: newLicenseKey,\n                expiresAt: newLicense.expires_at,\n                maxUses: newLicense.max_uses,\n                currentUses: newLicense.current_uses + 1\n            };\n        });\n        return transaction();\n    }\n    // Session management\n    createSession(userId, tokenHash, expiresAt, ipAddress = null, userAgent = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, tokenHash, expiresAt, ipAddress, userAgent);\n    }\n    validateSession(tokenHash) {\n        const stmt = this.db.prepare(`\n      SELECT s.*, u.username, u.role, u.is_active as user_active\n      FROM user_sessions s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')\n    `);\n        return stmt.get(tokenHash);\n    }\n    invalidateSession(tokenHash) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?\");\n        return stmt.run(tokenHash);\n    }\n    invalidateAllUserSessions(userId) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE user_id = ?\");\n        return stmt.run(userId);\n    }\n    // Activity logging\n    logActivity(userId, action, details = null, ipAddress = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO activity_logs (user_id, action, details, ip_address)\n      VALUES (?, ?, ?, ?)\n    `);\n        return stmt.run(userId, action, details, ipAddress);\n    }\n    getActivityLogs(userId = null, limit = 100, offset = 0) {\n        let query = `\n      SELECT \n        al.*,\n        u.username\n      FROM activity_logs al\n      LEFT JOIN users u ON al.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE al.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY al.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Analytics methods\n    getSystemStats() {\n        const stats = {};\n        // Total users\n        stats.totalUsers = this.db.prepare(\"SELECT COUNT(*) as count FROM users WHERE role = 'user'\").get().count;\n        // Active users (logged in within last 30 days)\n        stats.activeUsers = this.db.prepare(`\n      SELECT COUNT(*) as count FROM users \n      WHERE role = 'user' AND last_login > datetime('now', '-30 days')\n    `).get().count;\n        // Total license keys\n        stats.totalLicenseKeys = this.db.prepare(\"SELECT COUNT(*) as count FROM license_keys\").get().count;\n        // Active license keys\n        stats.activeLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE is_active = 1 AND expires_at > datetime('now')\n    `).get().count;\n        // Expired license keys\n        stats.expiredLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE expires_at <= datetime('now')\n    `).get().count;\n        // Recent activity (last 24 hours)\n        stats.recentActivity = this.db.prepare(`\n      SELECT COUNT(*) as count FROM activity_logs \n      WHERE created_at > datetime('now', '-1 day')\n    `).get().count;\n        return stats;\n    }\n    // User management for admin\n    getUsers(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id,\n        u.username,\n        u.role,\n        u.created_at,\n        u.last_login,\n        u.is_active,\n        lk.key_code,\n        lk.expires_at as license_expires\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    toggleUserStatus(userId) {\n        const stmt = this.db.prepare(\"UPDATE users SET is_active = NOT is_active WHERE id = ?\");\n        return stmt.run(userId);\n    }\n    // Cleanup methods\n    cleanupExpiredSessions() {\n        const stmt = this.db.prepare('DELETE FROM user_sessions WHERE expires_at <= datetime(\"now\")');\n        return stmt.run();\n    }\n    cleanupOldLogs(daysToKeep = 90) {\n        const stmt = this.db.prepare(`\n      DELETE FROM activity_logs \n      WHERE created_at <= datetime('now', '-${daysToKeep} days')\n    `);\n        return stmt.run();\n    }\n    // Encrypted credentials methods\n    saveEncryptedCredentials(userId, loginMethod, school, email, password, encryptionKey) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        // Generate a unique login key\n        const loginKey = \"SLK-\" + crypto.randomBytes(8).toString(\"hex\").toUpperCase();\n        // Create encryption IV\n        const iv = crypto.randomBytes(16);\n        const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n        // Encrypt school, email and password\n        const cipher1 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedSchool = cipher1.update(school, \"utf8\", \"hex\") + cipher1.final(\"hex\");\n        const cipher2 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedEmail = cipher2.update(email, \"utf8\", \"hex\") + cipher2.final(\"hex\");\n        const cipher3 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedPassword = cipher3.update(password, \"utf8\", \"hex\") + cipher3.final(\"hex\");\n        const stmt = this.db.prepare(`\n      INSERT INTO encrypted_credentials (user_id, login_key, login_method, encrypted_school, encrypted_email, encrypted_password, encryption_iv)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        stmt.run(userId, loginKey, loginMethod, encryptedSchool, encryptedEmail, encryptedPassword, iv.toString(\"hex\"));\n        return loginKey;\n    }\n    getEncryptedCredentials(loginKey, encryptionKey) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM encrypted_credentials \n      WHERE login_key = ? AND is_active = 1\n    `);\n        const result = stmt.get(loginKey);\n        if (!result) return null;\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        try {\n            const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n            const iv = Buffer.from(result.encryption_iv, \"hex\");\n            // Decrypt school, email and password\n            const decipher1 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const school = result.encrypted_school ? decipher1.update(result.encrypted_school, \"hex\", \"utf8\") + decipher1.final(\"utf8\") : null;\n            const decipher2 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const email = result.encrypted_email ? decipher2.update(result.encrypted_email, \"hex\", \"utf8\") + decipher2.final(\"utf8\") : null;\n            const decipher3 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const password = result.encrypted_password ? decipher3.update(result.encrypted_password, \"hex\", \"utf8\") + decipher3.final(\"utf8\") : null;\n            return {\n                loginMethod: result.login_method,\n                school,\n                email,\n                password,\n                userId: result.user_id\n            };\n        } catch (error) {\n            console.error(\"Failed to decrypt credentials:\", error);\n            return null;\n        }\n    }\n    getUserCredentials(userId) {\n        const stmt = this.db.prepare(`\n      SELECT login_key, login_method, created_at FROM encrypted_credentials \n      WHERE user_id = ? AND is_active = 1\n      ORDER BY created_at DESC\n    `);\n        return stmt.all(userId);\n    }\n    deactivateCredentials(loginKey) {\n        const stmt = this.db.prepare(\"UPDATE encrypted_credentials SET is_active = 0 WHERE login_key = ?\");\n        return stmt.run(loginKey);\n    }\n    // License Feature Settings Methods\n    setLicenseFeatures(licenseKeyId, features) {\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO license_feature_settings\n      (license_key_id, max_accounts_per_batch, priority_level, scheduling_access, max_batches_per_day, updated_at)\n      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(licenseKeyId, features.max_accounts_per_batch || 0, features.priority_level || 0, features.scheduling_access ? 1 : 0, features.max_batches_per_day || 1);\n    }\n    getLicenseFeatures(licenseKeyId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM license_feature_settings WHERE license_key_id = ?\n    `);\n        const result = stmt.get(licenseKeyId);\n        if (!result) {\n            // Return default features if none set\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    getUserLicenseFeatures(userId) {\n        const stmt = this.db.prepare(`\n      SELECT lfs.* FROM license_feature_settings lfs\n      JOIN users u ON u.license_key_id = lfs.license_key_id\n      WHERE u.id = ?\n    `);\n        const result = stmt.get(userId);\n        if (!result) {\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    // Daily batch count check\n    getUserDailyBatchCount(userId, date = null) {\n        const targetDate = date || new Date().toISOString().split(\"T\")[0]; // YYYY-MM-DD format\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_batches\n      WHERE user_id = ?\n      AND DATE(created_at) = ?\n    `);\n        const result = stmt.get(userId, targetDate);\n        return result.count;\n    }\n    // Weekly schedule count check\n    getUserWeeklyScheduleCount(userId) {\n        // Get the start of the current week (Monday)\n        const now = new Date();\n        const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.\n        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // If Sunday, go back 6 days to Monday\n        const startOfWeek = new Date(now);\n        startOfWeek.setDate(now.getDate() - daysToMonday);\n        startOfWeek.setHours(0, 0, 0, 0);\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_schedules\n      WHERE user_id = ?\n      AND created_at >= ?\n      AND status != 'cancelled'\n    `);\n        const result = stmt.get(userId, startOfWeek.toISOString());\n        return result.count;\n    }\n    // Queue Batch Methods\n    createQueueBatch(userId, batchName, accounts, scheduledTime = null, loginType = \"normal\", srpTarget = 100) {\n        const transaction = this.db.transaction(()=>{\n            // Get user's license features\n            const features = this.getUserLicenseFeatures(userId);\n            // Check daily batch limit\n            const dailyBatchCount = this.getUserDailyBatchCount(userId);\n            if (dailyBatchCount >= features.max_batches_per_day) {\n                throw new Error(`Daily batch limit reached (${features.max_batches_per_day} batches per day). Please try again tomorrow.`);\n            }\n            // Validate batch size against license limits\n            if (features.max_accounts_per_batch > 0 && accounts.length > features.max_accounts_per_batch) {\n                throw new Error(`Batch size (${accounts.length}) exceeds license limit (${features.max_accounts_per_batch})`);\n            }\n            // Validate scheduling access\n            if (scheduledTime && !features.scheduling_access) {\n                throw new Error(\"Scheduling access not available for this license\");\n            }\n            // Validate login type\n            if (![\n                \"normal\",\n                \"google\",\n                \"microsoft\"\n            ].includes(loginType)) {\n                throw new Error(\"Invalid login type specified\");\n            }\n            // Validate SRP target\n            if (srpTarget < 1 || srpTarget > 400) {\n                throw new Error(\"SRP target must be between 1 and 400\");\n            }\n            // Create batch\n            const batchStmt = this.db.prepare(`\n        INSERT INTO queue_batches (user_id, batch_name, login_type, total_accounts, priority_level, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            const batchResult = batchStmt.run(userId, batchName, loginType, accounts.length, features.priority_level, srpTarget, scheduledTime);\n            const batchId = batchResult.lastInsertRowid;\n            // Create individual jobs for each account\n            const jobStmt = this.db.prepare(`\n        INSERT INTO queue_jobs (batch_id, user_id, job_data, priority_level, effective_priority, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            accounts.forEach((account)=>{\n                const effectivePriority = this.calculateEffectivePriority(features.priority_level, scheduledTime);\n                jobStmt.run(batchId, userId, JSON.stringify(account), features.priority_level, effectivePriority, srpTarget, scheduledTime);\n            });\n            // Create schedule entry if scheduled\n            if (scheduledTime) {\n                this.createScheduleEntry(userId, scheduledTime, null, batchId, 30, srpTarget);\n            }\n            // Log activity\n            this.logActivity(userId, \"BATCH_CREATED\", `Created batch: ${batchName} with ${accounts.length} accounts`);\n            return batchId;\n        });\n        return transaction();\n    }\n    calculateEffectivePriority(basePriority, scheduledTime) {\n        let effectivePriority = basePriority;\n        // Boost priority for scheduled jobs approaching their time\n        if (scheduledTime) {\n            const now = new Date();\n            const scheduled = new Date(scheduledTime);\n            const timeDiff = scheduled.getTime() - now.getTime();\n            const hoursUntil = timeDiff / (1000 * 60 * 60);\n            if (hoursUntil <= 1) {\n                effectivePriority += 5; // High boost for jobs due within an hour\n            } else if (hoursUntil <= 6) {\n                effectivePriority += 2; // Medium boost for jobs due within 6 hours\n            }\n        }\n        // Apply starvation prevention (boost priority for old jobs)\n        // This would be implemented in a background process\n        return Math.min(effectivePriority, 10); // Cap at maximum priority\n    }\n    getQueueBatches(userId = null, status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT qb.*, u.username\n      FROM queue_batches qb\n      JOIN users u ON qb.user_id = u.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (userId) {\n            query += \" AND qb.user_id = ?\";\n            params.push(userId);\n        }\n        if (status) {\n            query += \" AND qb.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY qb.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getBatchJobs(batchId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_jobs \n      WHERE batch_id = ? \n      ORDER BY effective_priority DESC, created_at ASC\n    `);\n        return stmt.all(batchId);\n    }\n    updateBatchStatus(batchId, status, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_batches\n      SET status = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, completedAt, batchId);\n    }\n    // Queue Job Methods\n    getNextQueueJob() {\n        const stmt = this.db.prepare(`\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n      WHERE qj.status = 'queued' \n      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))\n      ORDER BY qj.effective_priority DESC, qj.created_at ASC\n      LIMIT 1\n    `);\n        return stmt.get();\n    }\n    updateJobStatus(jobId, status, errorMessage = null, startedAt = null, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET status = ?, error_message = ?, started_at = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, errorMessage, startedAt, completedAt, jobId);\n    }\n    incrementJobRetry(jobId) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET retry_count = retry_count + 1, status = 'queued'\n      WHERE id = ? AND retry_count < max_retries\n    `);\n        return stmt.run(jobId);\n    }\n    // Scheduling Methods\n    createScheduleEntry(userId, scheduledTime, jobId = null, batchId = null, durationMinutes = 30, srpTarget = 100) {\n        // Check for conflicts\n        const conflicts = this.checkScheduleConflicts(userId, scheduledTime, durationMinutes);\n        if (conflicts.length > 0) {\n            throw new Error(`Schedule conflict detected at ${scheduledTime}`);\n        }\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_schedules (user_id, scheduled_time, duration_minutes, srp_target, job_id, batch_id)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, scheduledTime, durationMinutes, srpTarget, jobId, batchId);\n    }\n    checkScheduleConflicts(userId, scheduledTime, durationMinutes) {\n        const startTime = new Date(scheduledTime);\n        const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_schedules\n      WHERE user_id = ? \n      AND status IN ('scheduled', 'active')\n      AND (\n        (scheduled_time <= ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') > ?) OR\n        (scheduled_time < ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') >= ?)\n      )\n    `);\n        return stmt.all(userId, startTime.toISOString(), startTime.toISOString(), endTime.toISOString(), endTime.toISOString());\n    }\n    getUserSchedules(userId, startDate = null, endDate = null) {\n        let query = `\n      SELECT qs.*, qj.job_type, qb.batch_name\n      FROM queue_schedules qs\n      LEFT JOIN queue_jobs qj ON qs.job_id = qj.id\n      LEFT JOIN queue_batches qb ON qs.batch_id = qb.id\n      WHERE qs.user_id = ?\n    `;\n        const params = [\n            userId\n        ];\n        if (startDate) {\n            query += \" AND qs.scheduled_time >= ?\";\n            params.push(startDate);\n        }\n        if (endDate) {\n            query += \" AND qs.scheduled_time <= ?\";\n            params.push(endDate);\n        }\n        query += \" ORDER BY qs.scheduled_time ASC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Priority Management Methods\n    updateJobPriority(jobId, newPriority, adminOverride = false) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = ?, priority_level = ?\n      WHERE id = ?\n    `);\n        const result = stmt.run(newPriority, adminOverride ? newPriority : null, jobId);\n        if (adminOverride) {\n            this.logActivity(null, \"ADMIN_PRIORITY_OVERRIDE\", `Job ${jobId} priority set to ${newPriority}`);\n        }\n        return result;\n    }\n    applyStarvationPrevention() {\n        // Boost priority for jobs that have been waiting too long\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = CASE \n        WHEN datetime('now') > datetime(created_at, '+2 hours') THEN MIN(effective_priority + 2, 10)\n        WHEN datetime('now') > datetime(created_at, '+1 hour') THEN MIN(effective_priority + 1, 10)\n        ELSE effective_priority\n      END\n      WHERE status = 'queued'\n    `);\n        return stmt.run();\n    }\n    getQueueStats() {\n        const stats = {};\n        // Total jobs by status\n        const statusStmt = this.db.prepare(`\n      SELECT status, COUNT(*) as count \n      FROM queue_jobs \n      GROUP BY status\n    `);\n        stats.jobsByStatus = statusStmt.all();\n        // Jobs by priority level\n        const priorityStmt = this.db.prepare(`\n      SELECT effective_priority, COUNT(*) as count \n      FROM queue_jobs \n      WHERE status = 'queued'\n      GROUP BY effective_priority\n      ORDER BY effective_priority DESC\n    `);\n        stats.jobsByPriority = priorityStmt.all();\n        // Average wait time\n        const waitTimeStmt = this.db.prepare(`\n      SELECT AVG(julianday(started_at) - julianday(created_at)) * 24 * 60 as avg_wait_minutes\n      FROM queue_jobs \n      WHERE started_at IS NOT NULL\n    `);\n        stats.averageWaitTime = waitTimeStmt.get()?.avg_wait_minutes || 0;\n        return stats;\n    }\n    close() {\n        this.db.close();\n    }\n}\n// Create data directory if it doesn't exist\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst dataDir = path.join(process.cwd(), \"data\");\nif (!fs.existsSync(dataDir)) {\n    fs.mkdirSync(dataDir, {\n        recursive: true\n    });\n}\n// Export singleton instance\nlet dbInstance = null;\nfunction getDatabase() {\n    if (!dbInstance) {\n        dbInstance = new DatabaseManager();\n    }\n    return dbInstance;\n}\nmodule.exports = {\n    getDatabase,\n    DatabaseManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.js\n");

/***/ }),

/***/ "(rsc)/./lib/queueMiddleware.js":
/*!********************************!*\
  !*** ./lib/queueMiddleware.js ***!
  \********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst { getWebhookManager } = __webpack_require__(/*! ./webhook */ \"(rsc)/./lib/webhook.js\");\nclass QueueMiddleware {\n    static async validateLicenseFeatures(req, res, next) {\n        try {\n            const db = getDatabase();\n            const webhook = getWebhookManager();\n            const userId = req.user?.id;\n            if (!userId) {\n                return res.status(401).json({\n                    error: \"Authentication required\"\n                });\n            }\n            // Get user's license features\n            const features = db.getUserLicenseFeatures(userId);\n            req.licenseFeatures = features;\n            // Check specific feature requirements based on the endpoint\n            const endpoint = req.route?.path || req.path || req.url || \"\";\n            if (endpoint.includes(\"/batch\")) {\n                // Validate batch processing access\n                if (features.max_accounts_per_batch === 0) {\n                    await webhook.sendLicenseViolation(req.user.username, \"Batch Processing Access Denied\", \"User attempted to access batch processing without proper license\");\n                    return res.status(403).json({\n                        error: \"Batch processing not available for your license\",\n                        feature: \"max_accounts_per_batch\",\n                        current_limit: 0\n                    });\n                }\n            }\n            if (endpoint.includes(\"/schedule\")) {\n                // Validate scheduling access\n                if (!features.scheduling_access) {\n                    await webhook.sendLicenseViolation(req.user.username, \"Scheduling Access Denied\", \"User attempted to access scheduling without proper license\");\n                    return res.status(403).json({\n                        error: \"Scheduling not available for your license\",\n                        feature: \"scheduling_access\",\n                        current_access: false\n                    });\n                }\n            }\n            next();\n        } catch (error) {\n            console.error(\"License validation error:\", error);\n            res.status(500).json({\n                error: \"License validation failed\"\n            });\n        }\n    }\n    static async validateBatchSize(req, res, next) {\n        try {\n            const accounts = req.body.accounts || [];\n            const maxAccounts = req.licenseFeatures?.max_accounts_per_batch || 0;\n            if (maxAccounts > 0 && accounts.length > maxAccounts) {\n                const webhook = getWebhookManager();\n                await webhook.sendLicenseViolation(req.user.username, \"Batch Size Limit Exceeded\", `Attempted to submit ${accounts.length} accounts, limit is ${maxAccounts}`);\n                return res.status(403).json({\n                    error: \"Batch size exceeds license limit\",\n                    submitted_count: accounts.length,\n                    max_allowed: maxAccounts\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Batch size validation error:\", error);\n            res.status(500).json({\n                error: \"Batch size validation failed\"\n            });\n        }\n    }\n    static async validateDailyBatchLimit(req, res, next) {\n        try {\n            const db = getDatabase();\n            const userId = req.user.id;\n            const features = req.licenseFeatures;\n            // Check daily batch count\n            const dailyBatchCount = db.getUserDailyBatchCount(userId);\n            const maxBatchesPerDay = features.max_batches_per_day || 1;\n            if (dailyBatchCount >= maxBatchesPerDay) {\n                const webhook = getWebhookManager();\n                await webhook.sendLicenseViolation(req.user.username, \"Daily Batch Limit Exceeded\", `Attempted to create batch ${dailyBatchCount + 1}, daily limit is ${maxBatchesPerDay}`);\n                return res.status(403).json({\n                    error: \"Daily batch limit reached\",\n                    current_count: dailyBatchCount,\n                    max_allowed: maxBatchesPerDay,\n                    message: `You have reached your daily limit of ${maxBatchesPerDay} batch${maxBatchesPerDay > 1 ? \"es\" : \"\"}. Please try again tomorrow.`\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Daily batch limit validation error:\", error);\n            res.status(500).json({\n                error: \"Daily batch limit validation failed\"\n            });\n        }\n    }\n    static async validatePriorityLevel(req, res, next) {\n        try {\n            const requestedPriority = req.body.priority_level;\n            const maxPriority = req.licenseFeatures?.priority_level || 0;\n            if (requestedPriority !== undefined && requestedPriority > maxPriority) {\n                const webhook = getWebhookManager();\n                await webhook.sendLicenseViolation(req.user.username, \"Priority Level Exceeded\", `Attempted to set priority ${requestedPriority}, max allowed is ${maxPriority}`);\n                return res.status(403).json({\n                    error: \"Priority level exceeds license limit\",\n                    requested_priority: requestedPriority,\n                    max_allowed: maxPriority\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Priority validation error:\", error);\n            res.status(500).json({\n                error: \"Priority validation failed\"\n            });\n        }\n    }\n    static async validateWeeklyScheduleLimit(req, res, next) {\n        try {\n            const db = getDatabase();\n            const webhook = getWebhookManager();\n            const userId = req.user.id;\n            // Check weekly schedule count (1 account per week maximum)\n            const weeklyScheduleCount = db.getUserWeeklyScheduleCount(userId);\n            const maxSchedulesPerWeek = 1; // Fixed limit: 1 account per week\n            if (weeklyScheduleCount >= maxSchedulesPerWeek) {\n                await webhook.sendLicenseViolation(req.user.username, \"Weekly Schedule Limit Exceeded\", `Attempted to create schedule ${weeklyScheduleCount + 1}, weekly limit is ${maxSchedulesPerWeek}`);\n                return res.status(403).json({\n                    error: \"Weekly schedule limit reached\",\n                    current_count: weeklyScheduleCount,\n                    max_allowed: maxSchedulesPerWeek,\n                    message: `You have reached your weekly limit of ${maxSchedulesPerWeek} scheduled account. Please try again next week.`\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Weekly schedule limit validation error:\", error);\n            res.status(500).json({\n                error: \"Weekly schedule limit validation failed\"\n            });\n        }\n    }\n    static async checkScheduleConflicts(req, res, next) {\n        try {\n            const db = getDatabase();\n            const webhook = getWebhookManager();\n            const { scheduled_time, duration_minutes = 30 } = req.body;\n            const userId = req.user.id;\n            if (scheduled_time) {\n                const conflicts = db.checkScheduleConflicts(userId, scheduled_time, duration_minutes);\n                if (conflicts.length > 0) {\n                    await webhook.sendScheduleConflict(req.user.username, scheduled_time, `${conflicts.length} conflicting schedule(s) found`);\n                    return res.status(409).json({\n                        error: \"Schedule conflict detected\",\n                        requested_time: scheduled_time,\n                        conflicts: conflicts.map((c)=>({\n                                id: c.id,\n                                scheduled_time: c.scheduled_time,\n                                duration_minutes: c.duration_minutes\n                            }))\n                    });\n                }\n            }\n            next();\n        } catch (error) {\n            console.error(\"Schedule conflict check error:\", error);\n            res.status(500).json({\n                error: \"Schedule conflict check failed\"\n            });\n        }\n    }\n    static async logQueueActivity(req, res, next) {\n        try {\n            const db = getDatabase();\n            const originalSend = res.send;\n            res.send = function(data) {\n                // Log successful queue operations\n                if (res.statusCode >= 200 && res.statusCode < 300) {\n                    const action = req.method + \"_\" + req.route?.path?.replace(/[\\/\\:]/g, \"_\").toUpperCase();\n                    const details = {\n                        endpoint: req.originalUrl,\n                        method: req.method,\n                        status: res.statusCode\n                    };\n                    db.logActivity(req.user?.id, action, JSON.stringify(details));\n                }\n                originalSend.call(this, data);\n            };\n            next();\n        } catch (error) {\n            console.error(\"Activity logging error:\", error);\n            next(); // Don't block the request for logging errors\n        }\n    }\n    static gracefulDegradation(featureCheck) {\n        return (req, res, next)=>{\n            try {\n                const hasFeature = featureCheck(req.licenseFeatures);\n                if (!hasFeature) {\n                    // Instead of blocking, provide limited functionality\n                    req.degradedMode = true;\n                    req.degradationReason = \"License feature not available\";\n                }\n                next();\n            } catch (error) {\n                console.error(\"Graceful degradation error:\", error);\n                next();\n            }\n        };\n    }\n    static async rateLimitByLicense(req, res, next) {\n        try {\n            const db = getDatabase();\n            const userId = req.user.id;\n            const features = req.licenseFeatures;\n            // Implement rate limiting based on license features\n            const recentBatches = db.getQueueBatches(userId, null, 10, 0);\n            const recentBatchCount = recentBatches.filter((batch)=>{\n                const batchTime = new Date(batch.created_at);\n                const hourAgo = new Date(Date.now() - 60 * 60 * 1000);\n                return batchTime > hourAgo;\n            }).length;\n            // Basic rate limiting: higher priority licenses get more requests\n            const maxBatchesPerHour = Math.max(1, features.priority_level);\n            if (recentBatchCount >= maxBatchesPerHour) {\n                return res.status(429).json({\n                    error: \"Rate limit exceeded\",\n                    limit: maxBatchesPerHour,\n                    current: recentBatchCount,\n                    reset_time: new Date(Date.now() + 60 * 60 * 1000).toISOString()\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Rate limiting error:\", error);\n            next(); // Don't block for rate limiting errors\n        }\n    }\n}\nmodule.exports = QueueMiddleware;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/queueMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./lib/webhook.js":
/*!************************!*\
  !*** ./lib/webhook.js ***!
  \************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nclass WebhookManager {\n    constructor(){\n        this.discordWebhookUrl = \"https://discord.com/api/webhooks/1391133719351394314/VEY8LIMPErwXKx8ZGgsJvhLwbjfqHEtzhsbiZfwHb3aSUp9htUtWDy9mrW4N2LhuD6c9\";\n    }\n    async sendDiscordNotification(title, description, color = 0x3498db, fields = []) {\n        const embed = {\n            title,\n            description,\n            color,\n            timestamp: new Date().toISOString(),\n            fields,\n            footer: {\n                text: \"SparxReader Queue System\"\n            }\n        };\n        const payload = {\n            embeds: [\n                embed\n            ]\n        };\n        try {\n            await this.sendWebhook(this.discordWebhookUrl, payload);\n            console.log(\"Discord notification sent:\", title);\n        } catch (error) {\n            console.error(\"Failed to send Discord notification:\", error.message);\n        }\n    }\n    async sendLicenseViolation(username, violation, details) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDEAB License Violation Detected\", `User **${username}** attempted an unauthorized action`, 0xe74c3c, [\n            {\n                name: \"Violation Type\",\n                value: violation,\n                inline: true\n            },\n            {\n                name: \"Details\",\n                value: details,\n                inline: false\n            },\n            {\n                name: \"Timestamp\",\n                value: new Date().toLocaleString(),\n                inline: true\n            }\n        ]);\n    }\n    async sendScheduleConflict(username, scheduledTime, conflictDetails) {\n        await this.sendDiscordNotification(\"⚠️ Schedule Conflict Detected\", `Schedule conflict for user **${username}**`, 0xf39c12, [\n            {\n                name: \"Requested Time\",\n                value: new Date(scheduledTime).toLocaleString(),\n                inline: true\n            },\n            {\n                name: \"Conflict Details\",\n                value: conflictDetails,\n                inline: false\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            }\n        ]);\n    }\n    async sendPriorityAdjustment(jobId, oldPriority, newPriority, reason, adminUser = null) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDD04 Priority Adjustment\", `Job priority has been adjusted`, 0x9b59b6, [\n            {\n                name: \"Job ID\",\n                value: jobId.toString(),\n                inline: true\n            },\n            {\n                name: \"Old Priority\",\n                value: oldPriority.toString(),\n                inline: true\n            },\n            {\n                name: \"New Priority\",\n                value: newPriority.toString(),\n                inline: true\n            },\n            {\n                name: \"Reason\",\n                value: reason,\n                inline: false\n            },\n            ...adminUser ? [\n                {\n                    name: \"Admin User\",\n                    value: adminUser,\n                    inline: true\n                }\n            ] : []\n        ]);\n    }\n    async sendBatchCreated(username, batchName, accountCount, scheduledTime = null) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDCE6 New Batch Created\", `User **${username}** created a new batch`, 0x2ecc71, [\n            {\n                name: \"Batch Name\",\n                value: batchName,\n                inline: true\n            },\n            {\n                name: \"Account Count\",\n                value: accountCount.toString(),\n                inline: true\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            },\n            ...scheduledTime ? [\n                {\n                    name: \"Scheduled Time\",\n                    value: new Date(scheduledTime).toLocaleString(),\n                    inline: false\n                }\n            ] : []\n        ]);\n    }\n    async sendBatchCompleted(username, batchName, processedCount, failedCount, duration) {\n        await this.sendDiscordNotification(\"✅ Batch Completed\", `Batch processing completed for **${username}**`, 0x27ae60, [\n            {\n                name: \"Batch Name\",\n                value: batchName,\n                inline: true\n            },\n            {\n                name: \"Processed\",\n                value: processedCount.toString(),\n                inline: true\n            },\n            {\n                name: \"Failed\",\n                value: failedCount.toString(),\n                inline: true\n            },\n            {\n                name: \"Duration\",\n                value: `${Math.round(duration / 60)} minutes`,\n                inline: true\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            }\n        ]);\n    }\n    async sendQueueAlert(alertType, message, details = {}) {\n        const colors = {\n            \"high_load\": 0xe67e22,\n            \"system_error\": 0xe74c3c,\n            \"maintenance\": 0x3498db,\n            \"info\": 0x95a5a6 // Gray\n        };\n        const icons = {\n            \"high_load\": \"\\uD83D\\uDD25\",\n            \"system_error\": \"\\uD83D\\uDCA5\",\n            \"maintenance\": \"\\uD83D\\uDD27\",\n            \"info\": \"ℹ️\"\n        };\n        const fields = Object.entries(details).map(([key, value])=>({\n                name: key.replace(/_/g, \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                value: value.toString(),\n                inline: true\n            }));\n        await this.sendDiscordNotification(`${icons[alertType] || \"ℹ️\"} Queue System Alert`, message, colors[alertType] || 0x95a5a6, fields);\n    }\n    sendWebhook(webhookUrl, payload) {\n        return new Promise((resolve, reject)=>{\n            const url = new URL(webhookUrl);\n            const data = JSON.stringify(payload);\n            const options = {\n                hostname: url.hostname,\n                port: url.port || 443,\n                path: url.pathname + url.search,\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Content-Length\": Buffer.byteLength(data),\n                    \"User-Agent\": \"SparxReader-Queue-System/1.0\"\n                }\n            };\n            const req = https.request(options, (res)=>{\n                let responseData = \"\";\n                res.on(\"data\", (chunk)=>{\n                    responseData += chunk;\n                });\n                res.on(\"end\", ()=>{\n                    if (res.statusCode >= 200 && res.statusCode < 300) {\n                        resolve(responseData);\n                    } else {\n                        reject(new Error(`Webhook request failed with status ${res.statusCode}: ${responseData}`));\n                    }\n                });\n            });\n            req.on(\"error\", (error)=>{\n                reject(error);\n            });\n            req.on(\"timeout\", ()=>{\n                req.destroy();\n                reject(new Error(\"Webhook request timed out\"));\n            });\n            req.setTimeout(10000); // 10 second timeout\n            req.write(data);\n            req.end();\n        });\n    }\n}\n// Export singleton instance\nlet webhookInstance = null;\nfunction getWebhookManager() {\n    if (!webhookInstance) {\n        webhookInstance = new WebhookManager();\n    }\n    return webhookInstance;\n}\nmodule.exports = {\n    getWebhookManager,\n    WebhookManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvd2ViaG9vay5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsTUFBTUEsUUFBUUMsbUJBQU9BLENBQUM7QUFDdEIsTUFBTSxFQUFFQyxHQUFHLEVBQUUsR0FBR0QsbUJBQU9BLENBQUM7QUFFeEIsTUFBTUU7SUFDSkMsYUFBYztRQUNaLElBQUksQ0FBQ0MsaUJBQWlCLEdBQUc7SUFDM0I7SUFFQSxNQUFNQyx3QkFBd0JDLEtBQUssRUFBRUMsV0FBVyxFQUFFQyxRQUFRLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEVBQUU7UUFDL0UsTUFBTUMsUUFBUTtZQUNaSjtZQUNBQztZQUNBQztZQUNBRyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDakNKO1lBQ0FLLFFBQVE7Z0JBQ05DLE1BQU07WUFDUjtRQUNGO1FBRUEsTUFBTUMsVUFBVTtZQUNkQyxRQUFRO2dCQUFDUDthQUFNO1FBQ2pCO1FBRUEsSUFBSTtZQUNGLE1BQU0sSUFBSSxDQUFDUSxXQUFXLENBQUMsSUFBSSxDQUFDZCxpQkFBaUIsRUFBRVk7WUFDL0NHLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJkO1FBQzVDLEVBQUUsT0FBT2UsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsd0NBQXdDQSxNQUFNQyxPQUFPO1FBQ3JFO0lBQ0Y7SUFFQSxNQUFNQyxxQkFBcUJDLFFBQVEsRUFBRUMsU0FBUyxFQUFFQyxPQUFPLEVBQUU7UUFDdkQsTUFBTSxJQUFJLENBQUNyQix1QkFBdUIsQ0FDaEMsMkNBQ0EsQ0FBQyxPQUFPLEVBQUVtQixTQUFTLG1DQUFtQyxDQUFDLEVBQ3ZELFVBQ0E7WUFDRTtnQkFBRUcsTUFBTTtnQkFBa0JDLE9BQU9IO2dCQUFXSSxRQUFRO1lBQUs7WUFDekQ7Z0JBQUVGLE1BQU07Z0JBQVdDLE9BQU9GO2dCQUFTRyxRQUFRO1lBQU07WUFDakQ7Z0JBQUVGLE1BQU07Z0JBQWFDLE9BQU8sSUFBSWhCLE9BQU9rQixjQUFjO2dCQUFJRCxRQUFRO1lBQUs7U0FDdkU7SUFFTDtJQUVBLE1BQU1FLHFCQUFxQlAsUUFBUSxFQUFFUSxhQUFhLEVBQUVDLGVBQWUsRUFBRTtRQUNuRSxNQUFNLElBQUksQ0FBQzVCLHVCQUF1QixDQUNoQyxpQ0FDQSxDQUFDLDZCQUE2QixFQUFFbUIsU0FBUyxFQUFFLENBQUMsRUFDNUMsVUFDQTtZQUNFO2dCQUFFRyxNQUFNO2dCQUFrQkMsT0FBTyxJQUFJaEIsS0FBS29CLGVBQWVGLGNBQWM7Z0JBQUlELFFBQVE7WUFBSztZQUN4RjtnQkFBRUYsTUFBTTtnQkFBb0JDLE9BQU9LO2dCQUFpQkosUUFBUTtZQUFNO1lBQ2xFO2dCQUFFRixNQUFNO2dCQUFRQyxPQUFPSjtnQkFBVUssUUFBUTtZQUFLO1NBQy9DO0lBRUw7SUFFQSxNQUFNSyx1QkFBdUJDLEtBQUssRUFBRUMsV0FBVyxFQUFFQyxXQUFXLEVBQUVDLE1BQU0sRUFBRUMsWUFBWSxJQUFJLEVBQUU7UUFDdEYsTUFBTSxJQUFJLENBQUNsQyx1QkFBdUIsQ0FDaEMsb0NBQ0EsQ0FBQyw4QkFBOEIsQ0FBQyxFQUNoQyxVQUNBO1lBQ0U7Z0JBQUVzQixNQUFNO2dCQUFVQyxPQUFPTyxNQUFNSyxRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDeEQ7Z0JBQUVGLE1BQU07Z0JBQWdCQyxPQUFPUSxZQUFZSSxRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDcEU7Z0JBQUVGLE1BQU07Z0JBQWdCQyxPQUFPUyxZQUFZRyxRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDcEU7Z0JBQUVGLE1BQU07Z0JBQVVDLE9BQU9VO2dCQUFRVCxRQUFRO1lBQU07ZUFDM0NVLFlBQVk7Z0JBQUM7b0JBQUVaLE1BQU07b0JBQWNDLE9BQU9XO29CQUFXVixRQUFRO2dCQUFLO2FBQUUsR0FBRyxFQUFFO1NBQzlFO0lBRUw7SUFFQSxNQUFNWSxpQkFBaUJqQixRQUFRLEVBQUVrQixTQUFTLEVBQUVDLFlBQVksRUFBRVgsZ0JBQWdCLElBQUksRUFBRTtRQUM5RSxNQUFNLElBQUksQ0FBQzNCLHVCQUF1QixDQUNoQyxrQ0FDQSxDQUFDLE9BQU8sRUFBRW1CLFNBQVMsc0JBQXNCLENBQUMsRUFDMUMsVUFDQTtZQUNFO2dCQUFFRyxNQUFNO2dCQUFjQyxPQUFPYztnQkFBV2IsUUFBUTtZQUFLO1lBQ3JEO2dCQUFFRixNQUFNO2dCQUFpQkMsT0FBT2UsYUFBYUgsUUFBUTtnQkFBSVgsUUFBUTtZQUFLO1lBQ3RFO2dCQUFFRixNQUFNO2dCQUFRQyxPQUFPSjtnQkFBVUssUUFBUTtZQUFLO2VBQzFDRyxnQkFBZ0I7Z0JBQUM7b0JBQUVMLE1BQU07b0JBQWtCQyxPQUFPLElBQUloQixLQUFLb0IsZUFBZUYsY0FBYztvQkFBSUQsUUFBUTtnQkFBTTthQUFFLEdBQUcsRUFBRTtTQUN0SDtJQUVMO0lBRUEsTUFBTWUsbUJBQW1CcEIsUUFBUSxFQUFFa0IsU0FBUyxFQUFFRyxjQUFjLEVBQUVDLFdBQVcsRUFBRUMsUUFBUSxFQUFFO1FBQ25GLE1BQU0sSUFBSSxDQUFDMUMsdUJBQXVCLENBQ2hDLHFCQUNBLENBQUMsaUNBQWlDLEVBQUVtQixTQUFTLEVBQUUsQ0FBQyxFQUNoRCxVQUNBO1lBQ0U7Z0JBQUVHLE1BQU07Z0JBQWNDLE9BQU9jO2dCQUFXYixRQUFRO1lBQUs7WUFDckQ7Z0JBQUVGLE1BQU07Z0JBQWFDLE9BQU9pQixlQUFlTCxRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDcEU7Z0JBQUVGLE1BQU07Z0JBQVVDLE9BQU9rQixZQUFZTixRQUFRO2dCQUFJWCxRQUFRO1lBQUs7WUFDOUQ7Z0JBQUVGLE1BQU07Z0JBQVlDLE9BQU8sQ0FBQyxFQUFFb0IsS0FBS0MsS0FBSyxDQUFDRixXQUFXLElBQUksUUFBUSxDQUFDO2dCQUFFbEIsUUFBUTtZQUFLO1lBQ2hGO2dCQUFFRixNQUFNO2dCQUFRQyxPQUFPSjtnQkFBVUssUUFBUTtZQUFLO1NBQy9DO0lBRUw7SUFFQSxNQUFNcUIsZUFBZUMsU0FBUyxFQUFFN0IsT0FBTyxFQUFFSSxVQUFVLENBQUMsQ0FBQyxFQUFFO1FBQ3JELE1BQU0wQixTQUFTO1lBQ2IsYUFBYTtZQUNiLGdCQUFnQjtZQUNoQixlQUFlO1lBQ2YsUUFBUSxTQUFTLE9BQU87UUFDMUI7UUFFQSxNQUFNQyxRQUFRO1lBQ1osYUFBYTtZQUNiLGdCQUFnQjtZQUNoQixlQUFlO1lBQ2YsUUFBUTtRQUNWO1FBRUEsTUFBTTVDLFNBQVM2QyxPQUFPQyxPQUFPLENBQUM3QixTQUFTOEIsR0FBRyxDQUFDLENBQUMsQ0FBQ0MsS0FBSzdCLE1BQU0sR0FBTTtnQkFDNURELE1BQU04QixJQUFJQyxPQUFPLENBQUMsTUFBTSxLQUFLQSxPQUFPLENBQUMsU0FBU0MsQ0FBQUEsSUFBS0EsRUFBRUMsV0FBVztnQkFDaEVoQyxPQUFPQSxNQUFNWSxRQUFRO2dCQUNyQlgsUUFBUTtZQUNWO1FBRUEsTUFBTSxJQUFJLENBQUN4Qix1QkFBdUIsQ0FDaEMsQ0FBQyxFQUFFZ0QsS0FBSyxDQUFDRixVQUFVLElBQUksS0FBSyxtQkFBbUIsQ0FBQyxFQUNoRDdCLFNBQ0E4QixNQUFNLENBQUNELFVBQVUsSUFBSSxVQUNyQjFDO0lBRUo7SUFFQVMsWUFBWTJDLFVBQVUsRUFBRTdDLE9BQU8sRUFBRTtRQUMvQixPQUFPLElBQUk4QyxRQUFRLENBQUNDLFNBQVNDO1lBQzNCLE1BQU1DLE1BQU0sSUFBSWhFLElBQUk0RDtZQUNwQixNQUFNSyxPQUFPQyxLQUFLQyxTQUFTLENBQUNwRDtZQUU1QixNQUFNcUQsVUFBVTtnQkFDZEMsVUFBVUwsSUFBSUssUUFBUTtnQkFDdEJDLE1BQU1OLElBQUlNLElBQUksSUFBSTtnQkFDbEJDLE1BQU1QLElBQUlRLFFBQVEsR0FBR1IsSUFBSVMsTUFBTTtnQkFDL0JDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixrQkFBa0JDLE9BQU9DLFVBQVUsQ0FBQ1o7b0JBQ3BDLGNBQWM7Z0JBQ2hCO1lBQ0Y7WUFFQSxNQUFNYSxNQUFNaEYsTUFBTWlGLE9BQU8sQ0FBQ1gsU0FBUyxDQUFDWTtnQkFDbEMsSUFBSUMsZUFBZTtnQkFFbkJELElBQUlFLEVBQUUsQ0FBQyxRQUFRLENBQUNDO29CQUNkRixnQkFBZ0JFO2dCQUNsQjtnQkFFQUgsSUFBSUUsRUFBRSxDQUFDLE9BQU87b0JBQ1osSUFBSUYsSUFBSUksVUFBVSxJQUFJLE9BQU9KLElBQUlJLFVBQVUsR0FBRyxLQUFLO3dCQUNqRHRCLFFBQVFtQjtvQkFDVixPQUFPO3dCQUNMbEIsT0FBTyxJQUFJc0IsTUFBTSxDQUFDLG1DQUFtQyxFQUFFTCxJQUFJSSxVQUFVLENBQUMsRUFBRSxFQUFFSCxhQUFhLENBQUM7b0JBQzFGO2dCQUNGO1lBQ0Y7WUFFQUgsSUFBSUksRUFBRSxDQUFDLFNBQVMsQ0FBQzlEO2dCQUNmMkMsT0FBTzNDO1lBQ1Q7WUFFQTBELElBQUlJLEVBQUUsQ0FBQyxXQUFXO2dCQUNoQkosSUFBSVEsT0FBTztnQkFDWHZCLE9BQU8sSUFBSXNCLE1BQU07WUFDbkI7WUFFQVAsSUFBSVMsVUFBVSxDQUFDLFFBQVEsb0JBQW9CO1lBQzNDVCxJQUFJVSxLQUFLLENBQUN2QjtZQUNWYSxJQUFJVyxHQUFHO1FBQ1Q7SUFDRjtBQUNGO0FBRUEsNEJBQTRCO0FBQzVCLElBQUlDLGtCQUFrQjtBQUV0QixTQUFTQztJQUNQLElBQUksQ0FBQ0QsaUJBQWlCO1FBQ3BCQSxrQkFBa0IsSUFBSXpGO0lBQ3hCO0lBQ0EsT0FBT3lGO0FBQ1Q7QUFFQUUsT0FBT0MsT0FBTyxHQUFHO0lBQUVGO0lBQW1CMUY7QUFBZSIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vbGliL3dlYmhvb2suanM/ODQwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBodHRwcyA9IHJlcXVpcmUoJ2h0dHBzJyk7XHJcbmNvbnN0IHsgVVJMIH0gPSByZXF1aXJlKCd1cmwnKTtcclxuXHJcbmNsYXNzIFdlYmhvb2tNYW5hZ2VyIHtcclxuICBjb25zdHJ1Y3RvcigpIHtcclxuICAgIHRoaXMuZGlzY29yZFdlYmhvb2tVcmwgPSAnaHR0cHM6Ly9kaXNjb3JkLmNvbS9hcGkvd2ViaG9va3MvMTM5MTEzMzcxOTM1MTM5NDMxNC9WRVk4TElNUEVyd1hLeDhaR2dzSnZoTHdiamZxSEV0emhzYmlaZndIYjNhU1VwOWh0VXRXRHk5bXJXNE4yTGh1RDZjOSc7XHJcbiAgfVxyXG5cclxuICBhc3luYyBzZW5kRGlzY29yZE5vdGlmaWNhdGlvbih0aXRsZSwgZGVzY3JpcHRpb24sIGNvbG9yID0gMHgzNDk4ZGIsIGZpZWxkcyA9IFtdKSB7XHJcbiAgICBjb25zdCBlbWJlZCA9IHtcclxuICAgICAgdGl0bGUsXHJcbiAgICAgIGRlc2NyaXB0aW9uLFxyXG4gICAgICBjb2xvcixcclxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIGZpZWxkcyxcclxuICAgICAgZm9vdGVyOiB7XHJcbiAgICAgICAgdGV4dDogJ1NwYXJ4UmVhZGVyIFF1ZXVlIFN5c3RlbSdcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBwYXlsb2FkID0ge1xyXG4gICAgICBlbWJlZHM6IFtlbWJlZF1cclxuICAgIH07XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgdGhpcy5zZW5kV2ViaG9vayh0aGlzLmRpc2NvcmRXZWJob29rVXJsLCBwYXlsb2FkKTtcclxuICAgICAgY29uc29sZS5sb2coJ0Rpc2NvcmQgbm90aWZpY2F0aW9uIHNlbnQ6JywgdGl0bGUpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNlbmQgRGlzY29yZCBub3RpZmljYXRpb246JywgZXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBzZW5kTGljZW5zZVZpb2xhdGlvbih1c2VybmFtZSwgdmlvbGF0aW9uLCBkZXRhaWxzKSB7XHJcbiAgICBhd2FpdCB0aGlzLnNlbmREaXNjb3JkTm90aWZpY2F0aW9uKFxyXG4gICAgICAn8J+aqyBMaWNlbnNlIFZpb2xhdGlvbiBEZXRlY3RlZCcsXHJcbiAgICAgIGBVc2VyICoqJHt1c2VybmFtZX0qKiBhdHRlbXB0ZWQgYW4gdW5hdXRob3JpemVkIGFjdGlvbmAsXHJcbiAgICAgIDB4ZTc0YzNjLCAvLyBSZWQgY29sb3JcclxuICAgICAgW1xyXG4gICAgICAgIHsgbmFtZTogJ1Zpb2xhdGlvbiBUeXBlJywgdmFsdWU6IHZpb2xhdGlvbiwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnRGV0YWlscycsIHZhbHVlOiBkZXRhaWxzLCBpbmxpbmU6IGZhbHNlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnVGltZXN0YW1wJywgdmFsdWU6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKSwgaW5saW5lOiB0cnVlIH1cclxuICAgICAgXVxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIGFzeW5jIHNlbmRTY2hlZHVsZUNvbmZsaWN0KHVzZXJuYW1lLCBzY2hlZHVsZWRUaW1lLCBjb25mbGljdERldGFpbHMpIHtcclxuICAgIGF3YWl0IHRoaXMuc2VuZERpc2NvcmROb3RpZmljYXRpb24oXHJcbiAgICAgICfimqDvuI8gU2NoZWR1bGUgQ29uZmxpY3QgRGV0ZWN0ZWQnLFxyXG4gICAgICBgU2NoZWR1bGUgY29uZmxpY3QgZm9yIHVzZXIgKioke3VzZXJuYW1lfSoqYCxcclxuICAgICAgMHhmMzljMTIsIC8vIE9yYW5nZSBjb2xvclxyXG4gICAgICBbXHJcbiAgICAgICAgeyBuYW1lOiAnUmVxdWVzdGVkIFRpbWUnLCB2YWx1ZTogbmV3IERhdGUoc2NoZWR1bGVkVGltZSkudG9Mb2NhbGVTdHJpbmcoKSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnQ29uZmxpY3QgRGV0YWlscycsIHZhbHVlOiBjb25mbGljdERldGFpbHMsIGlubGluZTogZmFsc2UgfSxcclxuICAgICAgICB7IG5hbWU6ICdVc2VyJywgdmFsdWU6IHVzZXJuYW1lLCBpbmxpbmU6IHRydWUgfVxyXG4gICAgICBdXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgc2VuZFByaW9yaXR5QWRqdXN0bWVudChqb2JJZCwgb2xkUHJpb3JpdHksIG5ld1ByaW9yaXR5LCByZWFzb24sIGFkbWluVXNlciA9IG51bGwpIHtcclxuICAgIGF3YWl0IHRoaXMuc2VuZERpc2NvcmROb3RpZmljYXRpb24oXHJcbiAgICAgICfwn5SEIFByaW9yaXR5IEFkanVzdG1lbnQnLFxyXG4gICAgICBgSm9iIHByaW9yaXR5IGhhcyBiZWVuIGFkanVzdGVkYCxcclxuICAgICAgMHg5YjU5YjYsIC8vIFB1cnBsZSBjb2xvclxyXG4gICAgICBbXHJcbiAgICAgICAgeyBuYW1lOiAnSm9iIElEJywgdmFsdWU6IGpvYklkLnRvU3RyaW5nKCksIGlubGluZTogdHJ1ZSB9LFxyXG4gICAgICAgIHsgbmFtZTogJ09sZCBQcmlvcml0eScsIHZhbHVlOiBvbGRQcmlvcml0eS50b1N0cmluZygpLCBpbmxpbmU6IHRydWUgfSxcclxuICAgICAgICB7IG5hbWU6ICdOZXcgUHJpb3JpdHknLCB2YWx1ZTogbmV3UHJpb3JpdHkudG9TdHJpbmcoKSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnUmVhc29uJywgdmFsdWU6IHJlYXNvbiwgaW5saW5lOiBmYWxzZSB9LFxyXG4gICAgICAgIC4uLihhZG1pblVzZXIgPyBbeyBuYW1lOiAnQWRtaW4gVXNlcicsIHZhbHVlOiBhZG1pblVzZXIsIGlubGluZTogdHJ1ZSB9XSA6IFtdKVxyXG4gICAgICBdXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgc2VuZEJhdGNoQ3JlYXRlZCh1c2VybmFtZSwgYmF0Y2hOYW1lLCBhY2NvdW50Q291bnQsIHNjaGVkdWxlZFRpbWUgPSBudWxsKSB7XHJcbiAgICBhd2FpdCB0aGlzLnNlbmREaXNjb3JkTm90aWZpY2F0aW9uKFxyXG4gICAgICAn8J+TpiBOZXcgQmF0Y2ggQ3JlYXRlZCcsXHJcbiAgICAgIGBVc2VyICoqJHt1c2VybmFtZX0qKiBjcmVhdGVkIGEgbmV3IGJhdGNoYCxcclxuICAgICAgMHgyZWNjNzEsIC8vIEdyZWVuIGNvbG9yXHJcbiAgICAgIFtcclxuICAgICAgICB7IG5hbWU6ICdCYXRjaCBOYW1lJywgdmFsdWU6IGJhdGNoTmFtZSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnQWNjb3VudCBDb3VudCcsIHZhbHVlOiBhY2NvdW50Q291bnQudG9TdHJpbmcoKSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnVXNlcicsIHZhbHVlOiB1c2VybmFtZSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgLi4uKHNjaGVkdWxlZFRpbWUgPyBbeyBuYW1lOiAnU2NoZWR1bGVkIFRpbWUnLCB2YWx1ZTogbmV3IERhdGUoc2NoZWR1bGVkVGltZSkudG9Mb2NhbGVTdHJpbmcoKSwgaW5saW5lOiBmYWxzZSB9XSA6IFtdKVxyXG4gICAgICBdXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgc2VuZEJhdGNoQ29tcGxldGVkKHVzZXJuYW1lLCBiYXRjaE5hbWUsIHByb2Nlc3NlZENvdW50LCBmYWlsZWRDb3VudCwgZHVyYXRpb24pIHtcclxuICAgIGF3YWl0IHRoaXMuc2VuZERpc2NvcmROb3RpZmljYXRpb24oXHJcbiAgICAgICfinIUgQmF0Y2ggQ29tcGxldGVkJyxcclxuICAgICAgYEJhdGNoIHByb2Nlc3NpbmcgY29tcGxldGVkIGZvciAqKiR7dXNlcm5hbWV9KipgLFxyXG4gICAgICAweDI3YWU2MCwgLy8gRGFyayBncmVlbiBjb2xvclxyXG4gICAgICBbXHJcbiAgICAgICAgeyBuYW1lOiAnQmF0Y2ggTmFtZScsIHZhbHVlOiBiYXRjaE5hbWUsIGlubGluZTogdHJ1ZSB9LFxyXG4gICAgICAgIHsgbmFtZTogJ1Byb2Nlc3NlZCcsIHZhbHVlOiBwcm9jZXNzZWRDb3VudC50b1N0cmluZygpLCBpbmxpbmU6IHRydWUgfSxcclxuICAgICAgICB7IG5hbWU6ICdGYWlsZWQnLCB2YWx1ZTogZmFpbGVkQ291bnQudG9TdHJpbmcoKSwgaW5saW5lOiB0cnVlIH0sXHJcbiAgICAgICAgeyBuYW1lOiAnRHVyYXRpb24nLCB2YWx1ZTogYCR7TWF0aC5yb3VuZChkdXJhdGlvbiAvIDYwKX0gbWludXRlc2AsIGlubGluZTogdHJ1ZSB9LFxyXG4gICAgICAgIHsgbmFtZTogJ1VzZXInLCB2YWx1ZTogdXNlcm5hbWUsIGlubGluZTogdHJ1ZSB9XHJcbiAgICAgIF1cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBhc3luYyBzZW5kUXVldWVBbGVydChhbGVydFR5cGUsIG1lc3NhZ2UsIGRldGFpbHMgPSB7fSkge1xyXG4gICAgY29uc3QgY29sb3JzID0ge1xyXG4gICAgICAnaGlnaF9sb2FkJzogMHhlNjdlMjIsIC8vIE9yYW5nZVxyXG4gICAgICAnc3lzdGVtX2Vycm9yJzogMHhlNzRjM2MsIC8vIFJlZFxyXG4gICAgICAnbWFpbnRlbmFuY2UnOiAweDM0OThkYiwgLy8gQmx1ZVxyXG4gICAgICAnaW5mbyc6IDB4OTVhNWE2IC8vIEdyYXlcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgaWNvbnMgPSB7XHJcbiAgICAgICdoaWdoX2xvYWQnOiAn8J+UpScsXHJcbiAgICAgICdzeXN0ZW1fZXJyb3InOiAn8J+SpScsXHJcbiAgICAgICdtYWludGVuYW5jZSc6ICfwn5SnJyxcclxuICAgICAgJ2luZm8nOiAn4oS577iPJ1xyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBmaWVsZHMgPSBPYmplY3QuZW50cmllcyhkZXRhaWxzKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHtcclxuICAgICAgbmFtZToga2V5LnJlcGxhY2UoL18vZywgJyAnKS5yZXBsYWNlKC9cXGJcXHcvZywgbCA9PiBsLnRvVXBwZXJDYXNlKCkpLFxyXG4gICAgICB2YWx1ZTogdmFsdWUudG9TdHJpbmcoKSxcclxuICAgICAgaW5saW5lOiB0cnVlXHJcbiAgICB9KSk7XHJcblxyXG4gICAgYXdhaXQgdGhpcy5zZW5kRGlzY29yZE5vdGlmaWNhdGlvbihcclxuICAgICAgYCR7aWNvbnNbYWxlcnRUeXBlXSB8fCAn4oS577iPJ30gUXVldWUgU3lzdGVtIEFsZXJ0YCxcclxuICAgICAgbWVzc2FnZSxcclxuICAgICAgY29sb3JzW2FsZXJ0VHlwZV0gfHwgMHg5NWE1YTYsXHJcbiAgICAgIGZpZWxkc1xyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHNlbmRXZWJob29rKHdlYmhvb2tVcmwsIHBheWxvYWQpIHtcclxuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XHJcbiAgICAgIGNvbnN0IHVybCA9IG5ldyBVUkwod2ViaG9va1VybCk7XHJcbiAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnN0cmluZ2lmeShwYXlsb2FkKTtcclxuXHJcbiAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XHJcbiAgICAgICAgaG9zdG5hbWU6IHVybC5ob3N0bmFtZSxcclxuICAgICAgICBwb3J0OiB1cmwucG9ydCB8fCA0NDMsXHJcbiAgICAgICAgcGF0aDogdXJsLnBhdGhuYW1lICsgdXJsLnNlYXJjaCxcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgJ0NvbnRlbnQtTGVuZ3RoJzogQnVmZmVyLmJ5dGVMZW5ndGgoZGF0YSksXHJcbiAgICAgICAgICAnVXNlci1BZ2VudCc6ICdTcGFyeFJlYWRlci1RdWV1ZS1TeXN0ZW0vMS4wJ1xyXG4gICAgICAgIH1cclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IHJlcSA9IGh0dHBzLnJlcXVlc3Qob3B0aW9ucywgKHJlcykgPT4ge1xyXG4gICAgICAgIGxldCByZXNwb25zZURhdGEgPSAnJztcclxuICAgICAgICBcclxuICAgICAgICByZXMub24oJ2RhdGEnLCAoY2h1bmspID0+IHtcclxuICAgICAgICAgIHJlc3BvbnNlRGF0YSArPSBjaHVuaztcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgcmVzLm9uKCdlbmQnLCAoKSA9PiB7XHJcbiAgICAgICAgICBpZiAocmVzLnN0YXR1c0NvZGUgPj0gMjAwICYmIHJlcy5zdGF0dXNDb2RlIDwgMzAwKSB7XHJcbiAgICAgICAgICAgIHJlc29sdmUocmVzcG9uc2VEYXRhKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHJlamVjdChuZXcgRXJyb3IoYFdlYmhvb2sgcmVxdWVzdCBmYWlsZWQgd2l0aCBzdGF0dXMgJHtyZXMuc3RhdHVzQ29kZX06ICR7cmVzcG9uc2VEYXRhfWApKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICByZXEub24oJ2Vycm9yJywgKGVycm9yKSA9PiB7XHJcbiAgICAgICAgcmVqZWN0KGVycm9yKTtcclxuICAgICAgfSk7XHJcblxyXG4gICAgICByZXEub24oJ3RpbWVvdXQnLCAoKSA9PiB7XHJcbiAgICAgICAgcmVxLmRlc3Ryb3koKTtcclxuICAgICAgICByZWplY3QobmV3IEVycm9yKCdXZWJob29rIHJlcXVlc3QgdGltZWQgb3V0JykpO1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHJlcS5zZXRUaW1lb3V0KDEwMDAwKTsgLy8gMTAgc2Vjb25kIHRpbWVvdXRcclxuICAgICAgcmVxLndyaXRlKGRhdGEpO1xyXG4gICAgICByZXEuZW5kKCk7XHJcbiAgICB9KTtcclxuICB9XHJcbn1cclxuXHJcbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcclxubGV0IHdlYmhvb2tJbnN0YW5jZSA9IG51bGw7XHJcblxyXG5mdW5jdGlvbiBnZXRXZWJob29rTWFuYWdlcigpIHtcclxuICBpZiAoIXdlYmhvb2tJbnN0YW5jZSkge1xyXG4gICAgd2ViaG9va0luc3RhbmNlID0gbmV3IFdlYmhvb2tNYW5hZ2VyKCk7XHJcbiAgfVxyXG4gIHJldHVybiB3ZWJob29rSW5zdGFuY2U7XHJcbn1cclxuXHJcbm1vZHVsZS5leHBvcnRzID0geyBnZXRXZWJob29rTWFuYWdlciwgV2ViaG9va01hbmFnZXIgfTsiXSwibmFtZXMiOlsiaHR0cHMiLCJyZXF1aXJlIiwiVVJMIiwiV2ViaG9va01hbmFnZXIiLCJjb25zdHJ1Y3RvciIsImRpc2NvcmRXZWJob29rVXJsIiwic2VuZERpc2NvcmROb3RpZmljYXRpb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiY29sb3IiLCJmaWVsZHMiLCJlbWJlZCIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImZvb3RlciIsInRleHQiLCJwYXlsb2FkIiwiZW1iZWRzIiwic2VuZFdlYmhvb2siLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJtZXNzYWdlIiwic2VuZExpY2Vuc2VWaW9sYXRpb24iLCJ1c2VybmFtZSIsInZpb2xhdGlvbiIsImRldGFpbHMiLCJuYW1lIiwidmFsdWUiLCJpbmxpbmUiLCJ0b0xvY2FsZVN0cmluZyIsInNlbmRTY2hlZHVsZUNvbmZsaWN0Iiwic2NoZWR1bGVkVGltZSIsImNvbmZsaWN0RGV0YWlscyIsInNlbmRQcmlvcml0eUFkanVzdG1lbnQiLCJqb2JJZCIsIm9sZFByaW9yaXR5IiwibmV3UHJpb3JpdHkiLCJyZWFzb24iLCJhZG1pblVzZXIiLCJ0b1N0cmluZyIsInNlbmRCYXRjaENyZWF0ZWQiLCJiYXRjaE5hbWUiLCJhY2NvdW50Q291bnQiLCJzZW5kQmF0Y2hDb21wbGV0ZWQiLCJwcm9jZXNzZWRDb3VudCIsImZhaWxlZENvdW50IiwiZHVyYXRpb24iLCJNYXRoIiwicm91bmQiLCJzZW5kUXVldWVBbGVydCIsImFsZXJ0VHlwZSIsImNvbG9ycyIsImljb25zIiwiT2JqZWN0IiwiZW50cmllcyIsIm1hcCIsImtleSIsInJlcGxhY2UiLCJsIiwidG9VcHBlckNhc2UiLCJ3ZWJob29rVXJsIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJ1cmwiLCJkYXRhIiwiSlNPTiIsInN0cmluZ2lmeSIsIm9wdGlvbnMiLCJob3N0bmFtZSIsInBvcnQiLCJwYXRoIiwicGF0aG5hbWUiLCJzZWFyY2giLCJtZXRob2QiLCJoZWFkZXJzIiwiQnVmZmVyIiwiYnl0ZUxlbmd0aCIsInJlcSIsInJlcXVlc3QiLCJyZXMiLCJyZXNwb25zZURhdGEiLCJvbiIsImNodW5rIiwic3RhdHVzQ29kZSIsIkVycm9yIiwiZGVzdHJveSIsInNldFRpbWVvdXQiLCJ3cml0ZSIsImVuZCIsIndlYmhvb2tJbnN0YW5jZSIsImdldFdlYmhvb2tNYW5hZ2VyIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/webhook.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs","vendor-chunks/uuid","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fbatch%2Froute&page=%2Fapi%2Fqueue%2Fbatch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fbatch%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();