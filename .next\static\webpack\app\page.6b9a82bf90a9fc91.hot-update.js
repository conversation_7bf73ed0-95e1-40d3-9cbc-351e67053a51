"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.jsx":
/*!**********************!*\
  !*** ./app/page.jsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SparxReaderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SparxReaderPage() {\n    _s();\n    // Authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [authLoading, setAuthLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bookTitle, setBookTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [needsPlaywright, setNeedsPlaywright] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [screenshot, setScreenshot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [storyContent, setStoryContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showBookConfirmation, setShowBookConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSrp, setCurrentSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetSrp, setTargetSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showInitialSrpInput, setShowInitialSrpInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for enhanced UI\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentAnswer, setCurrentAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [questionNumber, setQuestionNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [srpEarned, setSrpEarned] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutomationRunning, setIsAutomationRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionHistory, setQuestionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [animationKey, setAnimationKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [automationComplete, setAutomationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginMethod, setLoginMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\"); // 'normal' or 'microsoft'\n    // Credential system states\n    const [showCredentialInput, setShowCredentialInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [credentialMode, setCredentialMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"enter\"); // 'enter' or 'key'\n    const [userSchool, setUserSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userEmail, setUserEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userPassword, setUserPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loginKey, setLoginKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [savedCredentials, setSavedCredentials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // License renewal states\n    const [showLicenseRenewal, setShowLicenseRenewal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [licenseStatus, setLicenseStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newLicenseKey, setNewLicenseKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [licenseRenewalLoading, setLicenseRenewalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check authentication on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuthentication();\n    }, []);\n    // Simulate question-solving process AFTER automation completes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated || !isAutomationRunning || !automationComplete) return;\n        if (isAutomationRunning && automationComplete) {\n            const interval = setInterval(()=>{\n                // Simulate question solving progress\n                setQuestionNumber((prev)=>{\n                    const newNum = prev + 1;\n                    setSrpEarned((prevSrp)=>prevSrp + Math.floor(Math.random() * 3) + 2);\n                    setAnimationKey((prevKey)=>prevKey + 1);\n                    // Simulate new question\n                    const sampleQuestions = [\n                        \"What was the main character's motivation in chapter 3?\",\n                        \"How did the setting influence the story's outcome?\",\n                        \"What literary device was used in the opening paragraph?\",\n                        \"Why did the protagonist make that crucial decision?\",\n                        \"What theme is most prominent throughout the narrative?\",\n                        \"How does the author develop the central conflict?\",\n                        \"What role does symbolism play in the narrative?\",\n                        \"How do the characters change throughout the story?\"\n                    ];\n                    const sampleAnswers = [\n                        \"To find their lost family member\",\n                        \"The harsh winter created urgency\",\n                        \"Metaphor and symbolism\",\n                        \"To protect their friends\",\n                        \"The importance of friendship\",\n                        \"Through escalating tension\",\n                        \"It reinforces the main themes\",\n                        \"They grow through adversity\"\n                    ];\n                    const randomIndex = Math.floor(Math.random() * sampleQuestions.length);\n                    setCurrentQuestion(sampleQuestions[randomIndex]);\n                    setCurrentAnswer(sampleAnswers[randomIndex]);\n                    // Add to history\n                    setQuestionHistory((prev)=>[\n                            ...prev,\n                            {\n                                number: newNum,\n                                question: sampleQuestions[randomIndex],\n                                answer: sampleAnswers[randomIndex]\n                            }\n                        ]);\n                    // Stop after reaching target or max questions\n                    if (newNum >= 8 || srpEarned >= parseInt(targetSrp || 50)) {\n                        setTimeout(()=>{\n                            setIsAutomationRunning(false);\n                            setMessage(\"Target SRP reached! Automation completed successfully.\");\n                        }, 1500); // Show the last question for a bit\n                        clearInterval(interval);\n                        return newNum;\n                    }\n                    return newNum;\n                });\n            }, 2500); // Show new question every 2.5 seconds\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isAuthenticated,\n        isAutomationRunning,\n        automationComplete,\n        srpEarned,\n        targetSrp\n    ]);\n    // Authentication functions\n    const checkAuthentication = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const response = await fetch(\"/api/auth/validate\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success && data.valid) {\n                setIsAuthenticated(true);\n                setUser(data.user);\n                setLicenseStatus(data.licenseStatus);\n            } else {\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"user\");\n                router.push(\"/login\");\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        } finally{\n            setAuthLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        }\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    // Handle login method selection (doesn't start the process)\n    const handleLoginMethodSelect = (method)=>{\n        setLoginMethod(method);\n    };\n    // Check license validity before starting bot\n    const checkLicenseValidity = ()=>{\n        // Admin users don't need license validation\n        if (user && user.role === \"admin\") {\n            return {\n                valid: true\n            };\n        }\n        if (!licenseStatus) {\n            return {\n                valid: false,\n                error: \"License information not available\"\n            };\n        }\n        if (licenseStatus.license_status !== \"valid\") {\n            let errorMessage = \"Your license is not valid\";\n            switch(licenseStatus.license_status){\n                case \"expired\":\n                    errorMessage = \"Your license has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"Your license has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"Your license is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage,\n                status: licenseStatus.license_status\n            };\n        }\n        return {\n            valid: true\n        };\n    };\n    // Handle license renewal\n    const handleLicenseRenewal = async ()=>{\n        if (!newLicenseKey.trim()) {\n            setMessage(\"Please enter a valid license key\");\n            return;\n        }\n        setLicenseRenewalLoading(true);\n        setMessage(\"Renewing license...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/auth/renew-license\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    newLicenseKey: newLicenseKey.trim()\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(\"License renewed successfully! You can now start the bot.\");\n                setShowLicenseRenewal(false);\n                setNewLicenseKey(\"\");\n                // Refresh authentication to get updated license status\n                await checkAuthentication();\n            } else {\n                setMessage(data.error || \"Failed to renew license\");\n            }\n        } catch (error) {\n            setMessage(\"Error occurred while renewing license\");\n        } finally{\n            setLicenseRenewalLoading(false);\n        }\n    };\n    // Handle the actual start process\n    const handleBeginClick = async ()=>{\n        // Check authentication before starting\n        if (!isAuthenticated || !user) {\n            setMessage(\"Please login to use this feature\");\n            router.push(\"/login\");\n            return;\n        }\n        // Check license validity before proceeding\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            return;\n        }\n        // Reset all states\n        setMessage(\"\");\n        setBookTitle(\"\");\n        setNeedsPlaywright(false);\n        setScreenshot(\"\");\n        setStoryContent(\"\");\n        setShowBookConfirmation(false);\n        setCurrentSrp(\"\");\n        setTargetSrp(\"\");\n        setShowInitialSrpInput(false);\n        setAutomationComplete(false);\n        // Reset credential states\n        setUserSchool(\"\");\n        setUserEmail(\"\");\n        setUserPassword(\"\");\n        setLoginKey(\"\");\n        // Mark that user has started the process\n        setHasStarted(true);\n        // Show credential input first\n        setShowCredentialInput(true);\n        // Load user's saved credentials\n        await loadSavedCredentials();\n    };\n    // Load user's saved credentials\n    const loadSavedCredentials = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/credentials/list\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSavedCredentials(data.credentials);\n            }\n        } catch (error) {\n            console.error(\"Failed to load saved credentials:\", error);\n        }\n    };\n    // Handle credential submission\n    const handleCredentialSubmit = async ()=>{\n        if (credentialMode === \"key\") {\n            // Use saved credentials with login key\n            if (!loginKey) {\n                setMessage(\"Please enter your login key\");\n                return;\n            }\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    // Proceed with automation using saved credentials\n                    setShowCredentialInput(false);\n                    setMessage(\"Please enter how much SRP you need to earn:\");\n                    setShowInitialSrpInput(true);\n                } else {\n                    setMessage(data.error || \"Invalid login key\");\n                }\n            } catch (error) {\n                setMessage(\"Error retrieving credentials\");\n            }\n        } else {\n            // Use entered credentials\n            if (!userSchool || !userEmail || !userPassword) {\n                setMessage(\"Please enter school, email and password\");\n                return;\n            }\n            // Ask if user wants to save credentials\n            if (confirm(\"Would you like to save these credentials for future use? You will receive a secure login key.\")) {\n                try {\n                    const token = localStorage.getItem(\"token\");\n                    const response = await fetch(\"/api/credentials/save\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            \"Authorization\": \"Bearer \".concat(token)\n                        },\n                        body: JSON.stringify({\n                            loginMethod,\n                            school: userSchool,\n                            email: userEmail,\n                            password: userPassword\n                        })\n                    });\n                    const data = await response.json();\n                    if (data.success) {\n                        alert(\"Credentials saved! Your login key is: \".concat(data.loginKey, \"\\n\\nPlease save this key securely. You can use it for future logins.\"));\n                    }\n                } catch (error) {\n                    console.error(\"Failed to save credentials:\", error);\n                }\n            }\n            // Proceed with automation\n            setShowCredentialInput(false);\n            setMessage(\"Please enter how much SRP you need to earn:\");\n            setShowInitialSrpInput(true);\n        }\n    };\n    const handleSrpSubmit = async ()=>{\n        // Validate SRP input\n        if (!targetSrp || isNaN(targetSrp) || parseInt(targetSrp) <= 0) {\n            setMessage(\"Please enter a valid SRP target (positive number)\");\n            return;\n        }\n        // Check license validity again before starting automation\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            setShowInitialSrpInput(false);\n            return;\n        }\n        setLoading(true);\n        setShowInitialSrpInput(false);\n        const isNormalLogin = loginMethod === \"normal\";\n        const isMicrosoftLogin = loginMethod === \"microsoft\";\n        const isGoogleLogin = loginMethod === \"google\";\n        // Get credentials\n        let credentials = null;\n        if (credentialMode === \"key\" && loginKey) {\n            // Get credentials from login key\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    credentials = data.credentials;\n                } else {\n                    setLoading(false);\n                    setMessage(data.error || \"Failed to retrieve credentials\");\n                    return;\n                }\n            } catch (error) {\n                setLoading(false);\n                setMessage(\"Error retrieving credentials\");\n                return;\n            }\n        } else if (credentialMode === \"enter\" && userSchool && userEmail && userPassword) {\n            // Use entered credentials\n            credentials = {\n                school: userSchool,\n                email: userEmail,\n                password: userPassword,\n                loginMethod: loginMethod\n            };\n        } else {\n            setLoading(false);\n            setMessage(\"No credentials available\");\n            return;\n        }\n        if (isNormalLogin) {\n            setMessage(\"Preparing to start...\");\n        } else if (isMicrosoftLogin) {\n            setMessage(\"Starting Microsoft login automation...\");\n        } else if (isGoogleLogin) {\n            setMessage(\"Starting Google login automation...\");\n        }\n        try {\n            let apiEndpoint, requestBody;\n            if (isNormalLogin) {\n                apiEndpoint = \"/api/sparxreader/start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isMicrosoftLogin) {\n                apiEndpoint = \"/api/sparxreader/microsoft-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isGoogleLogin) {\n                apiEndpoint = \"/api/sparxreader/google-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            }\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(apiEndpoint, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Book title and SRP extracted, show confirmation dialog\n                setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                setBookTitle(data.bookTitle);\n                setCurrentSrp(data.currentSrp);\n                setShowBookConfirmation(true);\n                if (data.screenshot) {\n                    setScreenshot(data.screenshot);\n                }\n            } else {\n                setMessage(data.error || \"Failed to start\");\n                if (data.needsPlaywright) {\n                    setNeedsPlaywright(true);\n                }\n            }\n        } catch (error) {\n            setMessage(\"Error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleYesClick = async ()=>{\n        setLoading(true);\n        setShowBookConfirmation(false);\n        setIsProcessing(true);\n        setMessage(\"Processing automation...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/sparxreader/navigate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    action: \"confirm\",\n                    bookTitle: bookTitle,\n                    targetSrp: targetSrp ? parseInt(targetSrp) : null\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Backend automation is complete, now show the simulation\n                setTimeout(()=>{\n                    setIsProcessing(false);\n                    setMessage(\"Automation completed! Displaying results...\");\n                    setStoryContent(data.storyContent);\n                    setBookTitle(data.bookTitle); // Update with actual book title\n                    // Start the question simulation AFTER automation is done\n                    setTimeout(()=>{\n                        setAutomationComplete(true);\n                        setIsAutomationRunning(true);\n                        setSrpEarned(0);\n                        setQuestionNumber(0);\n                        setQuestionHistory([]);\n                        setMessage(\"\");\n                    }, 1500);\n                    if (data.screenshot) {\n                        setScreenshot(data.screenshot);\n                    }\n                }, 2000);\n            } else {\n                setIsProcessing(false);\n                setMessage(data.error || \"Failed to navigate to book\");\n            }\n        } catch (error) {\n            setIsProcessing(false);\n            setMessage(\"Error occurred while navigating to book\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNoClick = async ()=>{\n        setLoading(true);\n        setMessage(\"Finding a different book with same SRP target...\");\n        try {\n            // Close current session\n            const token = localStorage.getItem(\"token\");\n            await fetch(\"/api/sparxreader/close\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            // Restart automation with existing target SRP\n            setLoading(true);\n            setShowInitialSrpInput(false);\n            setMessage(\"Preparing to start with new book...\");\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/sparxreader/start\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                        targetSrp: parseInt(targetSrp)\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                    setBookTitle(data.bookTitle);\n                    setCurrentSrp(data.currentSrp);\n                    setShowBookConfirmation(true);\n                    if (data.screenshot) {\n                        setScreenshot(data.screenshot);\n                    }\n                } else {\n                    setMessage(data.error || \"Failed to start\");\n                    if (data.needsPlaywright) {\n                        setNeedsPlaywright(true);\n                    }\n                }\n            } catch (error) {\n                setMessage(\"Error occurred while restarting\");\n            }\n        } catch (error) {\n            setMessage(\"Error closing previous session\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBackClick = ()=>{\n        setIsAutomationRunning(false);\n        setIsProcessing(false);\n        setCurrentQuestion(\"\");\n        setCurrentAnswer(\"\");\n        setQuestionNumber(0);\n        setSrpEarned(0);\n        setQuestionHistory([]);\n        setMessage(\"\");\n        setShowBookConfirmation(false);\n        setShowInitialSrpInput(false);\n        setHasStarted(false);\n        setAutomationComplete(false);\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 676,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 679,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 677,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 675,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 693,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"p-6 border-b border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto relative\",\n                            children: [\n                                (isAutomationRunning || isProcessing) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackClick,\n                                    className: \"absolute left-0 top-1/2 transform -translate-y-1/2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-all duration-200 flex items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"<\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 top-1/2 transform -translate-y-1/2 flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        user === null || user === void 0 ? void 0 : user.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"Administrator\" : \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                (user === null || user === void 0 ? void 0 : user.role) === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/admin\"),\n                                                    className: \"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Admin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/queue\"),\n                                                    className: \"px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Queue\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogout,\n                                                    className: \"px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-light text-white text-center\",\n                                    children: \"Sparx Reader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-center text-gray-400 mt-1 text-sm\",\n                                    children: \"Automated Question Solving\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 698,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 697,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-4xl mx-auto\",\n                            children: [\n                                isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-8 text-center shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-20 h-20 mx-auto mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 border-4 border-blue-500/30 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 bg-blue-500 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.5s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4\",\n                                                children: \"\\uD83D\\uDE80 Processing Automation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-6 text-lg\",\n                                                children: \"AI is solving questions in the background...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full max-w-md mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm text-gray-400 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Progress\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    Math.round(processingProgress),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-gray-700 rounded-full h-3 overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 rounded-full transition-all duration-500 ease-out relative\",\n                                                            style: {\n                                                                width: \"\".concat(processingProgress, \"%\")\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-white/20 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, this),\n                                isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                    children: \"\\uD83D\\uDCCA Live Progress Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 797,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-24 h-24 mx-auto mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-24 h-24 transform -rotate-90\",\n                                                                        viewBox: \"0 0 100 100\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                className: \"text-gray-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 808,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"url(#srpGradient)\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                strokeDasharray: \"\".concat(srpEarned / parseInt(targetSrp || 1) * 251.2, \" 251.2\"),\n                                                                                className: \"transition-all duration-1000 ease-out drop-shadow-lg\",\n                                                                                strokeLinecap: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 809,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                                    id: \"srpGradient\",\n                                                                                    x1: \"0%\",\n                                                                                    y1: \"0%\",\n                                                                                    x2: \"100%\",\n                                                                                    y2: \"100%\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"0%\",\n                                                                                            stopColor: \"#10B981\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 820,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"50%\",\n                                                                                            stopColor: \"#3B82F6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 821,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"100%\",\n                                                                                            stopColor: \"#8B5CF6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 822,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 818,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xl font-bold text-white block\",\n                                                                                    children: srpEarned\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 828,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: \"SRP\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 829,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 827,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 826,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 rounded-full bg-gradient-to-r from-green-500/20 to-blue-500/20 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 805,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium mb-1\",\n                                                                children: \"SRP Earned\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-400 text-xs\",\n                                                                children: [\n                                                                    \"Target: \",\n                                                                    targetSrp\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    Math.round(srpEarned / parseInt(targetSrp || 1) * 100),\n                                                                    \"% Complete\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold text-blue-400 mb-1 animate-pulse\",\n                                                                        children: questionNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"Questions Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 846,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-700 rounded-full h-3 overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-blue-500 to-cyan-400 h-3 rounded-full transition-all duration-1000 ease-out relative\",\n                                                                            style: {\n                                                                                width: \"\".concat(Math.min(questionNumber / 10 * 100, 100), \"%\")\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-white/30 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 848,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 847,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            Math.min(Math.round(questionNumber / 10 * 100), 100),\n                                                                            \"% of estimated session\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 844,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83E\\uDDE0 AI Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mx-auto mb-3 flex items-center justify-center animate-pulse\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 866,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"System Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 868,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-700 rounded-lg p-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-green-400 mb-2\",\n                                                                                children: \"\\uD83D\\uDFE2 Active & Processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 871,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400 mb-1\",\n                                                                                children: \"Performance Metrics\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 872,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-white\",\n                                                                                children: [\n                                                                                    \"⚡ \",\n                                                                                    questionNumber > 0 ? Math.round(elapsedTime / questionNumber) : 0,\n                                                                                    \"s per question\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 873,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-400\",\n                                                                                children: [\n                                                                                    \"\\uD83C\\uDFAF \",\n                                                                                    questionNumber > 0 ? Math.round(questionNumber / elapsedTime * 60) : 0,\n                                                                                    \" questions/min\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 876,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-purple-400 mt-1\",\n                                                                                children: [\n                                                                                    \"⏱️ \",\n                                                                                    formatTime(elapsedTime),\n                                                                                    \" elapsed\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 879,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 870,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83D\\uDE80 AI Engine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 15\n                                }, this),\n                                currentQuestion && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-slide-up\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4 animate-pulse\",\n                                                        children: questionNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                                children: \"✅ Question Solved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"AI successfully processed this question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 903,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-auto flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 906,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400 font-medium\",\n                                                                children: \"COMPLETED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 895,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/60 border border-gray-600 rounded-xl p-6 mb-6 backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"Q\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-base leading-relaxed mb-3\",\n                                                                    children: currentQuestion\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 917,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 bg-green-600 text-white text-xs rounded font-medium\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                Math.floor(Math.random() * 3) + 1,\n                                                                                \" SRP\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 919,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"• Solved automatically\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentAnswer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"AI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 931,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-400 font-medium mb-2\",\n                                                                    children: \"\\uD83E\\uDD16 AI Solution:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 935,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-200 leading-relaxed\",\n                                                                    children: currentAnswer\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 936,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 17\n                                    }, this)\n                                }, animationKey, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 15\n                                }, this),\n                                !hasStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-10 max-w-lg mx-auto shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-16 h-16 mx-auto mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-bounce\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-2xl\",\n                                                                    children: \"\\uD83D\\uDE80\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 952,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-purple-400 rounded-full animate-ping\",\n                                                                style: {\n                                                                    animationDelay: \"0.5s\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3\",\n                                                        children: \"Start Automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-base\",\n                                                        children: \"\\uD83E\\uDD16 AI-powered question solving for Sparx Reader\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 960,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                        children: \"\\uD83D\\uDD10 Choose Login Method\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 965,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"normal\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"normal\" ? \"bg-gradient-to-r from-green-600 to-green-500 text-white ring-2 ring-green-400 shadow-lg shadow-green-500/25\" : \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-500 text-white shadow-lg hover:shadow-green-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDC64\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Normal Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 979,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"normal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 980,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"microsoft\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"microsoft\" ? \"bg-gradient-to-r from-blue-600 to-blue-500 text-white ring-2 ring-blue-400 shadow-lg shadow-blue-500/25\" : \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-500 text-white shadow-lg hover:shadow-blue-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83C\\uDFE2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 992,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Microsoft Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 993,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"microsoft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 994,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"google\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"google\" ? \"bg-gradient-to-r from-red-600 to-red-500 text-white ring-2 ring-red-400 shadow-lg shadow-red-500/25\" : \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-500 text-white shadow-lg hover:shadow-red-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDD0D\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1006,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Google Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1007,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"google\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1008,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: [\n                                                                \"Selected: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        loginMethod === \"normal\" && \"Normal Login\",\n                                                                        loginMethod === \"microsoft\" && \"Microsoft Login\",\n                                                                        loginMethod === \"google\" && \"Google Login\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1016,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1015,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBeginClick,\n                                                        disabled: loading,\n                                                        className: \"w-full py-3 px-6 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Begin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1013,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 947,\n                                    columnNumber: 15\n                                }, this),\n                                showInitialSrpInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1041,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Set SRP Target\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"How much SRP do you want to earn?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: targetSrp,\n                                                        onChange: (e)=>setTargetSrp(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleSrpSubmit(),\n                                                        placeholder: \"Enter target (e.g., 50)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center\",\n                                                        min: \"1\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 text-center\",\n                                                        children: \"Automation will stop when target is reached\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSrpSubmit,\n                                                                disabled: loading || !targetSrp,\n                                                                className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1070,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Starting...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1069,\n                                                                    columnNumber: 27\n                                                                }, this) : \"Start\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1063,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowInitialSrpInput(false),\n                                                                disabled: loading,\n                                                                className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1075,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1047,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1038,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1037,\n                                    columnNumber: 15\n                                }, this),\n                                showCredentialInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"\\uD83D\\uDD10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Login Credentials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            loginMethod === \"normal\" && \"Enter your Sparx Learning credentials\",\n                                                            loginMethod === \"microsoft\" && \"Enter your Microsoft account credentials\",\n                                                            loginMethod === \"google\" && \"Enter your Google account credentials\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex bg-gray-800 rounded-lg p-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"enter\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"enter\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Enter Credentials\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"key\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"key\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Use Login Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1105,\n                                                columnNumber: 19\n                                            }, this),\n                                            credentialMode === \"enter\" ? /* Enter Credentials Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: userSchool,\n                                                        onChange: (e)=>setUserSchool(e.target.value),\n                                                        placeholder: \"School name (e.g., theangmeringschool)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1133,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: userEmail,\n                                                        onChange: (e)=>setUserEmail(e.target.value),\n                                                        placeholder: \"Email address\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1141,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        value: userPassword,\n                                                        onChange: (e)=>setUserPassword(e.target.value),\n                                                        placeholder: \"Password\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDCA1 Your credentials will be encrypted and stored securely. You'll receive a login key for future use.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1155,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1132,\n                                                columnNumber: 21\n                                            }, this) : /* Use Login Key Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: loginKey,\n                                                        onChange: (e)=>setLoginKey(e.target.value),\n                                                        placeholder: \"Enter your login key (e.g., SLK-ABC12345)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center font-mono\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1162,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    savedCredentials.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mb-2\",\n                                                                children: \"Your saved login keys:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1173,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1 max-h-32 overflow-y-auto\",\n                                                                children: savedCredentials.map((cred, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setLoginKey(cred.loginKey),\n                                                                        className: \"w-full text-left px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-sm text-gray-300 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-mono text-blue-400\",\n                                                                                children: cred.loginKey\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1181,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-2 text-xs\",\n                                                                                children: [\n                                                                                    \"(\",\n                                                                                    cred.loginMethod,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1182,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1176,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1174,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1172,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDD11 Use your previously generated login key to access saved credentials.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1189,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCredentialSubmit,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1203,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1202,\n                                                            columnNumber: 25\n                                                        }, this) : \"Continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1196,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowCredentialInput(false);\n                                                            setHasStarted(false);\n                                                        },\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1091,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 15\n                                }, this),\n                                showBookConfirmation && bookTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-lg w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"B\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1229,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1228,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Book Found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1231,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Confirm to start automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1227,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/30 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-white mb-4\",\n                                                        children: bookTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Current SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1239,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: currentSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1240,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1238,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Target SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: targetSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1244,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1235,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleYesClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1257,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Starting...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 25\n                                                        }, this) : \"Yes, Start\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1250,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleNoClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? \"Finding...\" : \"Find Different\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1249,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1226,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1225,\n                                    columnNumber: 15\n                                }, this),\n                                questionHistory.length > 0 && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"H\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1279,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Question History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-60 overflow-y-auto\",\n                                                children: questionHistory.slice(-5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/30 rounded p-3 border-l-4 border-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 font-medium text-sm\",\n                                                                        children: [\n                                                                            \"Q\",\n                                                                            item.number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1286,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 text-xs\",\n                                                                        children: \"Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1287,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1285,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm mb-2\",\n                                                                children: [\n                                                                    item.question.substring(0, 100),\n                                                                    \"...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1289,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: [\n                                                                    \"Answer: \",\n                                                                    item.answer\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1290,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1284,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1282,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1277,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1276,\n                                    columnNumber: 15\n                                }, this),\n                                message && !showInitialSrpInput && !showBookConfirmation && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded p-4 inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white\",\n                                            children: message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 1302,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1301,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1300,\n                                    columnNumber: 15\n                                }, this),\n                                needsPlaywright && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-red-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1312,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Setup Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1315,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Playwright browsers need to be installed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1316,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-sm mb-2\",\n                                                        children: \"Run this command in your terminal:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-black text-blue-400 p-2 rounded block text-sm\",\n                                                        children: \"npx playwright install chromium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1321,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-xs text-center\",\n                                                children: \"After installation, refresh this page and try again.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1326,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1310,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1309,\n                                    columnNumber: 15\n                                }, this),\n                                showLicenseRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-8 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-500 text-2xl\",\n                                                            children: \"⚠️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1339,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-medium text-white mb-2\",\n                                                        children: \"License Renewal Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1341,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            licenseStatus && licenseStatus.license_status === \"expired\" && \"Your license has expired.\",\n                                                            licenseStatus && licenseStatus.license_status === \"maxed_out\" && \"Your license has reached maximum uses.\",\n                                                            licenseStatus && licenseStatus.license_status === \"inactive\" && \"Your license is inactive.\",\n                                                            (!licenseStatus || licenseStatus.license_status === \"valid\") && \"Your license is not valid.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1342,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mt-2\",\n                                                        children: \"Please enter a new license key to continue using the bot.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1348,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1337,\n                                                columnNumber: 19\n                                            }, this),\n                                            licenseStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 border border-gray-700 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-white font-medium mb-2\",\n                                                        children: \"Current License Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1355,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Key: \",\n                                                                    licenseStatus.key_code || \"N/A\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1357,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium \".concat(licenseStatus.license_status === \"expired\" ? \"text-red-400\" : licenseStatus.license_status === \"maxed_out\" ? \"text-orange-400\" : licenseStatus.license_status === \"inactive\" ? \"text-gray-400\" : \"text-green-400\"),\n                                                                        children: [\n                                                                            licenseStatus.license_status === \"expired\" && \"Expired\",\n                                                                            licenseStatus.license_status === \"maxed_out\" && \"Max Uses Reached\",\n                                                                            licenseStatus.license_status === \"inactive\" && \"Inactive\",\n                                                                            licenseStatus.license_status === \"valid\" && \"Valid\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1358,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1358,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            licenseStatus.expires_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Expires: \",\n                                                                    new Date(licenseStatus.expires_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1370,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            licenseStatus.max_uses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Uses: \",\n                                                                    licenseStatus.current_uses || 0,\n                                                                    \"/\",\n                                                                    licenseStatus.max_uses\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1373,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1354,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-white font-medium mb-2\",\n                                                        children: \"New License Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newLicenseKey,\n                                                        onChange: (e)=>setNewLicenseKey(e.target.value),\n                                                        placeholder: \"Enter your new license key (e.g., SRX-XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXXXX)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:border-blue-500 focus:outline-none\",\n                                                        disabled: licenseRenewalLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1379,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLicenseRenewal,\n                                                        disabled: licenseRenewalLoading || !newLicenseKey.trim(),\n                                                        className: \"flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded transition-colors duration-200\",\n                                                        children: licenseRenewalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1399,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Renewing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1398,\n                                                            columnNumber: 25\n                                                        }, this) : \"Renew License\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1392,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowLicenseRenewal(false);\n                                                            setNewLicenseKey(\"\");\n                                                            setMessage(\"\");\n                                                        },\n                                                        disabled: licenseRenewalLoading,\n                                                        className: \"px-6 py-3 border border-gray-600 text-gray-400 hover:text-white hover:border-gray-500 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1391,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1336,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1335,\n                                    columnNumber: 15\n                                }, this),\n                                screenshot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"S\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1427,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Browser State\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1426,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded border border-gray-700 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: screenshot,\n                                                    alt: \"Browser screenshot\",\n                                                    className: \"w-full h-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1431,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1425,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1424,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 749,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 748,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 695,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n        lineNumber: 691,\n        columnNumber: 5\n    }, this);\n}\n_s(SparxReaderPage, \"nT2pVUa4W3uDUws2fb2Acpiieuo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SparxReaderPage;\nvar _c;\n$RefreshReg$(_c, \"SparxReaderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.jsx\n"));

/***/ })

});