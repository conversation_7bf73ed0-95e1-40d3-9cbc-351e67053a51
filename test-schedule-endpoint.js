// Test script to verify schedule endpoint is working
// Using built-in fetch (Node.js 18+)

async function testScheduleEndpoint() {
  try {
    console.log('Testing schedule endpoint...');
    
    // Test without authentication first to see if server is responding
    const response = await fetch('http://localhost:3000/api/queue/schedule', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        scheduled_time: new Date(Date.now() + 60000).toISOString(), // 1 minute from now
        srp_target: 50,
        job_type: 'sparx_reader',
        login_type: 'normal',
        job_data: {
          school: 'Test School',
          email: '<EMAIL>',
          password: 'testpass'
        }
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    if (response.status === 401) {
      console.log('✅ Server is responding - authentication required as expected');
    } else {
      console.log('❌ Unexpected response');
    }
    
  } catch (error) {
    console.error('❌ Failed to connect to server:', error.message);
    console.log('Make sure Next.js server is running on port 3000');
  }
}

testScheduleEndpoint();
