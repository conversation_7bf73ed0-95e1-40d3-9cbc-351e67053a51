"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/queue/page",{

/***/ "(app-pages-browser)/./app/queue/page.jsx":
/*!****************************!*\
  !*** ./app/queue/page.jsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QueueDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ScheduleCalendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ScheduleCalendar */ \"(app-pages-browser)/./app/queue/components/ScheduleCalendar.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction QueueDashboard() {\n    var _queueStatus_license_features, _queueStatus_global_overview_queue_status, _queueStatus_global_overview, _queueStatus_license_features1, _queueStatus_license_features2, _queueStatus_license_features3, _queueStatus_license_features4, _queueStatus_license_features5, _queueStatus_license_features6;\n    _s();\n    const [queueStatus, setQueueStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [batches, setBatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [priorityLevels, setPriorityLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [processingJobs, setProcessingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Form states\n    const [batchForm, setBatchForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        batch_name: \"\",\n        login_type: \"normal\",\n        accounts: [\n            {\n                school: \"\",\n                email: \"\",\n                password: \"\"\n            }\n        ],\n        srp_target: 100,\n        priority_override: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadQueueData();\n    }, []);\n    const loadQueueData = async ()=>{\n        try {\n            setLoading(true);\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const headers = {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            };\n            // Load queue status\n            const statusResponse = await fetch(\"/api/queue/status?detailed=true\", {\n                headers\n            });\n            if (statusResponse.ok) {\n                const statusData = await statusResponse.json();\n                setQueueStatus(statusData);\n            }\n            // Load batches\n            const batchResponse = await fetch(\"/api/queue/batch\", {\n                headers\n            });\n            if (batchResponse.ok) {\n                const batchData = await batchResponse.json();\n                setBatches(batchData.batches || []);\n            }\n            // Load schedules\n            const scheduleResponse = await fetch(\"/api/queue/schedule\", {\n                headers\n            });\n            if (scheduleResponse.ok) {\n                const scheduleData = await scheduleResponse.json();\n                setSchedules(scheduleData.schedules || []);\n            }\n            // Load priority levels\n            const priorityResponse = await fetch(\"/api/queue/priority-levels\", {\n                headers\n            });\n            if (priorityResponse.ok) {\n                const priorityData = await priorityResponse.json();\n                setPriorityLevels(priorityData);\n            }\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBatchSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/queue/batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(batchForm)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                alert('Batch \"'.concat(result.batch.name, '\" created successfully!'));\n                setBatchForm({\n                    batch_name: \"\",\n                    login_type: \"normal\",\n                    accounts: [\n                        {\n                            school: \"\",\n                            email: \"\",\n                            password: \"\"\n                        }\n                    ],\n                    srp_target: 100,\n                    priority_override: \"\"\n                });\n                loadQueueData(); // Refresh data\n            } else {\n                const error = await response.json();\n                alert(\"Error: \".concat(error.error));\n            }\n        } catch (err) {\n            alert(\"Error: \".concat(err.message));\n        }\n    };\n    const addAccount = ()=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: [\n                    ...prev.accounts,\n                    {\n                        school: \"\",\n                        email: \"\",\n                        password: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeAccount = (index)=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: prev.accounts.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateAccount = (index, field, value)=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: prev.accounts.map((account, i)=>i === index ? {\n                        ...account,\n                        [field]: value\n                    } : account)\n            }));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-300\",\n                        children: \"Loading queue dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-400 text-xl mb-4\",\n                        children: \"⚠️ Error\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: loadQueueData,\n                        className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Queue Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-300\",\n                                    children: \"Manage your batch processing and scheduling\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadQueueData,\n                                    className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                                    children: \"\\uD83D\\uDD04 Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/\"),\n                                    className: \"px-4 py-2 bg-gray-800 text-gray-300 rounded hover:bg-gray-700 hover:text-white transition-colors\",\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.license_features) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-gray-900 border border-gray-700 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-400 mb-2\",\n                            children: \"Your License Features\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.max_accounts_per_batch || \"Unlimited\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Max Accounts per Batch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.priority_level\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Priority Level\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.scheduling_access ? \"✅\" : \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Scheduling Access\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                name: \"Overview\"\n                            },\n                            ...(queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features = queueStatus.license_features) === null || _queueStatus_license_features === void 0 ? void 0 : _queueStatus_license_features.max_accounts_per_batch) > 0 ? [\n                                {\n                                    id: \"batches\",\n                                    name: \"Batches\"\n                                },\n                                {\n                                    id: \"create\",\n                                    name: \"Create Batch\"\n                                }\n                            ] : [],\n                            {\n                                id: \"schedule\",\n                                name: \"Schedule\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? \"border-blue-400 text-blue-400\" : \"border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-600\"),\n                                children: tab.name\n                            }, tab.id, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.global_overview) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Real-time Queue Overview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-400\",\n                                                            children: queueStatus.global_overview.total_users\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Total Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-400\",\n                                                            children: queueStatus.global_overview.total_batches_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Batches Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-yellow-400\",\n                                                            children: queueStatus.global_overview.queue_status.queued_jobs\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Jobs in Queue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        queueStatus.global_overview.queue_status.queued_jobs === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: \"No pending jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-400\",\n                                                            children: queueStatus.global_overview.queue_status.processing_jobs\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Processing Now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        queueStatus.global_overview.queue_status.processing_jobs === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: \"System idle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-green-500\",\n                                                            children: queueStatus.global_overview.queue_status.completed_jobs_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Completed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-red-400\",\n                                                            children: queueStatus.global_overview.queue_status.failed_jobs_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Failed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 251,\n                            columnNumber: 15\n                        }, this),\n                        queueStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white\",\n                                        children: \"Your Queue Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Total Batches\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: queueStatus.user_queue_status.total_batches\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Active Jobs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-400\",\n                                                        children: queueStatus.user_queue_status.queued_jobs + queueStatus.user_queue_status.processing_jobs\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Completed Jobs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: queueStatus.user_queue_status.completed_jobs\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Estimated Wait\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-400\",\n                                                        children: queueStatus.estimated_wait_time.formatted\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 311,\n                            columnNumber: 15\n                        }, this),\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_global_overview = queueStatus.global_overview) === null || _queueStatus_global_overview === void 0 ? void 0 : (_queueStatus_global_overview_queue_status = _queueStatus_global_overview.queue_status) === null || _queueStatus_global_overview_queue_status === void 0 ? void 0 : _queueStatus_global_overview_queue_status.processing_jobs) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Live Processing (\",\n                                            queueStatus.global_overview.queue_status.processing_jobs,\n                                            \" active)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-white mb-2\",\n                                                children: queueStatus.global_overview.direct_homework_processing ? \"Homework Being Solved\" : \"Jobs Processing\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: queueStatus.global_overview.direct_homework_processing ? \"Direct homework solving in progress\" : \"\".concat(queueStatus.global_overview.queue_status.processing_jobs, \" job\").concat(queueStatus.global_overview.queue_status.processing_jobs !== 1 ? \"s\" : \"\", \" currently being processed\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-2\",\n                                                children: \"Live processing in progress\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 348,\n                            columnNumber: 15\n                        }, this),\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.queue_positions) && queueStatus.queue_positions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white\",\n                                        children: \"Your Queue Positions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: queueStatus.queue_positions.map((position)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center p-3 bg-gray-800 border border-gray-700 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-white\",\n                                                                children: [\n                                                                    \"Job #\",\n                                                                    position.id\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: [\n                                                                    \"Priority: \",\n                                                                    position.effective_priority\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    position.status\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-blue-400\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    position.queue_position\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"in queue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, position.id, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 379,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 248,\n                    columnNumber: 11\n                }, this),\n                (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features1 = queueStatus.license_features) === null || _queueStatus_license_features1 === void 0 ? void 0 : _queueStatus_license_features1.max_accounts_per_batch) === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDC65\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white mb-2\",\n                                children: \"Multi-user Access Required\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-2\",\n                                children: \"Your current license doesn't include batch processing access.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"You can only process single homework assignments.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 412,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Please upgrade your license to create batches with multiple accounts.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 413,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 408,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 407,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"batches\" && (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features2 = queueStatus.license_features) === null || _queueStatus_license_features2 === void 0 ? void 0 : _queueStatus_license_features2.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white\",\n                                children: \"Your Batches\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 421,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 420,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-800\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Batch Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Priority\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-gray-900 divide-y divide-gray-700\",\n                                        children: batches.map((batch)=>{\n                                            const progressPercentage = batch.total_accounts > 0 ? Math.round(batch.processed_accounts / batch.total_accounts * 100) : 0;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-white\",\n                                                                    children: batch.batch_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between text-xs text-gray-400 mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Progress\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                    lineNumber: 460,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        progressPercentage,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                    lineNumber: 461,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-500 ease-out\",\n                                                                                style: {\n                                                                                    width: \"\".concat(progressPercentage, \"%\")\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                lineNumber: 464,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(batch.status === \"completed\" ? \"bg-green-900 text-green-300 border border-green-700\" : batch.status === \"processing\" ? \"bg-blue-900 text-blue-300 border border-blue-700\" : batch.status === \"failed\" ? \"bg-red-900 text-red-300 border border-red-700\" : \"bg-yellow-900 text-yellow-300 border border-yellow-700\"),\n                                                                children: [\n                                                                    batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    batch.status\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                batch.processed_accounts,\n                                                                                \"/\",\n                                                                                batch.total_accounts\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 text-blue-400 text-xs\",\n                                                                            children: \"processing\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.failed_accounts > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-red-400 text-xs mt-1\",\n                                                                    children: [\n                                                                        batch.failed_accounts,\n                                                                        \" failed\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 rounded-full mr-2 \".concat(batch.priority_level >= 8 ? \"bg-red-400\" : batch.priority_level >= 5 ? \"bg-yellow-400\" : \"bg-green-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.priority_level\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: new Date(batch.created_at).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs\",\n                                                                    children: new Date(batch.created_at).toLocaleTimeString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, batch.id, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 424,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 423,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 419,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"schedule\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: !(queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features3 = queueStatus.license_features) === null || _queueStatus_license_features3 === void 0 ? void 0 : _queueStatus_license_features3.scheduling_access) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCC5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-white mb-2\",\n                                    children: \"Scheduling Not Available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Your current license doesn't include scheduling access.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Please upgrade your license to use this feature.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 532,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 531,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScheduleCalendar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        schedules: schedules,\n                        onScheduleSelect: (schedule)=>{\n                            console.log(\"Selected schedule:\", schedule);\n                        // Handle schedule selection (e.g., show details modal)\n                        },\n                        onCreateSchedule: async (scheduleData)=>{\n                            try {\n                                const token = localStorage.getItem(\"token\");\n                                const response = await fetch(\"/api/queue/schedule\", {\n                                    method: \"POST\",\n                                    headers: {\n                                        \"Authorization\": \"Bearer \".concat(token),\n                                        \"Content-Type\": \"application/json\"\n                                    },\n                                    body: JSON.stringify(scheduleData)\n                                });\n                                if (response.ok) {\n                                    const result = await response.json();\n                                    alert(\"Schedule created successfully!\");\n                                    loadQueueData(); // Refresh data\n                                } else {\n                                    const error = await response.json();\n                                    alert(\"Error: \".concat(error.error));\n                                }\n                            } catch (err) {\n                                alert(\"Error: \".concat(err.message));\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 540,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 529,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"create\" && (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features4 = queueStatus.license_features) === null || _queueStatus_license_features4 === void 0 ? void 0 : _queueStatus_license_features4.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white\",\n                                children: \"Create New Batch\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 578,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 577,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleBatchSubmit,\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300\",\n                                            children: \"Batch Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: batchForm.batch_name,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        batch_name: e.target.value\n                                                    })),\n                                            className: \"mt-1 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                            children: \"Login Type\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: batchForm.login_type,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        login_type: e.target.value\n                                                    })),\n                                            className: \"mb-4 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Login Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"normal\",\n                                                    children: \"\\uD83D\\uDC64 Normal Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"google\",\n                                                    children: \"\\uD83D\\uDD0D Google Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"microsoft\",\n                                                    children: \"\\uD83C\\uDFE2 Microsoft Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                            children: \"Accounts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mb-4\",\n                                            children: \"Fill in credentials based on login type. Some login types may not require all fields.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 17\n                                        }, this),\n                                        batchForm.accounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-600 bg-gray-800 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"School\",\n                                                        value: account.school,\n                                                        onChange: (e)=>updateAccount(index, \"school\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Email/Username\",\n                                                        value: account.email,\n                                                        onChange: (e)=>updateAccount(index, \"email\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        placeholder: \"Password\",\n                                                        value: account.password,\n                                                        onChange: (e)=>updateAccount(index, \"password\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeAccount(index),\n                                                        className: \"px-3 py-2 text-red-400 border border-red-600 rounded-md hover:bg-red-900 transition-colors\",\n                                                        disabled: batchForm.accounts.length === 1,\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 19\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addAccount,\n                                            className: \"px-4 py-2 text-blue-400 border border-blue-600 rounded-md hover:bg-blue-900 transition-colors\",\n                                            disabled: (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features5 = queueStatus.license_features) === null || _queueStatus_license_features5 === void 0 ? void 0 : _queueStatus_license_features5.max_accounts_per_batch) > 0 && batchForm.accounts.length >= queueStatus.license_features.max_accounts_per_batch,\n                                            children: \"Add Account\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, this),\n                                        (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features6 = queueStatus.license_features) === null || _queueStatus_license_features6 === void 0 ? void 0 : _queueStatus_license_features6.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-1\",\n                                            children: [\n                                                \"Maximum \",\n                                                queueStatus.license_features.max_accounts_per_batch,\n                                                \" accounts per batch\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300\",\n                                            children: \"SRP Target (max 400)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"1\",\n                                            max: \"400\",\n                                            value: batchForm.srp_target,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        srp_target: parseInt(e.target.value) || 1\n                                                    })),\n                                            className: \"mt-1 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                            placeholder: \"Enter SRP target (1-400)\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-gray-400\",\n                                            children: \"Browser will automatically close when this SRP target is reached.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-400 bg-gray-800 border border-gray-600 rounded-md p-3\",\n                                        children: \"ℹ️ Batches are automatically added to the end of the queue and processed in order.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setBatchForm({\n                                                    batch_name: \"\",\n                                                    login_type: \"\",\n                                                    accounts: [\n                                                        {\n                                                            school: \"\",\n                                                            email: \"\",\n                                                            username: \"\",\n                                                            password: \"\"\n                                                        }\n                                                    ],\n                                                    srp_target: 100,\n                                                    priority_override: \"\"\n                                                }),\n                                            className: \"px-4 py-2 text-gray-300 border border-gray-600 rounded-md hover:bg-gray-800 transition-colors\",\n                                            children: \"Reset\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\",\n                                            children: \"Create Batch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 580,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 576,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_s(QueueDashboard, \"4XMH8JMWQZ4h7IzF2LT2lJHgwtA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = QueueDashboard;\nvar _c;\n$RefreshReg$(_c, \"QueueDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/queue/page.jsx\n"));

/***/ })

});