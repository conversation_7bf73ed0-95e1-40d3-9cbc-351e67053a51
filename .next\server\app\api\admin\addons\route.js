"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/addons/route";
exports.ids = ["app/api/admin/addons/route"];
exports.modules = {

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Faddons%2Froute&page=%2Fapi%2Fadmin%2Faddons%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Faddons%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Faddons%2Froute&page=%2Fapi%2Fadmin%2Faddons%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Faddons%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_admin_addons_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/admin/addons/route.js */ \"(rsc)/./app/api/admin/addons/route.js\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/addons/route\",\n        pathname: \"/api/admin/addons\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/addons/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\admin\\\\addons\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_admin_addons_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/admin/addons/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Faddons%2Froute&page=%2Fapi%2Fadmin%2Faddons%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Faddons%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/addons/route.js":
/*!***************************************!*\
  !*** ./app/api/admin/addons/route.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/database */ \"(rsc)/./lib/database.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_lib_database__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./lib/auth.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lib_auth__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(request) {\n    try {\n        const auth = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAuthManager)();\n        // Check authentication and admin role\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"No token provided\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const session = auth.validateSession(token);\n        if (session.role !== \"admin\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Return available addons/features\n        const addons = [\n            {\n                id: \"premium_support\",\n                name: \"Premium Support\",\n                description: \"Priority customer support with faster response times\"\n            },\n            {\n                id: \"bulk_operations\",\n                name: \"Bulk Operations\",\n                description: \"Process multiple items simultaneously\"\n            },\n            {\n                id: \"custom_themes\",\n                name: \"Custom Themes\",\n                description: \"Personalize the interface with custom themes\"\n            }\n        ];\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            addons\n        });\n    } catch (error) {\n        console.error(\"Get addons error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: error.message === \"Invalid session\" ? 401 : 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/addons/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-this-in-production\";\nconst TOKEN_EXPIRY = \"7d\"; // 7 days\nclass AuthManager {\n    constructor(){\n        this.db = getDatabase();\n    }\n    // Generate JWT token\n    generateToken(user) {\n        const payload = {\n            userId: user.id,\n            username: user.username,\n            role: user.role,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jwt.sign(payload, JWT_SECRET, {\n            expiresIn: TOKEN_EXPIRY\n        });\n    }\n    // Verify JWT token\n    verifyToken(token) {\n        try {\n            return jwt.verify(token, JWT_SECRET);\n        } catch (error) {\n            throw new Error(\"Invalid token\");\n        }\n    }\n    // Hash token for database storage\n    hashToken(token) {\n        return crypto.createHash(\"sha256\").update(token).digest(\"hex\");\n    }\n    // Create session with token\n    createSession(user, ipAddress = null, userAgent = null) {\n        const token = this.generateToken(user);\n        const tokenHash = this.hashToken(token);\n        // Calculate expiry date\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days\n        // Store session in database\n        this.db.createSession(user.id, tokenHash, expiresAt.toISOString(), ipAddress, userAgent);\n        // Log activity\n        this.db.logActivity(user.id, \"LOGIN\", `User logged in from ${ipAddress}`, ipAddress);\n        return {\n            token,\n            user: {\n                id: user.id,\n                username: user.username,\n                role: user.role,\n                lastLogin: user.last_login\n            },\n            expiresAt: expiresAt.toISOString()\n        };\n    }\n    // Validate session\n    validateSession(token) {\n        try {\n            // First verify JWT\n            const decoded = this.verifyToken(token);\n            // Then check database session\n            const tokenHash = this.hashToken(token);\n            const session = this.db.validateSession(tokenHash);\n            if (!session || !session.user_active) {\n                throw new Error(\"Session invalid or user inactive\");\n            }\n            return {\n                userId: session.user_id,\n                username: session.username,\n                role: session.role,\n                sessionId: session.id\n            };\n        } catch (error) {\n            throw new Error(\"Invalid session\");\n        }\n    }\n    // Logout user\n    logout(token, userId = null) {\n        const tokenHash = this.hashToken(token);\n        this.db.invalidateSession(tokenHash);\n        if (userId) {\n            this.db.logActivity(userId, \"LOGOUT\", \"User logged out\");\n        }\n    }\n    // Logout all sessions for user\n    logoutAllSessions(userId) {\n        this.db.invalidateAllUserSessions(userId);\n        this.db.logActivity(userId, \"LOGOUT_ALL\", \"All sessions invalidated\");\n    }\n    // Middleware for protecting routes\n    requireAuth(requiredRole = null) {\n        return async (req, res, next)=>{\n            try {\n                const authHeader = req.headers.authorization;\n                if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n                    return res.status(401).json({\n                        error: \"No token provided\"\n                    });\n                }\n                const token = authHeader.substring(7);\n                const session = await this.validateSession(token);\n                // Check role if required\n                if (requiredRole && session.role !== requiredRole) {\n                    return res.status(403).json({\n                        error: \"Insufficient permissions\"\n                    });\n                }\n                // Add user info to request\n                req.user = session;\n                req.token = token;\n                next();\n            } catch (error) {\n                return res.status(401).json({\n                    error: error.message\n                });\n            }\n        };\n    }\n    // Admin only middleware\n    requireAdmin() {\n        return this.requireAuth(\"admin\");\n    }\n    // Extract IP address from request (Next.js compatible)\n    getClientIP(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || req.ip || \"127.0.0.1\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"x-forwarded-for\"] || req.connection?.remoteAddress || req.socket?.remoteAddress || (req.connection?.socket ? req.connection.socket.remoteAddress : null) || \"127.0.0.1\";\n    }\n    // Extract user agent (Next.js compatible)\n    getUserAgent(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"user-agent\") || \"Unknown\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"user-agent\"] || \"Unknown\";\n    }\n    // Rate limiting helper\n    checkRateLimit(identifier, maxAttempts = 5, windowMinutes = 15) {\n        // This is a simple in-memory rate limiter\n        // In production, you might want to use Redis or database\n        if (!this.rateLimitStore) {\n            this.rateLimitStore = new Map();\n        }\n        const now = Date.now();\n        const windowMs = windowMinutes * 60 * 1000;\n        const key = `rate_limit_${identifier}`;\n        if (!this.rateLimitStore.has(key)) {\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        const record = this.rateLimitStore.get(key);\n        if (now > record.resetTime) {\n            // Reset the window\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        if (record.count >= maxAttempts) {\n            return {\n                allowed: false,\n                remaining: 0,\n                resetTime: record.resetTime\n            };\n        }\n        record.count++;\n        return {\n            allowed: true,\n            remaining: maxAttempts - record.count\n        };\n    }\n    // Clean up expired rate limit entries\n    cleanupRateLimit() {\n        if (!this.rateLimitStore) return;\n        const now = Date.now();\n        for (const [key, record] of this.rateLimitStore.entries()){\n            if (now > record.resetTime) {\n                this.rateLimitStore.delete(key);\n            }\n        }\n    }\n    // Password strength validation\n    validatePasswordStrength(password) {\n        const minLength = 8;\n        const hasUpperCase = /[A-Z]/.test(password);\n        const hasLowerCase = /[a-z]/.test(password);\n        const hasNumbers = /\\d/.test(password);\n        const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n        const errors = [];\n        if (password.length < minLength) {\n            errors.push(`Password must be at least ${minLength} characters long`);\n        }\n        if (!hasUpperCase) {\n            errors.push(\"Password must contain at least one uppercase letter\");\n        }\n        if (!hasLowerCase) {\n            errors.push(\"Password must contain at least one lowercase letter\");\n        }\n        if (!hasNumbers) {\n            errors.push(\"Password must contain at least one number\");\n        }\n        if (!hasSpecialChar) {\n            errors.push(\"Password must contain at least one special character\");\n        }\n        return {\n            isValid: errors.length === 0,\n            errors,\n            strength: this.calculatePasswordStrength(password)\n        };\n    }\n    calculatePasswordStrength(password) {\n        let score = 0;\n        // Length bonus\n        score += Math.min(password.length * 2, 20);\n        // Character variety bonus\n        if (/[a-z]/.test(password)) score += 5;\n        if (/[A-Z]/.test(password)) score += 5;\n        if (/[0-9]/.test(password)) score += 5;\n        if (/[^A-Za-z0-9]/.test(password)) score += 10;\n        // Penalty for common patterns\n        if (/(.)\\1{2,}/.test(password)) score -= 10; // Repeated characters\n        if (/123|abc|qwe/i.test(password)) score -= 10; // Sequential patterns\n        if (score < 30) return \"weak\";\n        if (score < 60) return \"medium\";\n        return \"strong\";\n    }\n}\n// Export singleton instance\nlet authInstance = null;\nfunction getAuthManager() {\n    if (!authInstance) {\n        authInstance = new AuthManager();\n    }\n    return authInstance;\n}\nmodule.exports = {\n    getAuthManager,\n    AuthManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/database.js":
/*!*************************!*\
  !*** ./lib/database.js ***!
  \*************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\nconst bcrypt = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/umd/index.js\");\nconst { v4: uuidv4 } = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/cjs/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nclass DatabaseManager {\n    constructor(){\n        const dbPath = path.join(process.cwd(), \"data\", \"app.db\");\n        this.db = new Database(dbPath);\n        this.initializeTables();\n        this.createDefaultAdmin();\n    }\n    initializeTables() {\n        // Users table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        username TEXT UNIQUE NOT NULL,\n        password_hash TEXT NOT NULL,\n        role TEXT DEFAULT 'user' CHECK(role IN ('user', 'admin')),\n        license_key_id INTEGER,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        last_login DATETIME,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id)\n      )\n    `);\n        // License keys table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_keys (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        key_code TEXT UNIQUE NOT NULL,\n        duration_days INTEGER NOT NULL,\n        max_uses INTEGER NOT NULL DEFAULT 1,\n        current_uses INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        expires_at DATETIME NOT NULL,\n        is_active BOOLEAN DEFAULT 1,\n        created_by INTEGER,\n        features TEXT DEFAULT '[]',\n        FOREIGN KEY (created_by) REFERENCES users (id)\n      )\n    `);\n        // User sessions table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS user_sessions (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        token_hash TEXT NOT NULL,\n        expires_at DATETIME NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        ip_address TEXT,\n        user_agent TEXT,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Activity logs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER,\n        action TEXT NOT NULL,\n        details TEXT,\n        ip_address TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // System settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        setting_key TEXT UNIQUE NOT NULL,\n        setting_value TEXT,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_by INTEGER,\n        FOREIGN KEY (updated_by) REFERENCES users (id)\n      )\n    `);\n        // Encrypted login credentials table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS encrypted_credentials (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        login_key TEXT UNIQUE NOT NULL,\n        login_method TEXT NOT NULL CHECK(login_method IN ('normal', 'microsoft', 'google')),\n        encrypted_school TEXT NOT NULL,\n        encrypted_email TEXT NOT NULL,\n        encrypted_password TEXT NOT NULL,\n        encryption_iv TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // License feature settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_feature_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        license_key_id INTEGER NOT NULL,\n        max_accounts_per_batch INTEGER DEFAULT 0,\n        priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),\n        scheduling_access BOOLEAN DEFAULT 0,\n        multi_user_access BOOLEAN DEFAULT 0,\n        max_batches_per_day INTEGER DEFAULT 1,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id),\n        UNIQUE(license_key_id)\n      )\n    `);\n        // Queue batches table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_batches (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        batch_name TEXT,\n        login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft')),\n        total_accounts INTEGER NOT NULL,\n        processed_accounts INTEGER DEFAULT 0,\n        failed_accounts INTEGER DEFAULT 0,\n        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        scheduled_time DATETIME,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_batches if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_batches ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue jobs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_jobs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        batch_id INTEGER,\n        user_id INTEGER NOT NULL,\n        job_type TEXT NOT NULL DEFAULT 'sparx_reader',\n        job_data TEXT NOT NULL,\n        status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        effective_priority INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        scheduled_time DATETIME,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        error_message TEXT,\n        retry_count INTEGER DEFAULT 0,\n        max_retries INTEGER DEFAULT 3,\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id),\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_jobs if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_jobs ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue schedules table for conflict detection\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_schedules (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        scheduled_time DATETIME NOT NULL,\n        duration_minutes INTEGER DEFAULT 30,\n        srp_target INTEGER DEFAULT 100,\n        job_id INTEGER,\n        batch_id INTEGER,\n        status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (job_id) REFERENCES queue_jobs (id),\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id)\n      )\n    `);\n        // Add srp_target column if it doesn't exist (for existing databases)\n        try {\n            this.db.exec(`ALTER TABLE queue_schedules ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Create indexes for better performance\n        this.db.exec(`\n      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n      CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_code ON license_keys(key_code);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_expires ON license_keys(expires_at);\n      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);\n      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_created ON activity_logs(created_at);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_user ON encrypted_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_key ON encrypted_credentials(login_key);\n      CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time);\n    `);\n        // Migration: Add max_batches_per_day column if it doesn't exist\n        try {\n            const columns = this.db.prepare(\"PRAGMA table_info(license_feature_settings)\").all();\n            const hasMaxBatchesPerDay = columns.some((col)=>col.name === \"max_batches_per_day\");\n            if (!hasMaxBatchesPerDay) {\n                console.log(\"Adding max_batches_per_day column to license_feature_settings...\");\n                this.db.exec(`ALTER TABLE license_feature_settings ADD COLUMN max_batches_per_day INTEGER DEFAULT 1`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for max_batches_per_day:\", error);\n        }\n        // Migration: Add login_type column to queue_batches if it doesn't exist\n        try {\n            const batchColumns = this.db.prepare(\"PRAGMA table_info(queue_batches)\").all();\n            const hasLoginType = batchColumns.some((col)=>col.name === \"login_type\");\n            if (!hasLoginType) {\n                console.log(\"Adding login_type column to queue_batches...\");\n                this.db.exec(`ALTER TABLE queue_batches ADD COLUMN login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft'))`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for login_type:\", error);\n        }\n    }\n    createDefaultAdmin() {\n        const adminExists = this.db.prepare(\"SELECT id FROM users WHERE role = ? LIMIT 1\").get(\"admin\");\n        if (!adminExists) {\n            const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);\n            const stmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, role, is_active)\n        VALUES (?, ?, ?, ?)\n      `);\n            stmt.run(process.env.DEFAULT_ADMIN_USERNAME, hashedPassword, \"admin\", 1);\n            console.log(`Default admin user created: ${process.env.DEFAULT_ADMIN_USERNAME}/[hidden]`);\n        }\n    }\n    // User management methods\n    createUser(username, password, licenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // Validate license key\n            const licenseStmt = this.db.prepare(`\n        SELECT id, max_uses, current_uses, expires_at, is_active \n        FROM license_keys \n        WHERE key_code = ? AND is_active = 1\n      `);\n            const license = licenseStmt.get(licenseKey);\n            if (!license) {\n                throw new Error(\"Invalid license key\");\n            }\n            if (new Date(license.expires_at) < new Date()) {\n                throw new Error(\"License key has expired\");\n            }\n            if (license.current_uses >= license.max_uses) {\n                throw new Error(\"License key has reached maximum uses\");\n            }\n            // Check if username already exists\n            const userExists = this.db.prepare(\"SELECT id FROM users WHERE username = ?\").get(username);\n            if (userExists) {\n                throw new Error(\"Username already exists\");\n            }\n            // Create user\n            const hashedPassword = bcrypt.hashSync(password, 12);\n            const userStmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, license_key_id, role)\n        VALUES (?, ?, ?, ?)\n      `);\n            const result = userStmt.run(username, hashedPassword, license.id, \"user\");\n            // Update license key usage\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(license.id);\n            return result.lastInsertRowid;\n        });\n        return transaction();\n    }\n    authenticateUser(username, password) {\n        const stmt = this.db.prepare(`\n      SELECT u.*, lk.expires_at as license_expires, lk.is_active as license_active\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.username = ? AND u.is_active = 1\n    `);\n        const user = stmt.get(username);\n        if (!user) {\n            throw new Error(\"Invalid credentials\");\n        }\n        const isValidPassword = bcrypt.compareSync(password, user.password_hash);\n        if (!isValidPassword) {\n            throw new Error(\"Invalid credentials\");\n        }\n        // Check license validity for non-admin users\n        if (user.role !== \"admin\") {\n            if (!user.license_active || new Date(user.license_expires) < new Date()) {\n                throw new Error(\"License has expired\");\n            }\n        }\n        // Update last login\n        const updateStmt = this.db.prepare(\"UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?\");\n        updateStmt.run(user.id);\n        // Remove sensitive data\n        delete user.password_hash;\n        return user;\n    }\n    // License key management methods\n    createLicenseKey(durationDays, maxUses = 1, features = [], createdBy = null) {\n        const keyCode = this.generateLicenseKey();\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + durationDays);\n        const stmt = this.db.prepare(`\n      INSERT INTO license_keys (key_code, duration_days, max_uses, expires_at, created_by, features)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(keyCode, durationDays, maxUses, expiresAt.toISOString(), createdBy, JSON.stringify(features));\n        return {\n            id: result.lastInsertRowid,\n            keyCode,\n            durationDays,\n            maxUses,\n            expiresAt: expiresAt.toISOString(),\n            features\n        };\n    }\n    generateLicenseKey() {\n        const segments = [];\n        for(let i = 0; i < 4; i++){\n            segments.push(uuidv4().replace(/-/g, \"\").substring(0, 8).toUpperCase());\n        }\n        return `SRX-${segments.join(\"-\")}`;\n    }\n    getLicenseKeys(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        lk.*,\n        u.username as created_by_username,\n        COUNT(users.id) as users_count\n      FROM license_keys lk\n      LEFT JOIN users u ON lk.created_by = u.id\n      LEFT JOIN users ON users.license_key_id = lk.id\n      GROUP BY lk.id\n      ORDER BY lk.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    deactivateLicenseKey(keyId) {\n        const stmt = this.db.prepare(\"UPDATE license_keys SET is_active = 0 WHERE id = ?\");\n        return stmt.run(keyId);\n    }\n    // Get detailed license information for a user\n    getUserLicenseStatus(userId) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id as user_id,\n        u.username,\n        lk.id as license_id,\n        lk.key_code,\n        lk.max_uses,\n        lk.current_uses,\n        lk.expires_at,\n        lk.is_active,\n        CASE \n          WHEN lk.expires_at <= datetime('now') THEN 'expired'\n          WHEN lk.current_uses >= lk.max_uses THEN 'maxed_out'\n          WHEN lk.is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as license_status\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.id = ?\n    `);\n        return stmt.get(userId);\n    }\n    // Validate a license key for renewal (check if it's valid and has available uses)\n    validateLicenseForRenewal(licenseKey) {\n        const stmt = this.db.prepare(`\n      SELECT \n        id,\n        key_code,\n        max_uses,\n        current_uses,\n        expires_at,\n        is_active,\n        CASE \n          WHEN expires_at <= datetime('now') THEN 'expired'\n          WHEN current_uses >= max_uses THEN 'maxed_out'\n          WHEN is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as status\n      FROM license_keys \n      WHERE key_code = ?\n    `);\n        const license = stmt.get(licenseKey);\n        if (!license) {\n            return {\n                valid: false,\n                error: \"License key not found\"\n            };\n        }\n        if (license.status !== \"valid\") {\n            let errorMessage = \"License key is not valid\";\n            switch(license.status){\n                case \"expired\":\n                    errorMessage = \"License key has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"License key has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"License key is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage\n            };\n        }\n        return {\n            valid: true,\n            license\n        };\n    }\n    // Renew user's license with a new license key\n    renewUserLicense(userId, newLicenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // First validate the new license key\n            const validation = this.validateLicenseForRenewal(newLicenseKey);\n            if (!validation.valid) {\n                throw new Error(validation.error);\n            }\n            const newLicense = validation.license;\n            // Update user's license_key_id to the new license\n            const updateUserStmt = this.db.prepare(`\n        UPDATE users \n        SET license_key_id = ?, updated_at = CURRENT_TIMESTAMP \n        WHERE id = ?\n      `);\n            updateUserStmt.run(newLicense.id, userId);\n            // Increment the new license's current_uses\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(newLicense.id);\n            // Log the renewal activity\n            this.logActivity(userId, \"LICENSE_RENEWED\", `License renewed with key: ${newLicenseKey}`);\n            return {\n                success: true,\n                newLicenseId: newLicense.id,\n                newLicenseKey: newLicenseKey,\n                expiresAt: newLicense.expires_at,\n                maxUses: newLicense.max_uses,\n                currentUses: newLicense.current_uses + 1\n            };\n        });\n        return transaction();\n    }\n    // Session management\n    createSession(userId, tokenHash, expiresAt, ipAddress = null, userAgent = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, tokenHash, expiresAt, ipAddress, userAgent);\n    }\n    validateSession(tokenHash) {\n        const stmt = this.db.prepare(`\n      SELECT s.*, u.username, u.role, u.is_active as user_active\n      FROM user_sessions s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')\n    `);\n        return stmt.get(tokenHash);\n    }\n    invalidateSession(tokenHash) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?\");\n        return stmt.run(tokenHash);\n    }\n    invalidateAllUserSessions(userId) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE user_id = ?\");\n        return stmt.run(userId);\n    }\n    // Activity logging\n    logActivity(userId, action, details = null, ipAddress = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO activity_logs (user_id, action, details, ip_address)\n      VALUES (?, ?, ?, ?)\n    `);\n        return stmt.run(userId, action, details, ipAddress);\n    }\n    // Cleanup expired keys older than 30 days\n    cleanupExpiredKeys() {\n        try {\n            const stmt = this.db.prepare(`\n        DELETE FROM license_keys\n        WHERE status = 'expired'\n        AND datetime(expires_at) < datetime('now', '-30 days')\n      `);\n            const result = stmt.run();\n            if (result.changes > 0) {\n                console.log(`🧹 Cleaned up ${result.changes} expired license keys older than 30 days`);\n                this.logActivity(null, \"SYSTEM_CLEANUP\", `Removed ${result.changes} expired keys older than 30 days`);\n            }\n            return result.changes;\n        } catch (error) {\n            console.error(\"Error cleaning up expired keys:\", error);\n            return 0;\n        }\n    }\n    getActivityLogs(userId = null, limit = 100, offset = 0) {\n        let query = `\n      SELECT \n        al.*,\n        u.username\n      FROM activity_logs al\n      LEFT JOIN users u ON al.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE al.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY al.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Analytics methods\n    getSystemStats() {\n        const stats = {};\n        // Total users\n        stats.totalUsers = this.db.prepare(\"SELECT COUNT(*) as count FROM users WHERE role = 'user'\").get().count;\n        // Active users (logged in within last 30 days)\n        stats.activeUsers = this.db.prepare(`\n      SELECT COUNT(*) as count FROM users \n      WHERE role = 'user' AND last_login > datetime('now', '-30 days')\n    `).get().count;\n        // Total license keys\n        stats.totalLicenseKeys = this.db.prepare(\"SELECT COUNT(*) as count FROM license_keys\").get().count;\n        // Active license keys\n        stats.activeLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE is_active = 1 AND expires_at > datetime('now')\n    `).get().count;\n        // Expired license keys\n        stats.expiredLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE expires_at <= datetime('now')\n    `).get().count;\n        // Recent activity (last 24 hours)\n        stats.recentActivity = this.db.prepare(`\n      SELECT COUNT(*) as count FROM activity_logs \n      WHERE created_at > datetime('now', '-1 day')\n    `).get().count;\n        return stats;\n    }\n    // User management for admin\n    getUsers(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id,\n        u.username,\n        u.role,\n        u.created_at,\n        u.last_login,\n        u.is_active,\n        lk.key_code,\n        lk.expires_at as license_expires\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    toggleUserStatus(userId) {\n        const stmt = this.db.prepare(\"UPDATE users SET is_active = NOT is_active WHERE id = ?\");\n        return stmt.run(userId);\n    }\n    // Cleanup methods\n    cleanupExpiredSessions() {\n        const stmt = this.db.prepare('DELETE FROM user_sessions WHERE expires_at <= datetime(\"now\")');\n        return stmt.run();\n    }\n    cleanupOldLogs(daysToKeep = 90) {\n        const stmt = this.db.prepare(`\n      DELETE FROM activity_logs \n      WHERE created_at <= datetime('now', '-${daysToKeep} days')\n    `);\n        return stmt.run();\n    }\n    // Encrypted credentials methods\n    saveEncryptedCredentials(userId, loginMethod, school, email, password, encryptionKey) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        // Generate a unique login key\n        const loginKey = \"SLK-\" + crypto.randomBytes(8).toString(\"hex\").toUpperCase();\n        // Create encryption IV\n        const iv = crypto.randomBytes(16);\n        const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n        // Encrypt school, email and password\n        const cipher1 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedSchool = cipher1.update(school, \"utf8\", \"hex\") + cipher1.final(\"hex\");\n        const cipher2 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedEmail = cipher2.update(email, \"utf8\", \"hex\") + cipher2.final(\"hex\");\n        const cipher3 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedPassword = cipher3.update(password, \"utf8\", \"hex\") + cipher3.final(\"hex\");\n        const stmt = this.db.prepare(`\n      INSERT INTO encrypted_credentials (user_id, login_key, login_method, encrypted_school, encrypted_email, encrypted_password, encryption_iv)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        stmt.run(userId, loginKey, loginMethod, encryptedSchool, encryptedEmail, encryptedPassword, iv.toString(\"hex\"));\n        return loginKey;\n    }\n    getEncryptedCredentials(loginKey, encryptionKey) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM encrypted_credentials \n      WHERE login_key = ? AND is_active = 1\n    `);\n        const result = stmt.get(loginKey);\n        if (!result) return null;\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        try {\n            const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n            const iv = Buffer.from(result.encryption_iv, \"hex\");\n            // Decrypt school, email and password\n            const decipher1 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const school = result.encrypted_school ? decipher1.update(result.encrypted_school, \"hex\", \"utf8\") + decipher1.final(\"utf8\") : null;\n            const decipher2 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const email = result.encrypted_email ? decipher2.update(result.encrypted_email, \"hex\", \"utf8\") + decipher2.final(\"utf8\") : null;\n            const decipher3 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const password = result.encrypted_password ? decipher3.update(result.encrypted_password, \"hex\", \"utf8\") + decipher3.final(\"utf8\") : null;\n            return {\n                loginMethod: result.login_method,\n                school,\n                email,\n                password,\n                userId: result.user_id\n            };\n        } catch (error) {\n            console.error(\"Failed to decrypt credentials:\", error);\n            return null;\n        }\n    }\n    getUserCredentials(userId) {\n        const stmt = this.db.prepare(`\n      SELECT login_key, login_method, created_at FROM encrypted_credentials \n      WHERE user_id = ? AND is_active = 1\n      ORDER BY created_at DESC\n    `);\n        return stmt.all(userId);\n    }\n    deactivateCredentials(loginKey) {\n        const stmt = this.db.prepare(\"UPDATE encrypted_credentials SET is_active = 0 WHERE login_key = ?\");\n        return stmt.run(loginKey);\n    }\n    // License Feature Settings Methods\n    setLicenseFeatures(licenseKeyId, features) {\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO license_feature_settings\n      (license_key_id, max_accounts_per_batch, priority_level, scheduling_access, max_batches_per_day, updated_at)\n      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(licenseKeyId, features.max_accounts_per_batch || 0, features.priority_level || 0, features.scheduling_access ? 1 : 0, features.max_batches_per_day || 1);\n    }\n    getLicenseFeatures(licenseKeyId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM license_feature_settings WHERE license_key_id = ?\n    `);\n        const result = stmt.get(licenseKeyId);\n        if (!result) {\n            // Return default features if none set\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    getUserLicenseFeatures(userId) {\n        const stmt = this.db.prepare(`\n      SELECT lfs.* FROM license_feature_settings lfs\n      JOIN users u ON u.license_key_id = lfs.license_key_id\n      WHERE u.id = ?\n    `);\n        const result = stmt.get(userId);\n        if (!result) {\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    // Daily batch count check\n    getUserDailyBatchCount(userId, date = null) {\n        const targetDate = date || new Date().toISOString().split(\"T\")[0]; // YYYY-MM-DD format\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_batches\n      WHERE user_id = ?\n      AND DATE(created_at) = ?\n    `);\n        const result = stmt.get(userId, targetDate);\n        return result.count;\n    }\n    // Weekly schedule count check\n    getUserWeeklyScheduleCount(userId) {\n        // Get the start of the current week (Monday)\n        const now = new Date();\n        const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.\n        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // If Sunday, go back 6 days to Monday\n        const startOfWeek = new Date(now);\n        startOfWeek.setDate(now.getDate() - daysToMonday);\n        startOfWeek.setHours(0, 0, 0, 0);\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_schedules\n      WHERE user_id = ?\n      AND created_at >= ?\n      AND status != 'cancelled'\n    `);\n        const result = stmt.get(userId, startOfWeek.toISOString());\n        return result.count;\n    }\n    // Queue Batch Methods\n    createQueueBatch(userId, batchName, accounts, scheduledTime = null, loginType = \"normal\", srpTarget = 100) {\n        const transaction = this.db.transaction(()=>{\n            // Get user's license features\n            const features = this.getUserLicenseFeatures(userId);\n            // Check daily batch limit\n            const dailyBatchCount = this.getUserDailyBatchCount(userId);\n            if (dailyBatchCount >= features.max_batches_per_day) {\n                throw new Error(`Daily batch limit reached (${features.max_batches_per_day} batches per day). Please try again tomorrow.`);\n            }\n            // Validate batch size against license limits\n            if (features.max_accounts_per_batch > 0 && accounts.length > features.max_accounts_per_batch) {\n                throw new Error(`Batch size (${accounts.length}) exceeds license limit (${features.max_accounts_per_batch})`);\n            }\n            // Validate scheduling access\n            if (scheduledTime && !features.scheduling_access) {\n                throw new Error(\"Scheduling access not available for this license\");\n            }\n            // Validate login type\n            if (![\n                \"normal\",\n                \"google\",\n                \"microsoft\"\n            ].includes(loginType)) {\n                throw new Error(\"Invalid login type specified\");\n            }\n            // Validate SRP target\n            if (srpTarget < 1 || srpTarget > 400) {\n                throw new Error(\"SRP target must be between 1 and 400\");\n            }\n            // Create batch\n            const batchStmt = this.db.prepare(`\n        INSERT INTO queue_batches (user_id, batch_name, login_type, total_accounts, priority_level, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            const batchResult = batchStmt.run(userId, batchName, loginType, accounts.length, features.priority_level, srpTarget, scheduledTime);\n            const batchId = batchResult.lastInsertRowid;\n            // Create individual jobs for each account\n            const jobStmt = this.db.prepare(`\n        INSERT INTO queue_jobs (batch_id, user_id, job_data, priority_level, effective_priority, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            accounts.forEach((account)=>{\n                const effectivePriority = this.calculateEffectivePriority(features.priority_level, scheduledTime);\n                jobStmt.run(batchId, userId, JSON.stringify(account), features.priority_level, effectivePriority, srpTarget, scheduledTime);\n            });\n            // Create schedule entry if scheduled\n            if (scheduledTime) {\n                this.createScheduleEntry(userId, scheduledTime, null, batchId, 30, srpTarget);\n            }\n            // Log activity\n            this.logActivity(userId, \"BATCH_CREATED\", `Created batch: ${batchName} with ${accounts.length} accounts`);\n            return batchId;\n        });\n        return transaction();\n    }\n    calculateEffectivePriority(basePriority, scheduledTime) {\n        let effectivePriority = basePriority;\n        // Boost priority for scheduled jobs approaching their time\n        if (scheduledTime) {\n            const now = new Date();\n            const scheduled = new Date(scheduledTime);\n            const timeDiff = scheduled.getTime() - now.getTime();\n            const hoursUntil = timeDiff / (1000 * 60 * 60);\n            if (hoursUntil <= 1) {\n                effectivePriority += 5; // High boost for jobs due within an hour\n            } else if (hoursUntil <= 6) {\n                effectivePriority += 2; // Medium boost for jobs due within 6 hours\n            }\n        }\n        // Apply starvation prevention (boost priority for old jobs)\n        // This would be implemented in a background process\n        return Math.min(effectivePriority, 10); // Cap at maximum priority\n    }\n    getQueueBatches(userId = null, status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT qb.*, u.username\n      FROM queue_batches qb\n      JOIN users u ON qb.user_id = u.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (userId) {\n            query += \" AND qb.user_id = ?\";\n            params.push(userId);\n        }\n        if (status) {\n            query += \" AND qb.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY qb.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getBatchJobs(batchId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_jobs \n      WHERE batch_id = ? \n      ORDER BY effective_priority DESC, created_at ASC\n    `);\n        return stmt.all(batchId);\n    }\n    updateBatchStatus(batchId, status, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_batches\n      SET status = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, completedAt, batchId);\n    }\n    // Queue Job Methods\n    getNextQueueJob() {\n        const stmt = this.db.prepare(`\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n      WHERE qj.status = 'queued' \n      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))\n      ORDER BY qj.effective_priority DESC, qj.created_at ASC\n      LIMIT 1\n    `);\n        return stmt.get();\n    }\n    updateJobStatus(jobId, status, errorMessage = null, startedAt = null, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET status = ?, error_message = ?, started_at = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, errorMessage, startedAt, completedAt, jobId);\n    }\n    incrementJobRetry(jobId) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET retry_count = retry_count + 1, status = 'queued'\n      WHERE id = ? AND retry_count < max_retries\n    `);\n        return stmt.run(jobId);\n    }\n    // Scheduling Methods\n    createScheduleEntry(userId, scheduledTime, jobId = null, batchId = null, durationMinutes = 30, srpTarget = 100) {\n        // Check for conflicts\n        const conflicts = this.checkScheduleConflicts(userId, scheduledTime, durationMinutes);\n        if (conflicts.length > 0) {\n            throw new Error(`Schedule conflict detected at ${scheduledTime}`);\n        }\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_schedules (user_id, scheduled_time, duration_minutes, srp_target, job_id, batch_id)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, scheduledTime, durationMinutes, srpTarget, jobId, batchId);\n    }\n    checkScheduleConflicts(userId, scheduledTime, durationMinutes) {\n        const startTime = new Date(scheduledTime);\n        const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_schedules\n      WHERE user_id = ? \n      AND status IN ('scheduled', 'active')\n      AND (\n        (scheduled_time <= ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') > ?) OR\n        (scheduled_time < ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') >= ?)\n      )\n    `);\n        return stmt.all(userId, startTime.toISOString(), startTime.toISOString(), endTime.toISOString(), endTime.toISOString());\n    }\n    getUserSchedules(userId, startDate = null, endDate = null) {\n        let query = `\n      SELECT qs.*, qj.job_type, qb.batch_name\n      FROM queue_schedules qs\n      LEFT JOIN queue_jobs qj ON qs.job_id = qj.id\n      LEFT JOIN queue_batches qb ON qs.batch_id = qb.id\n      WHERE qs.user_id = ?\n    `;\n        const params = [\n            userId\n        ];\n        if (startDate) {\n            query += \" AND qs.scheduled_time >= ?\";\n            params.push(startDate);\n        }\n        if (endDate) {\n            query += \" AND qs.scheduled_time <= ?\";\n            params.push(endDate);\n        }\n        query += \" ORDER BY qs.scheduled_time ASC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Priority Management Methods\n    updateJobPriority(jobId, newPriority, adminOverride = false) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = ?, priority_level = ?\n      WHERE id = ?\n    `);\n        const result = stmt.run(newPriority, adminOverride ? newPriority : null, jobId);\n        if (adminOverride) {\n            this.logActivity(null, \"ADMIN_PRIORITY_OVERRIDE\", `Job ${jobId} priority set to ${newPriority}`);\n        }\n        return result;\n    }\n    applyStarvationPrevention() {\n        // Boost priority for jobs that have been waiting too long\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = CASE \n        WHEN datetime('now') > datetime(created_at, '+2 hours') THEN MIN(effective_priority + 2, 10)\n        WHEN datetime('now') > datetime(created_at, '+1 hour') THEN MIN(effective_priority + 1, 10)\n        ELSE effective_priority\n      END\n      WHERE status = 'queued'\n    `);\n        return stmt.run();\n    }\n    getQueueStats() {\n        const stats = {};\n        // Total jobs by status\n        const statusStmt = this.db.prepare(`\n      SELECT status, COUNT(*) as count \n      FROM queue_jobs \n      GROUP BY status\n    `);\n        stats.jobsByStatus = statusStmt.all();\n        // Jobs by priority level\n        const priorityStmt = this.db.prepare(`\n      SELECT effective_priority, COUNT(*) as count \n      FROM queue_jobs \n      WHERE status = 'queued'\n      GROUP BY effective_priority\n      ORDER BY effective_priority DESC\n    `);\n        stats.jobsByPriority = priorityStmt.all();\n        // Average wait time\n        const waitTimeStmt = this.db.prepare(`\n      SELECT AVG(julianday(started_at) - julianday(created_at)) * 24 * 60 as avg_wait_minutes\n      FROM queue_jobs \n      WHERE started_at IS NOT NULL\n    `);\n        stats.averageWaitTime = waitTimeStmt.get()?.avg_wait_minutes || 0;\n        return stats;\n    }\n    close() {\n        this.db.close();\n    }\n}\n// Create data directory if it doesn't exist\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst dataDir = path.join(process.cwd(), \"data\");\nif (!fs.existsSync(dataDir)) {\n    fs.mkdirSync(dataDir, {\n        recursive: true\n    });\n}\n// Export singleton instance\nlet dbInstance = null;\nfunction getDatabase() {\n    if (!dbInstance) {\n        dbInstance = new DatabaseManager();\n    }\n    return dbInstance;\n}\nmodule.exports = {\n    getDatabase,\n    DatabaseManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/uuid","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Faddons%2Froute&page=%2Fapi%2Fadmin%2Faddons%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Faddons%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();