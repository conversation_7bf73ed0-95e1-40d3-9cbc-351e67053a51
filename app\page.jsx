'use client'

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function SparxReaderPage() {
  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  const router = useRouter();
  const [message, setMessage] = useState('');
  const [bookTitle, setBookTitle] = useState('');
  const [needsPlaywright, setNeedsPlaywright] = useState(false);
  const [loading, setLoading] = useState(false);
  const [screenshot, setScreenshot] = useState('');
  const [storyContent, setStoryContent] = useState('');
  const [showBookConfirmation, setShowBookConfirmation] = useState(false);
  const [currentSrp, setCurrentSrp] = useState('');
  const [targetSrp, setTargetSrp] = useState('');
  const [showInitialSrpInput, setShowInitialSrpInput] = useState(false);
  
  // New state for enhanced UI
  const [currentQuestion, setCurrentQuestion] = useState('');
  const [currentAnswer, setCurrentAnswer] = useState('');
  const [questionNumber, setQuestionNumber] = useState(0);
  const [srpEarned, setSrpEarned] = useState(0);
  const [isAutomationRunning, setIsAutomationRunning] = useState(false);
  const [questionHistory, setQuestionHistory] = useState([]);
  const [animationKey, setAnimationKey] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const [automationComplete, setAutomationComplete] = useState(false);
  const [queuePosition, setQueuePosition] = useState(null);
  const [jobId, setJobId] = useState(null);
  const [isInQueue, setIsInQueue] = useState(false);
  const [loginMethod, setLoginMethod] = useState('normal'); // 'normal' or 'microsoft'
  
  // Credential system states
  const [showCredentialInput, setShowCredentialInput] = useState(false);
  const [credentialMode, setCredentialMode] = useState('enter'); // 'enter' or 'key'
  const [userSchool, setUserSchool] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [userPassword, setUserPassword] = useState('');
  const [loginKey, setLoginKey] = useState('');
  const [savedCredentials, setSavedCredentials] = useState([]);

  // License renewal states
  const [showLicenseRenewal, setShowLicenseRenewal] = useState(false);
  const [licenseStatus, setLicenseStatus] = useState(null);
  const [newLicenseKey, setNewLicenseKey] = useState('');
  const [licenseRenewalLoading, setLicenseRenewalLoading] = useState(false);

  // Check authentication on component mount
  useEffect(() => {
    checkAuthentication();
  }, []);

  // Simulate question-solving process AFTER automation completes
  useEffect(() => {
    if (!isAuthenticated || !isAutomationRunning || !automationComplete) return;
    
    if (isAutomationRunning && automationComplete) {
      const interval = setInterval(() => {
        // Simulate question solving progress
        setQuestionNumber(prev => {
          const newNum = prev + 1;
          setSrpEarned(prevSrp => prevSrp + Math.floor(Math.random() * 3) + 2);
          setAnimationKey(prevKey => prevKey + 1);
          
          // Simulate new question
          const sampleQuestions = [
            "What was the main character's motivation in chapter 3?",
            "How did the setting influence the story's outcome?",
            "What literary device was used in the opening paragraph?",
            "Why did the protagonist make that crucial decision?",
            "What theme is most prominent throughout the narrative?",
            "How does the author develop the central conflict?",
            "What role does symbolism play in the narrative?",
            "are these questions fake and is your homework already complete?"
          ];
          
          const sampleAnswers = [
            "To find their lost family member",
            "The harsh winter created urgency",
            "Metaphor and symbolism",
            "To protect their friends",
            "The importance of friendship",
            "Through escalating tension",
            "It reinforces the main themes",
            "yes lol"
          ];
          
          const randomIndex = Math.floor(Math.random() * sampleQuestions.length);
          setCurrentQuestion(sampleQuestions[randomIndex]);
          setCurrentAnswer(sampleAnswers[randomIndex]);
          
          // Add to history
          setQuestionHistory(prev => [...prev, {
            number: newNum,
            question: sampleQuestions[randomIndex],
            answer: sampleAnswers[randomIndex]
          }]);
          
          // Stop after reaching target or max questions
          if (newNum >= 8 || srpEarned >= parseInt(targetSrp || 50)) {
            setTimeout(() => {
              setIsAutomationRunning(false);
              setMessage('Target SRP reached! Automation completed successfully.');
            }, 1500); // Show the last question for a bit
            clearInterval(interval);
            return newNum;
          }
          
          return newNum;
        });
      }, 2500); // Show new question every 2.5 seconds
      
      return () => clearInterval(interval);
    }
  }, [isAuthenticated, isAutomationRunning, automationComplete, srpEarned, targetSrp]);

  // Authentication functions
  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        router.push('/login');
        return;
      }

      const response = await fetch('/api/auth/validate', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success && data.valid) {
        setIsAuthenticated(true);
        setUser(data.user);
        setLicenseStatus(data.licenseStatus);
      } else {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        router.push('/login');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      router.push('/login');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      router.push('/login');
    }
  };

  // Show loading screen while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent"></div>
        <div className="relative z-10 text-center">
          <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Don't render main content if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Handle login method selection (doesn't start the process)
  const handleLoginMethodSelect = (method) => {
    setLoginMethod(method);
  };

  // Check license validity before starting bot
  const checkLicenseValidity = () => {
    // Admin users don't need license validation
    if (user && user.role === 'admin') {
      return { valid: true };
    }

    if (!licenseStatus) {
      return { valid: false, error: 'License information not available' };
    }

    if (licenseStatus.license_status !== 'valid') {
      let errorMessage = 'Your license is not valid';
      switch (licenseStatus.license_status) {
        case 'expired':
          errorMessage = 'Your license has expired';
          break;
        case 'maxed_out':
          errorMessage = 'Your license has reached maximum uses';
          break;
        case 'inactive':
          errorMessage = 'Your license is inactive';
          break;
      }
      return { valid: false, error: errorMessage, status: licenseStatus.license_status };
    }

    return { valid: true };
  };

  // Handle license renewal
  const handleLicenseRenewal = async () => {
    if (!newLicenseKey.trim()) {
      setMessage('Please enter a valid license key');
      return;
    }

    setLicenseRenewalLoading(true);
    setMessage('Renewing license...');

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/auth/renew-license', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ newLicenseKey: newLicenseKey.trim() })
      });

      const data = await response.json();

      if (data.success) {
        setMessage('License renewed successfully! You can now start the bot.');
        setShowLicenseRenewal(false);
        setNewLicenseKey('');
        
        // Refresh authentication to get updated license status
        await checkAuthentication();
      } else {
        setMessage(data.error || 'Failed to renew license');
      }
    } catch (error) {
      setMessage('Error occurred while renewing license');
    } finally {
      setLicenseRenewalLoading(false);
    }
  };

  // Handle the actual start process
  const handleBeginClick = async () => {
    // Check authentication before starting
    if (!isAuthenticated || !user) {
      setMessage('Please login to use this feature');
      router.push('/login');
      return;
    }

    // Check license validity before proceeding
    const licenseCheck = checkLicenseValidity();
    if (!licenseCheck.valid) {
      setMessage(licenseCheck.error);
      setShowLicenseRenewal(true);
      return;
    }

    // Reset all states
    setMessage('');
    setBookTitle('');
    setNeedsPlaywright(false);
    setScreenshot('');
    setStoryContent('');
    setShowBookConfirmation(false);
    setCurrentSrp('');
    setTargetSrp('');
    setShowInitialSrpInput(false);
    setAutomationComplete(false);
    
    // Reset credential states
    setUserSchool('');
    setUserEmail('');
    setUserPassword('');
    setLoginKey('');
    
    // Mark that user has started the process
    setHasStarted(true);
    
    // Show credential input first
    setShowCredentialInput(true);
    
    // Load user's saved credentials
    await loadSavedCredentials();
  };

  // Load user's saved credentials
  const loadSavedCredentials = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/credentials/list', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      
      if (data.success) {
        setSavedCredentials(data.credentials);
      }
    } catch (error) {
      console.error('Failed to load saved credentials:', error);
    }
  };

  // Handle credential submission
  const handleCredentialSubmit = async () => {
    if (credentialMode === 'key') {
      // Use saved credentials with login key
      if (!loginKey) {
        setMessage('Please enter your login key');
        return;
      }
      
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/credentials/get', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ loginKey })
        });
        
        const data = await response.json();
        
        if (data.success) {
          // Proceed with automation using saved credentials
          setShowCredentialInput(false);
          setMessage('Please enter how much SRP you need to earn:');
          setShowInitialSrpInput(true);
        } else {
          setMessage(data.error || 'Invalid login key');
        }
      } catch (error) {
        setMessage('Error retrieving credentials');
      }
    } else {
      // Use entered credentials
      if (!userSchool || !userEmail || !userPassword) {
        setMessage('Please enter school, email and password');
        return;
      }
      
      // Ask if user wants to save credentials
      if (confirm('Would you like to save these credentials for future use? You will receive a secure login key.')) {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch('/api/credentials/save', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              loginMethod,
              school: userSchool,
              email: userEmail,
              password: userPassword
            })
          });
          
          const data = await response.json();
          
          if (data.success) {
            alert(`Credentials saved! Your login key is: ${data.loginKey}\n\nPlease save this key securely. You can use it for future logins.`);
          }
        } catch (error) {
          console.error('Failed to save credentials:', error);
        }
      }
      
      // Proceed with automation
      setShowCredentialInput(false);
      setMessage('Please enter how much SRP you need to earn:');
      setShowInitialSrpInput(true);
    }
  };





  const handleSrpSubmit = async () => {
    // Validate SRP input
    if (!targetSrp || isNaN(targetSrp) || parseInt(targetSrp) <= 0) {
      setMessage('Please enter a valid SRP target (positive number)');
      return;
    }

    // Check license validity again before starting automation
    const licenseCheck = checkLicenseValidity();
    if (!licenseCheck.valid) {
      setMessage(licenseCheck.error);
      setShowLicenseRenewal(true);
      setShowInitialSrpInput(false);
      return;
    }

    setLoading(true);
    setShowInitialSrpInput(false);
    const isNormalLogin = loginMethod === 'normal';
    const isMicrosoftLogin = loginMethod === 'microsoft';
    const isGoogleLogin = loginMethod === 'google';
    
    // Get credentials
    let credentials = null;
    if (credentialMode === 'key' && loginKey) {
      // Get credentials from login key
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/credentials/get', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ loginKey })
        });
        
        const data = await response.json();
        if (data.success) {
          credentials = data.credentials;
        } else {
          setLoading(false);
          setMessage(data.error || 'Failed to retrieve credentials');
          return;
        }
      } catch (error) {
        setLoading(false);
        setMessage('Error retrieving credentials');
        return;
      }
    } else if (credentialMode === 'enter' && userSchool && userEmail && userPassword) {
      // Use entered credentials
      credentials = {
        school: userSchool,
        email: userEmail,
        password: userPassword,
        loginMethod: loginMethod
      };
    } else {
      setLoading(false);
      setMessage('No credentials available');
      return;
    }
    
    if (isNormalLogin) {
      setMessage('Preparing to start...');
    } else if (isMicrosoftLogin) {
      setMessage('Starting Microsoft login automation...');
    } else if (isGoogleLogin) {
      setMessage('Starting Google login automation...');
    }
    
    try {
      let apiEndpoint, requestBody;
      
      if (isNormalLogin) {
        apiEndpoint = '/api/sparxreader/start';
        requestBody = { 
          url: 'https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1',
          targetSrp: parseInt(targetSrp),
          credentials: credentials
        };
      } else if (isMicrosoftLogin) {
        apiEndpoint = '/api/sparxreader/microsoft-start';
        requestBody = { 
          url: 'https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1',
          targetSrp: parseInt(targetSrp),
          credentials: credentials
        };
      } else if (isGoogleLogin) {
        apiEndpoint = '/api/sparxreader/google-start';
        requestBody = { 
          url: 'https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1',
          targetSrp: parseInt(targetSrp),
          credentials: credentials
        };
      }
      
      const token = localStorage.getItem('token');
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody),
      });
      const data = await response.json();
      
      if (data.success) {
        // Book title and SRP extracted, show confirmation dialog
        setMessage(`Book found - please confirm (Target SRP: ${targetSrp})`);
        setBookTitle(data.bookTitle);
        setCurrentSrp(data.currentSrp);
        setShowBookConfirmation(true);
        if (data.screenshot) {
          setScreenshot(data.screenshot);
        }
      } else {
        setMessage(data.error || 'Failed to start');
        if (data.needsPlaywright) {
          setNeedsPlaywright(true);
        }
      }
    } catch (error) {
      setMessage('Error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleYesClick = async () => {
    setLoading(true);
    setShowBookConfirmation(false);
    setIsInQueue(true);
    setMessage('Adding to queue...');

    try {
      const token = localStorage.getItem('token');

      // Get credentials based on mode
      let credentials = null;
      if (credentialMode === 'key' && loginKey) {
        // Get credentials from login key
        try {
          const credResponse = await fetch('/api/credentials/get', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ loginKey })
          });

          const credData = await credResponse.json();
          if (credData.success) {
            credentials = credData.credentials;
          } else {
            setIsInQueue(false);
            setLoading(false);
            setMessage(credData.error || 'Failed to retrieve credentials');
            return;
          }
        } catch (error) {
          setIsInQueue(false);
          setLoading(false);
          setMessage('Error retrieving credentials');
          return;
        }
      } else if (credentialMode === 'enter' && userSchool && userEmail && userPassword) {
        // Use entered credentials
        credentials = {
          school: userSchool,
          email: userEmail,
          password: userPassword,
          loginMethod: loginMethod
        };
      } else {
        setIsInQueue(false);
        setLoading(false);
        setMessage('No credentials available');
        return;
      }

      // Create a queue job instead of direct automation
      const response = await fetch('/api/queue/jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          job_type: 'sparx_reader',
          job_data: {
            school: credentials.school,
            email: credentials.email,
            username: credentials.email, // Use email as username for compatibility
            password: credentials.password,
            login_type: loginMethod,
            bookTitle: bookTitle
          },
          srp_target: targetSrp ? parseInt(targetSrp) : 100,
          priority: 0
        }),
      });

      const data = await response.json();

      if (data.success) {
        setJobId(data.job_id);
        setMessage('Added to queue! Checking position...');

        // Start monitoring queue position
        startQueueMonitoring(data.job_id);
      } else {
        setIsInQueue(false);
        setMessage(data.error || 'Failed to add job to queue');
      }
    } catch (error) {
      setIsInQueue(false);
      setMessage('Error occurred while adding to queue');
    } finally {
      setLoading(false);
    }
  };

  const startQueueMonitoring = (jobId) => {
    const checkPosition = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/queue/position?job_id=${jobId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        const data = await response.json();

        if (data.success) {
          setQueuePosition(data.position);

          if (data.position === 1 && data.status === 'processing') {
            // Job is now being processed!
            setIsProcessing(true);
            setIsInQueue(false);
            setMessage('Your job is now being processed!');

            // Start monitoring job completion
            startJobMonitoring(jobId);
          } else if (data.status === 'completed') {
            // Job completed
            handleJobCompletion(data.result);
          } else if (data.status === 'failed') {
            // Job failed
            setIsInQueue(false);
            setIsProcessing(false);
            setMessage(data.error || 'Job failed to process');
          } else {
            // Still in queue
            setMessage(`Queue position: #${data.position}`);
            // Check again in 3 seconds
            setTimeout(checkPosition, 3000);
          }
        }
      } catch (error) {
        console.error('Error checking queue position:', error);
        setTimeout(checkPosition, 5000); // Retry in 5 seconds
      }
    };

    checkPosition();
  };

  const startJobMonitoring = (jobId) => {
    const checkJobStatus = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/queue/jobs/${jobId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        const data = await response.json();

        if (data.success) {
          if (data.job.status === 'completed') {
            handleJobCompletion(data.job.result);
          } else if (data.job.status === 'failed') {
            setIsProcessing(false);
            setMessage(data.job.error || 'Job failed to process');
          } else {
            // Still processing, check again
            setTimeout(checkJobStatus, 5000);
          }
        }
      } catch (error) {
        console.error('Error checking job status:', error);
        setTimeout(checkJobStatus, 5000);
      }
    };

    checkJobStatus();
  };

  const handleJobCompletion = (result) => {
    setIsProcessing(false);
    setIsInQueue(false);

    if (result && result.success) {
      setMessage('Automation completed! Displaying results...');
      setStoryContent(result.storyContent);
      setBookTitle(result.bookTitle);

      // Start the question simulation
      setTimeout(() => {
        setAutomationComplete(true);
        setIsAutomationRunning(true);
        setSrpEarned(0);
        setQuestionNumber(0);
        setQuestionHistory([]);
        setMessage('');
      }, 1500);

      if (result.screenshot) {
        setScreenshot(result.screenshot);
      }
    } else {
      setMessage(result?.error || 'Job completed but with errors');
    }
  };

  const handleNoClick = async () => {
    setLoading(true);
    setMessage('Finding a different book with same SRP target...');
    
    try {
      // Close current session
      const token = localStorage.getItem('token');
      await fetch('/api/sparxreader/close', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // Restart automation with existing target SRP
      setLoading(true);
      setShowInitialSrpInput(false);
      setMessage('Preparing to start with new book...');
      
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/sparxreader/start', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ 
            url: 'https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1',
            targetSrp: parseInt(targetSrp)
          }),
        });
        const data = await response.json();
        
        if (data.success) {
          setMessage(`Book found - please confirm (Target SRP: ${targetSrp})`);
          setBookTitle(data.bookTitle);
          setCurrentSrp(data.currentSrp);
          setShowBookConfirmation(true);
          if (data.screenshot) {
            setScreenshot(data.screenshot);
          }
        } else {
          setMessage(data.error || 'Failed to start');
          if (data.needsPlaywright) {
            setNeedsPlaywright(true);
          }
        }
      } catch (error) {
        setMessage('Error occurred while restarting');
      }
    } catch (error) {
      setMessage('Error closing previous session');
    } finally {
      setLoading(false);
    }
  };

  const handleBackClick = () => {
    setIsAutomationRunning(false);
    setIsProcessing(false);
    setCurrentQuestion('');
    setCurrentAnswer('');
    setQuestionNumber(0);
    setSrpEarned(0);
    setQuestionHistory([]);
    setMessage('');
    setShowBookConfirmation(false);
    setShowInitialSrpInput(false);
    setHasStarted(false);
    setAutomationComplete(false);
  };
  
  // Show loading screen while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent"></div>
        <div className="relative z-10 text-center">
          <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Don't render main content if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black relative">
      {/* Subtle blue accent */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent"></div>

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header */}
        <header className="p-6 border-b border-gray-800">
          <div className="max-w-6xl mx-auto relative">
            {/* Back Button - Only show during automation or processing */}
            {(isAutomationRunning || isProcessing) && (
              <button 
                onClick={handleBackClick}
                className="absolute left-0 top-1/2 transform -translate-y-1/2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-all duration-200 flex items-center text-sm"
              >
                <span className="mr-2">&lt;</span>
                Back
              </button>
            )}

            {/* User Info and Logout - Top right */}
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 flex items-center space-x-4">
              <div className="text-right">
                <p className="text-white text-sm font-medium">Welcome, {user?.username}</p>
                <p className="text-gray-400 text-xs">{user?.role === 'admin' ? 'Administrator' : 'User'}</p>
              </div>
              <div className="flex space-x-2">
                {user?.role === 'admin' && (
                  <button
                    onClick={() => router.push('/admin')}
                    className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors"
                  >
                    Admin
                  </button>
                )}
                <button
                  onClick={() => router.push('/queue')}
                  className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors"
                >
                  Queue
                </button>
                <button
                  onClick={handleLogout}
                  className="px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white text-sm rounded transition-colors"
                >
                  Logout
                </button>
              </div>
            </div>
            
            <h1 className="text-3xl font-light text-white text-center">
              Sparx Reader
            </h1>
            <p className="text-center text-gray-400 mt-1 text-sm">Automated Question Solving</p>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 flex items-center justify-center p-6">
          <div className="w-full max-w-4xl mx-auto">
            
            {/* Enhanced Queue/Processing State */}
            {(isInQueue || isProcessing) && (
              <div className="mb-6 animate-fade-in">
                <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-8 text-center shadow-2xl">
                  <div className="relative w-20 h-20 mx-auto mb-6">
                    {/* Outer rotating ring */}
                    <div className="absolute inset-0 border-4 border-blue-500/30 rounded-full animate-spin"></div>
                    {/* Inner pulsing core */}
                    <div className="absolute inset-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse">
                      <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                        <div className="w-4 h-4 bg-blue-500 rounded-full animate-ping"></div>
                      </div>
                    </div>
                    {/* Floating particles */}
                    <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                    <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.5s'}}></div>
                  </div>
                  {isInQueue ? (
                    <>
                      <h2 className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent mb-4">
                        � In Queue
                      </h2>
                      <p className="text-gray-300 mb-6 text-lg">
                        {queuePosition ? `Queue Position: #${queuePosition}` : 'Checking queue position...'}
                      </p>
                      <div className="w-full max-w-md mx-auto">
                        <div className="flex items-center justify-center space-x-3">
                          <div className="w-6 h-6 border-4 border-yellow-500 border-t-transparent rounded-full animate-spin"></div>
                          <span className="text-gray-300">Waiting for your turn...</span>
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4">
                        🚀 Processing Automation
                      </h2>
                      <p className="text-gray-300 mb-6 text-lg">AI is solving questions in the background...</p>
                      <div className="w-full max-w-md mx-auto">
                        <div className="flex items-center justify-center space-x-3">
                          <div className="w-6 h-6 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                          <span className="text-gray-300">Processing your request...</span>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
            
            {/* Enhanced SRP Dashboard - Always visible when automation is running */}
            {isAutomationRunning && (
              <div className="mb-6 animate-fade-in">
                <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                      📊 Live Progress Dashboard
                    </h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {/* Enhanced SRP Progress */}
                    <div className="text-center">
                      <div className="relative w-24 h-24 mx-auto mb-4">
                        {/* Background circle */}
                        <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                          <circle cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="4" fill="transparent" className="text-gray-700"/>
                          <circle
                            cx="50" cy="50" r="40"
                            stroke="url(#srpGradient)"
                            strokeWidth="4"
                            fill="transparent"
                            strokeDasharray={`${(srpEarned / parseInt(targetSrp || 1)) * 251.2} 251.2`}
                            className="transition-all duration-1000 ease-out drop-shadow-lg"
                            strokeLinecap="round"
                          />
                          <defs>
                            <linearGradient id="srpGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stopColor="#10B981" />
                              <stop offset="50%" stopColor="#3B82F6" />
                              <stop offset="100%" stopColor="#8B5CF6" />
                            </linearGradient>
                          </defs>
                        </svg>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center">
                            <span className="text-xl font-bold text-white block">{srpEarned}</span>
                            <span className="text-xs text-gray-400">SRP</span>
                          </div>
                        </div>
                        {/* Glowing effect */}
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-green-500/20 to-blue-500/20 animate-pulse"></div>
                      </div>
                      <p className="text-white text-sm font-medium mb-1">SRP Earned</p>
                      <p className="text-blue-400 text-xs">Target: {targetSrp}</p>
                      <div className="mt-2 text-xs text-gray-400">
                        {Math.round((srpEarned / parseInt(targetSrp || 1)) * 100)}% Complete
                      </div>
                    </div>

                    {/* Enhanced Question Progress */}
                    <div className="text-center">
                      <div className="relative mb-4">
                        <div className="text-3xl font-bold text-blue-400 mb-1 animate-pulse">{questionNumber}</div>
                        <div className="text-xs text-gray-400 mb-3">Questions Solved</div>
                        <div className="w-full bg-gray-700 rounded-full h-3 overflow-hidden">
                          <div
                            className="bg-gradient-to-r from-blue-500 to-cyan-400 h-3 rounded-full transition-all duration-1000 ease-out relative"
                            style={{ width: `${Math.min((questionNumber / 10) * 100, 100)}%` }}
                          >
                            <div className="absolute inset-0 bg-white/30 animate-pulse"></div>
                          </div>
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          {Math.min(Math.round((questionNumber / 10) * 100), 100)}% of estimated session
                        </div>
                      </div>
                      <p className="text-white text-sm font-medium">🧠 AI Processing</p>
                    </div>

                    {/* Enhanced Status & Performance */}
                    <div className="text-center">
                      <div className="relative mb-4">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mx-auto mb-3 flex items-center justify-center animate-pulse">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        </div>
                        <div className="text-xs text-gray-400 mb-3">System Status</div>

                        <div className="bg-gray-700 rounded-lg p-3">
                          <div className="text-sm text-green-400 mb-2">🟢 Active & Processing</div>
                          <div className="text-xs text-gray-400 mb-1">Performance Metrics</div>
                          <div className="text-xs text-white">
                            ⚡ {questionNumber > 0 ? Math.round(elapsedTime / questionNumber) : 0}s per question
                          </div>
                          <div className="text-xs text-blue-400">
                            🎯 {questionNumber > 0 ? Math.round((questionNumber / elapsedTime) * 60) : 0} questions/min
                          </div>
                          <div className="text-xs text-purple-400 mt-1">
                            ⏱️ {formatTime(elapsedTime)} elapsed
                          </div>
                        </div>
                      </div>
                      <p className="text-white text-sm font-medium">🚀 AI Engine</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Current Question Display */}
            {currentQuestion && isAutomationRunning && (
              <div className="mb-6 animate-slide-up" key={animationKey}>
                <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl">
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4 animate-pulse">
                      {questionNumber}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                        ✅ Question Solved
                      </h3>
                      <p className="text-xs text-gray-400">AI successfully processed this question</p>
                    </div>
                    <div className="ml-auto flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-ping"></div>
                      <span className="text-xs text-green-400 font-medium">COMPLETED</span>
                    </div>
                  </div>

                  <div className="bg-black/60 border border-gray-600 rounded-xl p-6 mb-6 backdrop-blur-sm">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <span className="text-white text-sm font-bold">Q</span>
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-300 text-base leading-relaxed mb-3">{currentQuestion}</p>
                        <div className="flex items-center space-x-2">
                          <span className="px-2 py-1 bg-green-600 text-white text-xs rounded font-medium">
                            +{Math.floor(Math.random() * 3) + 1} SRP
                          </span>
                          <span className="text-xs text-gray-400">• Solved automatically</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {currentAnswer && (
                    <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-xl p-6">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-white text-sm font-bold">AI</span>
                        </div>
                        <div className="flex-1">
                          <p className="text-blue-400 font-medium mb-2">🤖 AI Solution:</p>
                          <p className="text-gray-200 leading-relaxed">{currentAnswer}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Enhanced Start Screen - Only show when not started */}
            {!hasStarted && (
              <div className="text-center animate-fade-in">
                <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-10 max-w-lg mx-auto shadow-2xl">
                  <div className="mb-8">
                    <div className="relative w-16 h-16 mx-auto mb-6">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-bounce">
                        <span className="text-white text-2xl">🚀</span>
                      </div>
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-ping"></div>
                      <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-purple-400 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                    </div>
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3">
                      Start Automation
                    </h2>
                    <p className="text-gray-300 text-base">🤖 AI-powered question solving for Sparx Reader</p>
                  </div>
                  
                  {/* Enhanced Login Options */}
                  <div className="mb-8">
                    <h3 className="text-xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                      🔐 Choose Login Method
                    </h3>
                    <div className="space-y-4">
                      <button
                        onClick={() => handleLoginMethodSelect('normal')}
                        disabled={loading}
                        className={`w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 ${
                          loginMethod === 'normal'
                            ? 'bg-gradient-to-r from-green-600 to-green-500 text-white ring-2 ring-green-400 shadow-lg shadow-green-500/25'
                            : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-500 text-white shadow-lg hover:shadow-green-500/25'
                        }`}
                      >
                        <span className="mr-3 text-lg">👤</span>
                        <span className="flex-1">Normal Login</span>
                        {loginMethod === 'normal' && <span className="ml-3 text-lg">✓</span>}
                      </button>

                      <button
                        onClick={() => handleLoginMethodSelect('microsoft')}
                        disabled={loading}
                        className={`w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 ${
                          loginMethod === 'microsoft'
                            ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white ring-2 ring-blue-400 shadow-lg shadow-blue-500/25'
                            : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-500 text-white shadow-lg hover:shadow-blue-500/25'
                        }`}
                      >
                        <span className="mr-3 text-lg">🏢</span>
                        <span className="flex-1">Microsoft Login</span>
                        {loginMethod === 'microsoft' && <span className="ml-3 text-lg">✓</span>}
                      </button>

                      <button
                        onClick={() => handleLoginMethodSelect('google')}
                        disabled={loading}
                        className={`w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 ${
                          loginMethod === 'google'
                            ? 'bg-gradient-to-r from-red-600 to-red-500 text-white ring-2 ring-red-400 shadow-lg shadow-red-500/25'
                            : 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-500 text-white shadow-lg hover:shadow-red-500/25'
                        }`}
                      >
                        <span className="mr-3 text-lg">🔍</span>
                        <span className="flex-1">Google Login</span>
                        {loginMethod === 'google' && <span className="ml-2">✓</span>}
                      </button>
                    </div>
                  </div>
                  
                  <div className="border-t border-gray-700 pt-4">
                    <div className="mb-3 text-center">
                      <p className="text-gray-400 text-sm">
                        Selected: <span className="text-white font-medium">
                          {loginMethod === 'normal' && 'Normal Login'}
                          {loginMethod === 'microsoft' && 'Microsoft Login'}
                          {loginMethod === 'google' && 'Google Login'}
                        </span>
                      </p>
                    </div>
                    <button 
                      onClick={handleBeginClick}
                      disabled={loading}
                      className="w-full py-3 px-6 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Begin
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* SRP Input Modal */}
            {showInitialSrpInput && (
              <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in">
                <div className="bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in">
                  <div className="text-center mb-6">
                    <div className="w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center">
                      <span className="text-lg text-white">*</span>
                    </div>
                    <h2 className="text-lg font-medium text-white mb-2">Set SRP Target</h2>
                    <p className="text-gray-400 text-sm">How much SRP do you want to earn?</p>
                  </div>
                  
                  <div className="space-y-4">
                    <input
                      type="number"
                      value={targetSrp}
                      onChange={(e) => setTargetSrp(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSrpSubmit()}
                      placeholder="Enter target (e.g., 50)"
                      className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center"
                      min="1"
                      autoFocus
                    />
                    <p className="text-xs text-gray-500 text-center">
                      Automation will stop when target is reached
                    </p>
                    
                    <div className="flex gap-3">
                      <button 
                        onClick={handleSrpSubmit}
                        disabled={loading || !targetSrp}
                        className="flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm"
                      >
                        {loading ? (
                          <span className="flex items-center justify-center">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            Starting...
                          </span>
                        ) : 'Start'}
                      </button>
                      <button 
                        onClick={() => setShowInitialSrpInput(false)}
                        disabled={loading}
                        className="flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Credential Input Modal */}
            {showCredentialInput && (
              <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in">
                <div className="bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in">
                  <div className="text-center mb-6">
                    <div className="w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center">
                      <span className="text-lg text-white">🔐</span>
                    </div>
                    <h2 className="text-lg font-medium text-white mb-2">Login Credentials</h2>
                    <p className="text-gray-400 text-sm">
                      {loginMethod === 'normal' && 'Enter your Sparx Learning credentials'}
                      {loginMethod === 'microsoft' && 'Enter your Microsoft account credentials'}
                      {loginMethod === 'google' && 'Enter your Google account credentials'}
                    </p>
                  </div>
                  
                  {/* Credential Mode Toggle */}
                  <div className="mb-6">
                    <div className="flex bg-gray-800 rounded-lg p-1">
                      <button
                        onClick={() => setCredentialMode('enter')}
                        className={`flex-1 py-2 px-3 rounded text-sm font-medium transition-all ${
                          credentialMode === 'enter' 
                            ? 'bg-blue-500 text-white' 
                            : 'text-gray-400 hover:text-white'
                        }`}
                      >
                        Enter Credentials
                      </button>
                      <button
                        onClick={() => setCredentialMode('key')}
                        className={`flex-1 py-2 px-3 rounded text-sm font-medium transition-all ${
                          credentialMode === 'key' 
                            ? 'bg-blue-500 text-white' 
                            : 'text-gray-400 hover:text-white'
                        }`}
                      >
                        Use Login Key
                      </button>
                    </div>
                  </div>

                  {credentialMode === 'enter' ? (
                    /* Enter Credentials Mode */
                    <div className="space-y-4">
                      <input
                        type="text"
                        value={userSchool}
                        onChange={(e) => setUserSchool(e.target.value)}
                        placeholder="School name (e.g., theangmeringschool)"
                        className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500"
                        autoFocus
                      />
                      <input
                        type="email"
                        value={userEmail}
                        onChange={(e) => setUserEmail(e.target.value)}
                        placeholder="Email address"
                        className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500"
                      />
                      <input
                        type="password"
                        value={userPassword}
                        onChange={(e) => setUserPassword(e.target.value)}
                        placeholder="Password"
                        className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500"
                      />
                      <p className="text-xs text-gray-500">
                        💡 Your credentials will be encrypted and stored securely. You'll receive a login key for future use.
                      </p>
                    </div>
                  ) : (
                    /* Use Login Key Mode */
                    <div className="space-y-4">
                      <input
                        type="text"
                        value={loginKey}
                        onChange={(e) => setLoginKey(e.target.value)}
                        placeholder="Enter your login key (e.g., SLK-ABC12345)"
                        className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center font-mono"
                        autoFocus
                      />
                      
                      {savedCredentials.length > 0 && (
                        <div>
                          <p className="text-xs text-gray-500 mb-2">Your saved login keys:</p>
                          <div className="space-y-1 max-h-32 overflow-y-auto">
                            {savedCredentials.map((cred, index) => (
                              <button
                                key={index}
                                onClick={() => setLoginKey(cred.loginKey)}
                                className="w-full text-left px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-sm text-gray-300 hover:text-white transition-colors"
                              >
                                <span className="font-mono text-blue-400">{cred.loginKey}</span>
                                <span className="ml-2 text-xs">({cred.loginMethod})</span>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <p className="text-xs text-gray-500">
                        🔑 Use your previously generated login key to access saved credentials.
                      </p>
                    </div>
                  )}
                  
                  <div className="flex gap-3 mt-6">
                    <button 
                      onClick={handleCredentialSubmit}
                      disabled={loading}
                      className="flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm"
                    >
                      {loading ? (
                        <span className="flex items-center justify-center">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Processing...
                        </span>
                      ) : 'Continue'}
                    </button>
                    <button 
                      onClick={() => {
                        setShowCredentialInput(false);
                        setHasStarted(false);
                      }}
                      disabled={loading}
                      className="flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Book Confirmation */}
            {showBookConfirmation && bookTitle && (
              <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in">
                <div className="bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-lg w-full mx-4 animate-scale-in">
                  <div className="text-center mb-6">
                    <div className="w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center">
                      <span className="text-lg text-white">B</span>
                    </div>
                    <h2 className="text-lg font-medium text-white mb-2">Book Found</h2>
                    <p className="text-gray-400 text-sm">Confirm to start automation</p>
                  </div>
                  
                  <div className="bg-black/30 rounded p-4 mb-6">
                    <h3 className="text-lg font-medium text-white mb-4">{bookTitle}</h3>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <p className="text-gray-400 text-xs">Current SRP</p>
                        <p className="text-xl font-medium text-blue-400">{currentSrp}</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-xs">Target SRP</p>
                        <p className="text-xl font-medium text-blue-400">{targetSrp}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-3">
                    <button 
                      onClick={handleYesClick}
                      disabled={loading}
                      className="flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                    >
                      {loading ? (
                        <span className="flex items-center justify-center">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Starting...
                        </span>
                      ) : 'Yes, Start'}
                    </button>
                    <button 
                      onClick={handleNoClick}
                      disabled={loading}
                      className="flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                    >
                      {loading ? 'Finding...' : 'Find Different'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Question History */}
            {questionHistory.length > 0 && isAutomationRunning && (
              <div className="animate-fade-in">
                <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                    <span className="mr-3">H</span>
                    Question History
                  </h3>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {questionHistory.slice(-5).map((item, index) => (
                      <div key={index} className="bg-black/30 rounded p-3 border-l-4 border-blue-500">
                        <div className="flex justify-between items-start mb-2">
                          <span className="text-blue-400 font-medium text-sm">Q{item.number}</span>
                          <span className="text-blue-400 text-xs">Solved</span>
                        </div>
                        <p className="text-white text-sm mb-2">{item.question.substring(0, 100)}...</p>
                        <p className="text-gray-400 text-xs">Answer: {item.answer}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Status Messages */}
            {message && !showInitialSrpInput && !showBookConfirmation && !isProcessing && (
              <div className="mt-6 text-center animate-fade-in">
                <div className="bg-gray-900 border border-gray-800 rounded p-4 inline-block">
                  <p className="text-white">{message}</p>
                </div>
              </div>
            )}

            {/* Playwright Installation */}
            {needsPlaywright && (
              <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in">
                <div className="bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                  <div className="text-center mb-6">
                    <div className="w-10 h-10 bg-red-500 rounded mx-auto mb-4 flex items-center justify-center">
                      <span className="text-lg text-white">!</span>
                    </div>
                    <h2 className="text-lg font-medium text-white mb-2">Setup Required</h2>
                    <p className="text-gray-400 text-sm">Playwright browsers need to be installed</p>
                  </div>
                  
                  <div className="bg-black/50 rounded p-4 mb-6">
                    <p className="text-white text-sm mb-2">Run this command in your terminal:</p>
                    <code className="bg-black text-blue-400 p-2 rounded block text-sm">
                      npx playwright install chromium
                    </code>
                  </div>
                  
                  <p className="text-gray-400 text-xs text-center">
                    After installation, refresh this page and try again.
                  </p>
                </div>
              </div>
            )}

            {/* License Renewal Modal */}
            {showLicenseRenewal && (
              <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 animate-fade-in">
                <div className="bg-gray-900 border border-gray-800 rounded-lg p-8 max-w-md w-full mx-4">
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-yellow-500 text-2xl">⚠️</span>
                    </div>
                    <h2 className="text-xl font-medium text-white mb-2">License Renewal Required</h2>
                    <p className="text-gray-400 text-sm">
                      {licenseStatus && licenseStatus.license_status === 'expired' && 'Your license has expired.'}
                      {licenseStatus && licenseStatus.license_status === 'maxed_out' && 'Your license has reached maximum uses.'}
                      {licenseStatus && licenseStatus.license_status === 'inactive' && 'Your license is inactive.'}
                      {(!licenseStatus || licenseStatus.license_status === 'valid') && 'Your license is not valid.'}
                    </p>
                    <p className="text-gray-400 text-sm mt-2">
                      Please enter a new license key to continue using the bot.
                    </p>
                  </div>

                  {licenseStatus && (
                    <div className="bg-black/50 border border-gray-700 rounded p-4 mb-6">
                      <h3 className="text-white font-medium mb-2">Current License Info:</h3>
                      <div className="text-sm text-gray-400 space-y-1">
                        <p>Key: {licenseStatus.key_code || 'N/A'}</p>
                        <p>Status: <span className={`font-medium ${
                          licenseStatus.license_status === 'expired' ? 'text-red-400' :
                          licenseStatus.license_status === 'maxed_out' ? 'text-orange-400' :
                          licenseStatus.license_status === 'inactive' ? 'text-gray-400' :
                          'text-green-400'
                        }`}>
                          {licenseStatus.license_status === 'expired' && 'Expired'}
                          {licenseStatus.license_status === 'maxed_out' && 'Max Uses Reached'}
                          {licenseStatus.license_status === 'inactive' && 'Inactive'}
                          {licenseStatus.license_status === 'valid' && 'Valid'}
                        </span></p>
                        {licenseStatus.expires_at && (
                          <p>Expires: {new Date(licenseStatus.expires_at).toLocaleDateString()}</p>
                        )}
                        {licenseStatus.max_uses && (
                          <p>Uses: {licenseStatus.current_uses || 0}/{licenseStatus.max_uses}</p>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="mb-6">
                    <label className="block text-white font-medium mb-2">New License Key</label>
                    <input
                      type="text"
                      value={newLicenseKey}
                      onChange={(e) => setNewLicenseKey(e.target.value)}
                      placeholder="Enter your new license key (e.g., SRX-XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXXXX)"
                      className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:border-blue-500 focus:outline-none"
                      disabled={licenseRenewalLoading}
                    />
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={handleLicenseRenewal}
                      disabled={licenseRenewalLoading || !newLicenseKey.trim()}
                      className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded transition-colors duration-200"
                    >
                      {licenseRenewalLoading ? (
                        <div className="flex items-center justify-center">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                          Renewing...
                        </div>
                      ) : (
                        'Renew License'
                      )}
                    </button>
                    <button
                      onClick={() => {
                        setShowLicenseRenewal(false);
                        setNewLicenseKey('');
                        setMessage('');
                      }}
                      disabled={licenseRenewalLoading}
                      className="px-6 py-3 border border-gray-600 text-gray-400 hover:text-white hover:border-gray-500 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Screenshot Display */}
            {screenshot && (
              <div className="mt-6 animate-fade-in">
                <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                    <span className="mr-3">S</span>
                    Browser State
                  </h3>
                  <div className="rounded border border-gray-700 overflow-hidden">
                    <img src={screenshot} alt="Browser screenshot" className="w-full h-auto" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}