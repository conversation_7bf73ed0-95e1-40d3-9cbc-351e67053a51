"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/queue/page",{

/***/ "(app-pages-browser)/./app/queue/page.jsx":
/*!****************************!*\
  !*** ./app/queue/page.jsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QueueDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ScheduleCalendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ScheduleCalendar */ \"(app-pages-browser)/./app/queue/components/ScheduleCalendar.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction QueueDashboard() {\n    var _queueStatus_global_overview, _queueStatus_license_features, _queueStatus_global_overview_queue_status, _queueStatus_global_overview1, _queueStatus_license_features1, _queueStatus_license_features2, _queueStatus_license_features3, _queueStatus_license_features4, _queueStatus_license_features5, _queueStatus_license_features6, _queueStatus_license_features7;\n    _s();\n    const [queueStatus, setQueueStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [batches, setBatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [priorityLevels, setPriorityLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [processingJobs, setProcessingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [realtimeUpdates, setRealtimeUpdates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Form states\n    const [batchForm, setBatchForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        batch_name: \"\",\n        login_type: \"\",\n        accounts: [\n            {\n                school: \"\",\n                email: \"\",\n                password: \"\",\n                login_type: \"regular\"\n            }\n        ],\n        scheduled_time: \"\",\n        priority_override: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadQueueData();\n    }, []);\n    // Dynamic auto-refresh based on activity\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _queueStatus_global_overview_queue_status, _queueStatus_global_overview, _queueStatus_global_overview_queue_status1, _queueStatus_global_overview1;\n        if (!realtimeUpdates) return;\n        // Use faster refresh when there are processing jobs or queued jobs\n        const hasActivity = (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_global_overview = queueStatus.global_overview) === null || _queueStatus_global_overview === void 0 ? void 0 : (_queueStatus_global_overview_queue_status = _queueStatus_global_overview.queue_status) === null || _queueStatus_global_overview_queue_status === void 0 ? void 0 : _queueStatus_global_overview_queue_status.processing_jobs) > 0 || (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_global_overview1 = queueStatus.global_overview) === null || _queueStatus_global_overview1 === void 0 ? void 0 : (_queueStatus_global_overview_queue_status1 = _queueStatus_global_overview1.queue_status) === null || _queueStatus_global_overview_queue_status1 === void 0 ? void 0 : _queueStatus_global_overview_queue_status1.queued_jobs) > 0 || batches.some((batch)=>batch.status === \"processing\" || batch.status === \"queued\");\n        const refreshInterval = hasActivity ? 10000 : 30000; // 10s for activity, 30s when idle\n        const interval = setInterval(()=>{\n            loadQueueData();\n        }, refreshInterval);\n        return ()=>clearInterval(interval);\n    }, [\n        realtimeUpdates,\n        queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_global_overview = queueStatus.global_overview) === null || _queueStatus_global_overview === void 0 ? void 0 : _queueStatus_global_overview.queue_status,\n        batches\n    ]);\n    const loadQueueData = async ()=>{\n        try {\n            setLoading(true);\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const headers = {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            };\n            // Load queue status\n            const statusResponse = await fetch(\"/api/queue/status?detailed=true\", {\n                headers\n            });\n            if (statusResponse.ok) {\n                const statusData = await statusResponse.json();\n                setQueueStatus(statusData);\n            }\n            // Load batches\n            const batchResponse = await fetch(\"/api/queue/batch\", {\n                headers\n            });\n            if (batchResponse.ok) {\n                const batchData = await batchResponse.json();\n                setBatches(batchData.batches || []);\n            }\n            // Load schedules\n            const scheduleResponse = await fetch(\"/api/queue/schedule\", {\n                headers\n            });\n            if (scheduleResponse.ok) {\n                const scheduleData = await scheduleResponse.json();\n                setSchedules(scheduleData.schedules || []);\n            }\n            // Load priority levels\n            const priorityResponse = await fetch(\"/api/queue/priority-levels\", {\n                headers\n            });\n            if (priorityResponse.ok) {\n                const priorityData = await priorityResponse.json();\n                setPriorityLevels(priorityData);\n            }\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBatchSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/queue/batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(batchForm)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                alert('Batch \"'.concat(result.batch.name, '\" created successfully!'));\n                setBatchForm({\n                    batch_name: \"\",\n                    login_type: \"\",\n                    accounts: [\n                        {\n                            school: \"\",\n                            email: \"\",\n                            password: \"\"\n                        }\n                    ],\n                    scheduled_time: \"\",\n                    priority_override: \"\"\n                });\n                loadQueueData(); // Refresh data\n            } else {\n                const error = await response.json();\n                alert(\"Error: \".concat(error.error));\n            }\n        } catch (err) {\n            alert(\"Error: \".concat(err.message));\n        }\n    };\n    const addAccount = ()=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: [\n                    ...prev.accounts,\n                    {\n                        school: \"\",\n                        email: \"\",\n                        password: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeAccount = (index)=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: prev.accounts.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateAccount = (index, field, value)=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: prev.accounts.map((account, i)=>i === index ? {\n                        ...account,\n                        [field]: value\n                    } : account)\n            }));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-300\",\n                        children: \"Loading queue dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-400 text-xl mb-4\",\n                        children: \"⚠️ Error\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: loadQueueData,\n                        className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Queue Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-300\",\n                                    children: \"Manage your batch processing and scheduling\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: realtimeUpdates,\n                                                onChange: (e)=>setRealtimeUpdates(e.target.checked),\n                                                className: \"sr-only\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-11 h-6 rounded-full transition-colors \".concat(realtimeUpdates ? \"bg-blue-600\" : \"bg-gray-600\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform \".concat(realtimeUpdates ? \"translate-x-5\" : \"translate-x-0\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-gray-300\",\n                                                children: [\n                                                    \"Real-time \",\n                                                    realtimeUpdates ? \"ON\" : \"OFF\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/\"),\n                                    className: \"px-4 py-2 bg-gray-800 text-gray-300 rounded hover:bg-gray-700 hover:text-white transition-colors\",\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this),\n                (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.license_features) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-gray-900 border border-gray-700 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-400 mb-2\",\n                            children: \"Your License Features\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.max_accounts_per_batch || \"Unlimited\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Max Accounts per Batch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.priority_level\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Priority Level\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.scheduling_access ? \"✅\" : \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Scheduling Access\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 226,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                name: \"Overview\"\n                            },\n                            ...(queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features = queueStatus.license_features) === null || _queueStatus_license_features === void 0 ? void 0 : _queueStatus_license_features.max_accounts_per_batch) > 0 ? [\n                                {\n                                    id: \"batches\",\n                                    name: \"Batches\"\n                                },\n                                {\n                                    id: \"create\",\n                                    name: \"Create Batch\"\n                                }\n                            ] : [],\n                            {\n                                id: \"schedule\",\n                                name: \"Schedule\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? \"border-blue-400 text-blue-400\" : \"border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-600\"),\n                                children: tab.name\n                            }, tab.id, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.global_overview) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Real-time Queue Overview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-400\",\n                                                            children: queueStatus.global_overview.total_users\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Total Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-400\",\n                                                            children: queueStatus.global_overview.total_batches_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Batches Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-yellow-400\",\n                                                            children: queueStatus.global_overview.queue_status.queued_jobs\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Jobs in Queue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        queueStatus.global_overview.queue_status.queued_jobs === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: \"No pending jobs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-400\",\n                                                            children: queueStatus.global_overview.queue_status.processing_jobs\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Processing Now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        queueStatus.global_overview.queue_status.processing_jobs === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: \"System idle\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-green-500\",\n                                                            children: queueStatus.global_overview.queue_status.completed_jobs_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Completed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-red-400\",\n                                                            children: queueStatus.global_overview.queue_status.failed_jobs_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Failed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 282,\n                            columnNumber: 15\n                        }, this),\n                        queueStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white\",\n                                        children: \"Your Queue Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Total Batches\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: queueStatus.user_queue_status.total_batches\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Active Jobs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-400\",\n                                                        children: queueStatus.user_queue_status.queued_jobs + queueStatus.user_queue_status.processing_jobs\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Completed Jobs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: queueStatus.user_queue_status.completed_jobs\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Estimated Wait\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-400\",\n                                                        children: queueStatus.estimated_wait_time.formatted\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 342,\n                            columnNumber: 15\n                        }, this),\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_global_overview1 = queueStatus.global_overview) === null || _queueStatus_global_overview1 === void 0 ? void 0 : (_queueStatus_global_overview_queue_status = _queueStatus_global_overview1.queue_status) === null || _queueStatus_global_overview_queue_status === void 0 ? void 0 : _queueStatus_global_overview_queue_status.processing_jobs) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Live Processing (\",\n                                            queueStatus.global_overview.queue_status.processing_jobs,\n                                            \" active)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-white mb-2\",\n                                                children: \"Jobs Processing\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: [\n                                                    queueStatus.global_overview.queue_status.processing_jobs,\n                                                    \" job\",\n                                                    queueStatus.global_overview.queue_status.processing_jobs !== 1 ? \"s\" : \"\",\n                                                    \" currently being processed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-2\",\n                                                children: \"Real-time updates every 5 seconds\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 379,\n                            columnNumber: 15\n                        }, this),\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.queue_positions) && queueStatus.queue_positions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white\",\n                                        children: \"Your Queue Positions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: queueStatus.queue_positions.map((position)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center p-3 bg-gray-800 border border-gray-700 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-white\",\n                                                                children: [\n                                                                    \"Job #\",\n                                                                    position.id\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: [\n                                                                    \"Priority: \",\n                                                                    position.effective_priority\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    position.status\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-blue-400\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    position.queue_position\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"in queue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, position.id, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 405,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, this),\n                (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features1 = queueStatus.license_features) === null || _queueStatus_license_features1 === void 0 ? void 0 : _queueStatus_license_features1.max_accounts_per_batch) === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDC65\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 435,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white mb-2\",\n                                children: \"Multi-user Access Required\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 436,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-2\",\n                                children: \"Your current license doesn't include batch processing access.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 437,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"You can only process single homework assignments.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 438,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Please upgrade your license to create batches with multiple accounts.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 439,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 434,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 433,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"batches\" && (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features2 = queueStatus.license_features) === null || _queueStatus_license_features2 === void 0 ? void 0 : _queueStatus_license_features2.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white\",\n                                children: \"Your Batches\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 447,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-800\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Batch Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Priority\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-gray-900 divide-y divide-gray-700\",\n                                        children: batches.map((batch)=>{\n                                            const progressPercentage = batch.total_accounts > 0 ? Math.round(batch.processed_accounts / batch.total_accounts * 100) : 0;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-white\",\n                                                                    children: batch.batch_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between text-xs text-gray-400 mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Progress\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                    lineNumber: 483,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        progressPercentage,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                    lineNumber: 484,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-500 ease-out\",\n                                                                                style: {\n                                                                                    width: \"\".concat(progressPercentage, \"%\")\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 486,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(batch.status === \"completed\" ? \"bg-green-900 text-green-300 border border-green-700\" : batch.status === \"processing\" ? \"bg-blue-900 text-blue-300 border border-blue-700\" : batch.status === \"failed\" ? \"bg-red-900 text-red-300 border border-red-700\" : \"bg-yellow-900 text-yellow-300 border border-yellow-700\"),\n                                                                children: [\n                                                                    batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    batch.status\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                batch.processed_accounts,\n                                                                                \"/\",\n                                                                                batch.total_accounts\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 text-blue-400 text-xs\",\n                                                                            children: \"processing\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 516,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.failed_accounts > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-red-400 text-xs mt-1\",\n                                                                    children: [\n                                                                        batch.failed_accounts,\n                                                                        \" failed\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 rounded-full mr-2 \".concat(batch.priority_level >= 8 ? \"bg-red-400\" : batch.priority_level >= 5 ? \"bg-yellow-400\" : \"bg-green-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.priority_level\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: new Date(batch.created_at).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs\",\n                                                                    children: new Date(batch.created_at).toLocaleTimeString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, batch.id, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 450,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 449,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 445,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"schedule\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: !(queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features3 = queueStatus.license_features) === null || _queueStatus_license_features3 === void 0 ? void 0 : _queueStatus_license_features3.scheduling_access) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCC5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-white mb-2\",\n                                    children: \"Scheduling Not Available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Your current license doesn't include scheduling access.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Please upgrade your license to use this feature.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 555,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 554,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScheduleCalendar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        schedules: schedules,\n                        onScheduleSelect: (schedule)=>{\n                            console.log(\"Selected schedule:\", schedule);\n                        // Handle schedule selection (e.g., show details modal)\n                        },\n                        onCreateSchedule: async (scheduleData)=>{\n                            try {\n                                const token = localStorage.getItem(\"token\");\n                                const response = await fetch(\"/api/queue/schedule\", {\n                                    method: \"POST\",\n                                    headers: {\n                                        \"Authorization\": \"Bearer \".concat(token),\n                                        \"Content-Type\": \"application/json\"\n                                    },\n                                    body: JSON.stringify(scheduleData)\n                                });\n                                if (response.ok) {\n                                    const result = await response.json();\n                                    alert(\"Schedule created successfully!\");\n                                    loadQueueData(); // Refresh data\n                                } else {\n                                    const error = await response.json();\n                                    alert(\"Error: \".concat(error.error));\n                                }\n                            } catch (err) {\n                                alert(\"Error: \".concat(err.message));\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 563,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 552,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"create\" && (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features4 = queueStatus.license_features) === null || _queueStatus_license_features4 === void 0 ? void 0 : _queueStatus_license_features4.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white\",\n                                children: \"Create New Batch\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 601,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 600,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleBatchSubmit,\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300\",\n                                            children: \"Batch Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: batchForm.batch_name,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        batch_name: e.target.value\n                                                    })),\n                                            className: \"mt-1 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                            children: \"Login Type\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: batchForm.login_type,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        login_type: e.target.value\n                                                    })),\n                                            className: \"mb-4 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Login Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"normal\",\n                                                    children: \"\\uD83D\\uDC64 Normal Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"google\",\n                                                    children: \"\\uD83D\\uDD0D Google Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"microsoft\",\n                                                    children: \"\\uD83C\\uDFE2 Microsoft Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                            children: \"Accounts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 17\n                                        }, this),\n                                        batchForm.accounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-600 bg-gray-800 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"School\",\n                                                        value: account.school,\n                                                        onChange: (e)=>updateAccount(index, \"school\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        placeholder: \"Email\",\n                                                        value: account.email,\n                                                        onChange: (e)=>updateAccount(index, \"email\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        placeholder: \"Password\",\n                                                        value: account.password,\n                                                        onChange: (e)=>updateAccount(index, \"password\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeAccount(index),\n                                                        className: \"px-3 py-2 text-red-400 border border-red-600 rounded-md hover:bg-red-900 transition-colors\",\n                                                        disabled: batchForm.accounts.length === 1,\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 19\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addAccount,\n                                            className: \"px-4 py-2 text-blue-400 border border-blue-600 rounded-md hover:bg-blue-900 transition-colors\",\n                                            disabled: (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features5 = queueStatus.license_features) === null || _queueStatus_license_features5 === void 0 ? void 0 : _queueStatus_license_features5.max_accounts_per_batch) > 0 && batchForm.accounts.length >= queueStatus.license_features.max_accounts_per_batch,\n                                            children: \"Add Account\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 17\n                                        }, this),\n                                        (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features6 = queueStatus.license_features) === null || _queueStatus_license_features6 === void 0 ? void 0 : _queueStatus_license_features6.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-1\",\n                                            children: [\n                                                \"Maximum \",\n                                                queueStatus.license_features.max_accounts_per_batch,\n                                                \" accounts per batch\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 15\n                                }, this),\n                                (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features7 = queueStatus.license_features) === null || _queueStatus_license_features7 === void 0 ? void 0 : _queueStatus_license_features7.scheduling_access) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300\",\n                                            children: \"Schedule Time (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"datetime-local\",\n                                            value: batchForm.scheduled_time,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        scheduled_time: e.target.value\n                                                    })),\n                                            className: \"mt-1 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setBatchForm({\n                                                    batch_name: \"\",\n                                                    login_type: \"\",\n                                                    accounts: [\n                                                        {\n                                                            school: \"\",\n                                                            email: \"\",\n                                                            password: \"\"\n                                                        }\n                                                    ],\n                                                    scheduled_time: \"\",\n                                                    priority_override: \"\"\n                                                }),\n                                            className: \"px-4 py-2 text-gray-300 border border-gray-600 rounded-md hover:bg-gray-800 transition-colors\",\n                                            children: \"Reset\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\",\n                                            children: \"Create Batch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 599,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n_s(QueueDashboard, \"JugoJYrb/5NgGCTNa5EUIbUkvWU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = QueueDashboard;\nvar _c;\n$RefreshReg$(_c, \"QueueDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/queue/page.jsx\n"));

/***/ })

});