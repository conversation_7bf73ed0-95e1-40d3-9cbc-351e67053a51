"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.jsx":
/*!**********************!*\
  !*** ./app/page.jsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SparxReaderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SparxReaderPage() {\n    _s();\n    // Authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [authLoading, setAuthLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bookTitle, setBookTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [needsPlaywright, setNeedsPlaywright] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [screenshot, setScreenshot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [storyContent, setStoryContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showBookConfirmation, setShowBookConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSrp, setCurrentSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetSrp, setTargetSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showInitialSrpInput, setShowInitialSrpInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for enhanced UI\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentAnswer, setCurrentAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [questionNumber, setQuestionNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [srpEarned, setSrpEarned] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutomationRunning, setIsAutomationRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionHistory, setQuestionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [animationKey, setAnimationKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [automationComplete, setAutomationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [queuePosition, setQueuePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobId, setJobId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInQueue, setIsInQueue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginMethod, setLoginMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\"); // 'normal' or 'microsoft'\n    // Credential system states\n    const [showCredentialInput, setShowCredentialInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [credentialMode, setCredentialMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"enter\"); // 'enter' or 'key'\n    const [userSchool, setUserSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userEmail, setUserEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userPassword, setUserPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loginKey, setLoginKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [savedCredentials, setSavedCredentials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // License renewal states\n    const [showLicenseRenewal, setShowLicenseRenewal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [licenseStatus, setLicenseStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newLicenseKey, setNewLicenseKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [licenseRenewalLoading, setLicenseRenewalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check authentication on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuthentication();\n    }, []);\n    // Simulate question-solving process AFTER automation completes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated || !isAutomationRunning || !automationComplete) return;\n        if (isAutomationRunning && automationComplete) {\n            const interval = setInterval(()=>{\n                // Simulate question solving progress\n                setQuestionNumber((prev)=>{\n                    const newNum = prev + 1;\n                    setSrpEarned((prevSrp)=>prevSrp + Math.floor(Math.random() * 3) + 2);\n                    setAnimationKey((prevKey)=>prevKey + 1);\n                    // Simulate new question\n                    const sampleQuestions = [\n                        \"What was the main character's motivation in chapter 3?\",\n                        \"How did the setting influence the story's outcome?\",\n                        \"What literary device was used in the opening paragraph?\",\n                        \"Why did the protagonist make that crucial decision?\",\n                        \"What theme is most prominent throughout the narrative?\",\n                        \"How does the author develop the central conflict?\",\n                        \"What role does symbolism play in the narrative?\",\n                        \"are these questions fake and is your homework already complete?\"\n                    ];\n                    const sampleAnswers = [\n                        \"To find their lost family member\",\n                        \"The harsh winter created urgency\",\n                        \"Metaphor and symbolism\",\n                        \"To protect their friends\",\n                        \"The importance of friendship\",\n                        \"Through escalating tension\",\n                        \"It reinforces the main themes\",\n                        \"yes lol\"\n                    ];\n                    const randomIndex = Math.floor(Math.random() * sampleQuestions.length);\n                    setCurrentQuestion(sampleQuestions[randomIndex]);\n                    setCurrentAnswer(sampleAnswers[randomIndex]);\n                    // Add to history\n                    setQuestionHistory((prev)=>[\n                            ...prev,\n                            {\n                                number: newNum,\n                                question: sampleQuestions[randomIndex],\n                                answer: sampleAnswers[randomIndex]\n                            }\n                        ]);\n                    // Stop after reaching target or max questions\n                    if (newNum >= 8 || srpEarned >= parseInt(targetSrp || 50)) {\n                        setTimeout(()=>{\n                            setIsAutomationRunning(false);\n                            setMessage(\"Target SRP reached! Automation completed successfully.\");\n                        }, 1500); // Show the last question for a bit\n                        clearInterval(interval);\n                        return newNum;\n                    }\n                    return newNum;\n                });\n            }, 2500); // Show new question every 2.5 seconds\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isAuthenticated,\n        isAutomationRunning,\n        automationComplete,\n        srpEarned,\n        targetSrp\n    ]);\n    // Authentication functions\n    const checkAuthentication = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const response = await fetch(\"/api/auth/validate\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success && data.valid) {\n                setIsAuthenticated(true);\n                setUser(data.user);\n                setLicenseStatus(data.licenseStatus);\n            } else {\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"user\");\n                router.push(\"/login\");\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        } finally{\n            setAuthLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        }\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    // Handle login method selection (doesn't start the process)\n    const handleLoginMethodSelect = (method)=>{\n        setLoginMethod(method);\n    };\n    // Check license validity before starting bot\n    const checkLicenseValidity = ()=>{\n        // Admin users don't need license validation\n        if (user && user.role === \"admin\") {\n            return {\n                valid: true\n            };\n        }\n        if (!licenseStatus) {\n            return {\n                valid: false,\n                error: \"License information not available\"\n            };\n        }\n        if (licenseStatus.license_status !== \"valid\") {\n            let errorMessage = \"Your license is not valid\";\n            switch(licenseStatus.license_status){\n                case \"expired\":\n                    errorMessage = \"Your license has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"Your license has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"Your license is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage,\n                status: licenseStatus.license_status\n            };\n        }\n        return {\n            valid: true\n        };\n    };\n    // Handle license renewal\n    const handleLicenseRenewal = async ()=>{\n        if (!newLicenseKey.trim()) {\n            setMessage(\"Please enter a valid license key\");\n            return;\n        }\n        setLicenseRenewalLoading(true);\n        setMessage(\"Renewing license...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/auth/renew-license\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    newLicenseKey: newLicenseKey.trim()\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(\"License renewed successfully! You can now start the bot.\");\n                setShowLicenseRenewal(false);\n                setNewLicenseKey(\"\");\n                // Refresh authentication to get updated license status\n                await checkAuthentication();\n            } else {\n                setMessage(data.error || \"Failed to renew license\");\n            }\n        } catch (error) {\n            setMessage(\"Error occurred while renewing license\");\n        } finally{\n            setLicenseRenewalLoading(false);\n        }\n    };\n    // Handle the actual start process\n    const handleBeginClick = async ()=>{\n        // Check authentication before starting\n        if (!isAuthenticated || !user) {\n            setMessage(\"Please login to use this feature\");\n            router.push(\"/login\");\n            return;\n        }\n        // Check license validity before proceeding\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            return;\n        }\n        // Reset all states\n        setMessage(\"\");\n        setBookTitle(\"\");\n        setNeedsPlaywright(false);\n        setScreenshot(\"\");\n        setStoryContent(\"\");\n        setShowBookConfirmation(false);\n        setCurrentSrp(\"\");\n        setTargetSrp(\"\");\n        setShowInitialSrpInput(false);\n        setAutomationComplete(false);\n        // Reset credential states\n        setUserSchool(\"\");\n        setUserEmail(\"\");\n        setUserPassword(\"\");\n        setLoginKey(\"\");\n        // Mark that user has started the process\n        setHasStarted(true);\n        // Show credential input first\n        setShowCredentialInput(true);\n        // Load user's saved credentials\n        await loadSavedCredentials();\n    };\n    // Load user's saved credentials\n    const loadSavedCredentials = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/credentials/list\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSavedCredentials(data.credentials);\n            }\n        } catch (error) {\n            console.error(\"Failed to load saved credentials:\", error);\n        }\n    };\n    // Handle credential submission\n    const handleCredentialSubmit = async ()=>{\n        if (credentialMode === \"key\") {\n            // Use saved credentials with login key\n            if (!loginKey) {\n                setMessage(\"Please enter your login key\");\n                return;\n            }\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    // Proceed with automation using saved credentials\n                    setShowCredentialInput(false);\n                    setMessage(\"Please enter how much SRP you need to earn:\");\n                    setShowInitialSrpInput(true);\n                } else {\n                    setMessage(data.error || \"Invalid login key\");\n                }\n            } catch (error) {\n                setMessage(\"Error retrieving credentials\");\n            }\n        } else {\n            // Use entered credentials\n            if (!userSchool || !userEmail || !userPassword) {\n                setMessage(\"Please enter school, email and password\");\n                return;\n            }\n            // Ask if user wants to save credentials\n            if (confirm(\"Would you like to save these credentials for future use? You will receive a secure login key.\")) {\n                try {\n                    const token = localStorage.getItem(\"token\");\n                    const response = await fetch(\"/api/credentials/save\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            \"Authorization\": \"Bearer \".concat(token)\n                        },\n                        body: JSON.stringify({\n                            loginMethod,\n                            school: userSchool,\n                            email: userEmail,\n                            password: userPassword\n                        })\n                    });\n                    const data = await response.json();\n                    if (data.success) {\n                        alert(\"Credentials saved! Your login key is: \".concat(data.loginKey, \"\\n\\nPlease save this key securely. You can use it for future logins.\"));\n                    }\n                } catch (error) {\n                    console.error(\"Failed to save credentials:\", error);\n                }\n            }\n            // Proceed with automation\n            setShowCredentialInput(false);\n            setMessage(\"Please enter how much SRP you need to earn:\");\n            setShowInitialSrpInput(true);\n        }\n    };\n    const handleSrpSubmit = async ()=>{\n        // Validate SRP input\n        if (!targetSrp || isNaN(targetSrp) || parseInt(targetSrp) <= 0) {\n            setMessage(\"Please enter a valid SRP target (positive number)\");\n            return;\n        }\n        // Check license validity again before starting automation\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            setShowInitialSrpInput(false);\n            return;\n        }\n        setLoading(true);\n        setShowInitialSrpInput(false);\n        const isNormalLogin = loginMethod === \"normal\";\n        const isMicrosoftLogin = loginMethod === \"microsoft\";\n        const isGoogleLogin = loginMethod === \"google\";\n        // Get credentials\n        let credentials = null;\n        if (credentialMode === \"key\" && loginKey) {\n            // Get credentials from login key\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    credentials = data.credentials;\n                } else {\n                    setLoading(false);\n                    setMessage(data.error || \"Failed to retrieve credentials\");\n                    return;\n                }\n            } catch (error) {\n                setLoading(false);\n                setMessage(\"Error retrieving credentials\");\n                return;\n            }\n        } else if (credentialMode === \"enter\" && userSchool && userEmail && userPassword) {\n            // Use entered credentials\n            credentials = {\n                school: userSchool,\n                email: userEmail,\n                password: userPassword,\n                loginMethod: loginMethod\n            };\n        } else {\n            setLoading(false);\n            setMessage(\"No credentials available\");\n            return;\n        }\n        if (isNormalLogin) {\n            setMessage(\"Preparing to start...\");\n        } else if (isMicrosoftLogin) {\n            setMessage(\"Starting Microsoft login automation...\");\n        } else if (isGoogleLogin) {\n            setMessage(\"Starting Google login automation...\");\n        }\n        try {\n            let apiEndpoint, requestBody;\n            if (isNormalLogin) {\n                apiEndpoint = \"/api/sparxreader/start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isMicrosoftLogin) {\n                apiEndpoint = \"/api/sparxreader/microsoft-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isGoogleLogin) {\n                apiEndpoint = \"/api/sparxreader/google-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            }\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(apiEndpoint, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Book title and SRP extracted, show confirmation dialog\n                setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                setBookTitle(data.bookTitle);\n                setCurrentSrp(data.currentSrp);\n                setShowBookConfirmation(true);\n                if (data.screenshot) {\n                    setScreenshot(data.screenshot);\n                }\n            } else {\n                setMessage(data.error || \"Failed to start\");\n                if (data.needsPlaywright) {\n                    setNeedsPlaywright(true);\n                }\n            }\n        } catch (error) {\n            setMessage(\"Error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleYesClick = async ()=>{\n        setLoading(true);\n        setShowBookConfirmation(false);\n        setIsInQueue(true);\n        setMessage(\"Adding to queue...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            // Get credentials based on mode\n            let credentials = null;\n            if (credentialMode === \"key\" && loginKey) {\n                // Get credentials from login key\n                try {\n                    const credResponse = await fetch(\"/api/credentials/get\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            \"Authorization\": \"Bearer \".concat(token)\n                        },\n                        body: JSON.stringify({\n                            loginKey\n                        })\n                    });\n                    const credData = await credResponse.json();\n                    if (credData.success) {\n                        credentials = credData.credentials;\n                    } else {\n                        setIsInQueue(false);\n                        setLoading(false);\n                        setMessage(credData.error || \"Failed to retrieve credentials\");\n                        return;\n                    }\n                } catch (error) {\n                    setIsInQueue(false);\n                    setLoading(false);\n                    setMessage(\"Error retrieving credentials\");\n                    return;\n                }\n            } else if (credentialMode === \"enter\" && userSchool && userEmail && userPassword) {\n                // Use entered credentials\n                credentials = {\n                    school: userSchool,\n                    email: userEmail,\n                    password: userPassword\n                };\n            } else {\n                setIsInQueue(false);\n                setLoading(false);\n                setMessage(\"No credentials available\");\n                return;\n            }\n            // Create a queue job instead of direct automation\n            const response = await fetch(\"/api/queue/jobs\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    job_type: \"sparx_reader\",\n                    job_data: {\n                        school: credentials.school,\n                        email: credentials.email,\n                        username: credentials.email,\n                        password: credentials.password,\n                        login_type: loginMethod,\n                        bookTitle: bookTitle\n                    },\n                    srp_target: targetSrp ? parseInt(targetSrp) : 100,\n                    priority: 0\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setJobId(data.job_id);\n                setMessage(\"Added to queue! Checking position...\");\n                // Start monitoring queue position\n                startQueueMonitoring(data.job_id);\n            } else {\n                setIsInQueue(false);\n                setMessage(data.error || \"Failed to add job to queue\");\n            }\n        } catch (error) {\n            setIsInQueue(false);\n            setMessage(\"Error occurred while adding to queue\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const startQueueMonitoring = (jobId)=>{\n        const checkPosition = async ()=>{\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/queue/position?job_id=\".concat(jobId), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n                const data = await response.json();\n                if (data.success) {\n                    setQueuePosition(data.position);\n                    if (data.position === 1 && data.status === \"processing\") {\n                        // Job is now being processed!\n                        setIsProcessing(true);\n                        setIsInQueue(false);\n                        setMessage(\"Your job is now being processed!\");\n                        // Start monitoring job completion\n                        startJobMonitoring(jobId);\n                    } else if (data.status === \"completed\") {\n                        // Job completed\n                        handleJobCompletion(data.result);\n                    } else if (data.status === \"failed\") {\n                        // Job failed\n                        setIsInQueue(false);\n                        setIsProcessing(false);\n                        setMessage(data.error || \"Job failed to process\");\n                    } else {\n                        // Still in queue\n                        setMessage(\"Queue position: #\".concat(data.position));\n                        // Check again in 3 seconds\n                        setTimeout(checkPosition, 3000);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error checking queue position:\", error);\n                setTimeout(checkPosition, 5000); // Retry in 5 seconds\n            }\n        };\n        checkPosition();\n    };\n    const startJobMonitoring = (jobId)=>{\n        const checkJobStatus = async ()=>{\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/queue/jobs/\".concat(jobId), {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n                const data = await response.json();\n                if (data.success) {\n                    if (data.job.status === \"completed\") {\n                        handleJobCompletion(data.job.result);\n                    } else if (data.job.status === \"failed\") {\n                        setIsProcessing(false);\n                        setMessage(data.job.error || \"Job failed to process\");\n                    } else {\n                        // Still processing, check again\n                        setTimeout(checkJobStatus, 5000);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error checking job status:\", error);\n                setTimeout(checkJobStatus, 5000);\n            }\n        };\n        checkJobStatus();\n    };\n    const handleJobCompletion = (result)=>{\n        setIsProcessing(false);\n        setIsInQueue(false);\n        if (result && result.success) {\n            setMessage(\"Automation completed! Displaying results...\");\n            setStoryContent(result.storyContent);\n            setBookTitle(result.bookTitle);\n            // Start the question simulation\n            setTimeout(()=>{\n                setAutomationComplete(true);\n                setIsAutomationRunning(true);\n                setSrpEarned(0);\n                setQuestionNumber(0);\n                setQuestionHistory([]);\n                setMessage(\"\");\n            }, 1500);\n            if (result.screenshot) {\n                setScreenshot(result.screenshot);\n            }\n        } else {\n            setMessage((result === null || result === void 0 ? void 0 : result.error) || \"Job completed but with errors\");\n        }\n    };\n    const handleNoClick = async ()=>{\n        setLoading(true);\n        setMessage(\"Finding a different book with same SRP target...\");\n        try {\n            // Close current session\n            const token = localStorage.getItem(\"token\");\n            await fetch(\"/api/sparxreader/close\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            // Restart automation with existing target SRP\n            setLoading(true);\n            setShowInitialSrpInput(false);\n            setMessage(\"Preparing to start with new book...\");\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/sparxreader/start\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                        targetSrp: parseInt(targetSrp)\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                    setBookTitle(data.bookTitle);\n                    setCurrentSrp(data.currentSrp);\n                    setShowBookConfirmation(true);\n                    if (data.screenshot) {\n                        setScreenshot(data.screenshot);\n                    }\n                } else {\n                    setMessage(data.error || \"Failed to start\");\n                    if (data.needsPlaywright) {\n                        setNeedsPlaywright(true);\n                    }\n                }\n            } catch (error) {\n                setMessage(\"Error occurred while restarting\");\n            }\n        } catch (error) {\n            setMessage(\"Error closing previous session\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBackClick = ()=>{\n        setIsAutomationRunning(false);\n        setIsProcessing(false);\n        setCurrentQuestion(\"\");\n        setCurrentAnswer(\"\");\n        setQuestionNumber(0);\n        setSrpEarned(0);\n        setQuestionHistory([]);\n        setMessage(\"\");\n        setShowBookConfirmation(false);\n        setShowInitialSrpInput(false);\n        setHasStarted(false);\n        setAutomationComplete(false);\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 817,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 819,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 820,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 818,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 816,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 834,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"p-6 border-b border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto relative\",\n                            children: [\n                                (isAutomationRunning || isProcessing) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackClick,\n                                    className: \"absolute left-0 top-1/2 transform -translate-y-1/2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-all duration-200 flex items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"<\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 top-1/2 transform -translate-y-1/2 flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        user === null || user === void 0 ? void 0 : user.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"Administrator\" : \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 853,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                (user === null || user === void 0 ? void 0 : user.role) === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/admin\"),\n                                                    className: \"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Admin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/queue\"),\n                                                    className: \"px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Queue\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogout,\n                                                    className: \"px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-light text-white text-center\",\n                                    children: \"Sparx Reader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 881,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-center text-gray-400 mt-1 text-sm\",\n                                    children: \"Automated Question Solving\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 839,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 838,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-4xl mx-auto\",\n                            children: [\n                                (isInQueue || isProcessing) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-8 text-center shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-20 h-20 mx-auto mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 border-4 border-blue-500/30 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 bg-blue-500 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.5s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 19\n                                            }, this),\n                                            isInQueue ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent mb-4\",\n                                                        children: \"� In Queue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 911,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 text-lg\",\n                                                        children: queuePosition ? \"Queue Position: #\".concat(queuePosition) : \"Checking queue position...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full max-w-md mx-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 border-4 border-yellow-500 border-t-transparent rounded-full animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 919,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-300\",\n                                                                    children: \"Waiting for your turn...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 920,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 918,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4\",\n                                                        children: \"\\uD83D\\uDE80 Processing Automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 text-lg\",\n                                                        children: \"AI is solving questions in the background...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full max-w-md mx-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 border-4 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 932,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-300\",\n                                                                    children: \"Processing your request...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 933,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 931,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 930,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 15\n                                }, this),\n                                isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                    children: \"\\uD83D\\uDCCA Live Progress Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 947,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-24 h-24 mx-auto mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-24 h-24 transform -rotate-90\",\n                                                                        viewBox: \"0 0 100 100\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                className: \"text-gray-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 958,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"url(#srpGradient)\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                strokeDasharray: \"\".concat(srpEarned / parseInt(targetSrp || 1) * 251.2, \" 251.2\"),\n                                                                                className: \"transition-all duration-1000 ease-out drop-shadow-lg\",\n                                                                                strokeLinecap: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 959,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                                    id: \"srpGradient\",\n                                                                                    x1: \"0%\",\n                                                                                    y1: \"0%\",\n                                                                                    x2: \"100%\",\n                                                                                    y2: \"100%\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"0%\",\n                                                                                            stopColor: \"#10B981\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 970,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"50%\",\n                                                                                            stopColor: \"#3B82F6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 971,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"100%\",\n                                                                                            stopColor: \"#8B5CF6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 972,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 969,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 968,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 957,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xl font-bold text-white block\",\n                                                                                    children: srpEarned\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 978,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: \"SRP\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 979,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 977,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 976,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 rounded-full bg-gradient-to-r from-green-500/20 to-blue-500/20 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 983,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium mb-1\",\n                                                                children: \"SRP Earned\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-400 text-xs\",\n                                                                children: [\n                                                                    \"Target: \",\n                                                                    targetSrp\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    Math.round(srpEarned / parseInt(targetSrp || 1) * 100),\n                                                                    \"% Complete\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold text-blue-400 mb-1 animate-pulse\",\n                                                                        children: questionNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 995,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"Questions Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-700 rounded-full h-3 overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-blue-500 to-cyan-400 h-3 rounded-full transition-all duration-1000 ease-out relative\",\n                                                                            style: {\n                                                                                width: \"\".concat(Math.min(questionNumber / 10 * 100, 100), \"%\")\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-white/30 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1002,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 998,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            Math.min(Math.round(questionNumber / 10 * 100), 100),\n                                                                            \"% of estimated session\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1005,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83E\\uDDE0 AI Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mx-auto mb-3 flex items-center justify-center animate-pulse\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1016,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1015,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"System Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1018,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-700 rounded-lg p-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-green-400 mb-2\",\n                                                                                children: \"\\uD83D\\uDFE2 Active & Processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1021,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400 mb-1\",\n                                                                                children: \"Performance Metrics\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1022,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-white\",\n                                                                                children: [\n                                                                                    \"⚡ \",\n                                                                                    questionNumber > 0 ? Math.round(elapsedTime / questionNumber) : 0,\n                                                                                    \"s per question\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1023,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-400\",\n                                                                                children: [\n                                                                                    \"\\uD83C\\uDFAF \",\n                                                                                    questionNumber > 0 ? Math.round(questionNumber / elapsedTime * 60) : 0,\n                                                                                    \" questions/min\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1026,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-purple-400 mt-1\",\n                                                                                children: [\n                                                                                    \"⏱️ \",\n                                                                                    formatTime(elapsedTime),\n                                                                                    \" elapsed\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1029,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1020,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83D\\uDE80 AI Engine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1034,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 952,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this),\n                                currentQuestion && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-slide-up\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4 animate-pulse\",\n                                                        children: questionNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                                children: \"✅ Question Solved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"AI successfully processed this question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-auto flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400 font-medium\",\n                                                                children: \"COMPLETED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1055,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/60 border border-gray-600 rounded-xl p-6 mb-6 backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"Q\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1064,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1063,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-base leading-relaxed mb-3\",\n                                                                    children: currentQuestion\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1067,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 bg-green-600 text-white text-xs rounded font-medium\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                Math.floor(Math.random() * 3) + 1,\n                                                                                \" SRP\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1069,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"• Solved automatically\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1072,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1068,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1066,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1062,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentAnswer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"AI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-400 font-medium mb-2\",\n                                                                    children: \"\\uD83E\\uDD16 AI Solution:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1085,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-200 leading-relaxed\",\n                                                                    children: currentAnswer\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1086,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1084,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1080,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1079,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 17\n                                    }, this)\n                                }, animationKey, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 15\n                                }, this),\n                                !hasStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-10 max-w-lg mx-auto shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-16 h-16 mx-auto mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-bounce\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-2xl\",\n                                                                    children: \"\\uD83D\\uDE80\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1102,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1101,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1104,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-purple-400 rounded-full animate-ping\",\n                                                                style: {\n                                                                    animationDelay: \"0.5s\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1105,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3\",\n                                                        children: \"Start Automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1107,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-base\",\n                                                        children: \"\\uD83E\\uDD16 AI-powered question solving for Sparx Reader\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                        children: \"\\uD83D\\uDD10 Choose Login Method\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1115,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"normal\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"normal\" ? \"bg-gradient-to-r from-green-600 to-green-500 text-white ring-2 ring-green-400 shadow-lg shadow-green-500/25\" : \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-500 text-white shadow-lg hover:shadow-green-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDC64\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Normal Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1129,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"normal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1130,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1119,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"microsoft\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"microsoft\" ? \"bg-gradient-to-r from-blue-600 to-blue-500 text-white ring-2 ring-blue-400 shadow-lg shadow-blue-500/25\" : \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-500 text-white shadow-lg hover:shadow-blue-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83C\\uDFE2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1142,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Microsoft Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1143,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"microsoft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1144,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1133,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"google\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"google\" ? \"bg-gradient-to-r from-red-600 to-red-500 text-white ring-2 ring-red-400 shadow-lg shadow-red-500/25\" : \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-500 text-white shadow-lg hover:shadow-red-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDD0D\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1156,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Google Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1157,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"google\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1158,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1147,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: [\n                                                                \"Selected: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        loginMethod === \"normal\" && \"Normal Login\",\n                                                                        loginMethod === \"microsoft\" && \"Microsoft Login\",\n                                                                        loginMethod === \"google\" && \"Google Login\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1166,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1165,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1164,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBeginClick,\n                                                        disabled: loading,\n                                                        className: \"w-full py-3 px-6 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Begin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1173,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1163,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1098,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1097,\n                                    columnNumber: 15\n                                }, this),\n                                showInitialSrpInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1191,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Set SRP Target\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"How much SRP do you want to earn?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1194,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1189,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: targetSrp,\n                                                        onChange: (e)=>setTargetSrp(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleSrpSubmit(),\n                                                        placeholder: \"Enter target (e.g., 50)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center\",\n                                                        min: \"1\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 text-center\",\n                                                        children: \"Automation will stop when target is reached\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1208,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSrpSubmit,\n                                                                disabled: loading || !targetSrp,\n                                                                className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1220,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Starting...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1219,\n                                                                    columnNumber: 27\n                                                                }, this) : \"Start\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1213,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowInitialSrpInput(false),\n                                                                disabled: loading,\n                                                                className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1225,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1197,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1188,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1187,\n                                    columnNumber: 15\n                                }, this),\n                                showCredentialInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"\\uD83D\\uDD10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1244,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Login Credentials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1246,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            loginMethod === \"normal\" && \"Enter your Sparx Learning credentials\",\n                                                            loginMethod === \"microsoft\" && \"Enter your Microsoft account credentials\",\n                                                            loginMethod === \"google\" && \"Enter your Google account credentials\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1247,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1242,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex bg-gray-800 rounded-lg p-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"enter\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"enter\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Enter Credentials\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1257,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"key\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"key\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Use Login Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1267,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1256,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1255,\n                                                columnNumber: 19\n                                            }, this),\n                                            credentialMode === \"enter\" ? /* Enter Credentials Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: userSchool,\n                                                        onChange: (e)=>setUserSchool(e.target.value),\n                                                        placeholder: \"School name (e.g., theangmeringschool)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1283,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: userEmail,\n                                                        onChange: (e)=>setUserEmail(e.target.value),\n                                                        placeholder: \"Email address\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1291,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        value: userPassword,\n                                                        onChange: (e)=>setUserPassword(e.target.value),\n                                                        placeholder: \"Password\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1298,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDCA1 Your credentials will be encrypted and stored securely. You'll receive a login key for future use.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1282,\n                                                columnNumber: 21\n                                            }, this) : /* Use Login Key Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: loginKey,\n                                                        onChange: (e)=>setLoginKey(e.target.value),\n                                                        placeholder: \"Enter your login key (e.g., SLK-ABC12345)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center font-mono\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1312,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    savedCredentials.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mb-2\",\n                                                                children: \"Your saved login keys:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1323,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1 max-h-32 overflow-y-auto\",\n                                                                children: savedCredentials.map((cred, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setLoginKey(cred.loginKey),\n                                                                        className: \"w-full text-left px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-sm text-gray-300 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-mono text-blue-400\",\n                                                                                children: cred.loginKey\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1331,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-2 text-xs\",\n                                                                                children: [\n                                                                                    \"(\",\n                                                                                    cred.loginMethod,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1332,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1326,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1324,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1322,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDD11 Use your previously generated login key to access saved credentials.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1339,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1311,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCredentialSubmit,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1353,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1352,\n                                                            columnNumber: 25\n                                                        }, this) : \"Continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowCredentialInput(false);\n                                                            setHasStarted(false);\n                                                        },\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1345,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1241,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1240,\n                                    columnNumber: 15\n                                }, this),\n                                showBookConfirmation && bookTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-lg w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"B\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1379,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1378,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Book Found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1381,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Confirm to start automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1377,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/30 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-white mb-4\",\n                                                        children: bookTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Current SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1389,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: currentSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1390,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1388,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Target SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: targetSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1394,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1387,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1385,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleYesClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1407,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Starting...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1406,\n                                                            columnNumber: 25\n                                                        }, this) : \"Yes, Start\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1400,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleNoClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? \"Finding...\" : \"Find Different\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1376,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1375,\n                                    columnNumber: 15\n                                }, this),\n                                questionHistory.length > 0 && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"H\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1429,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Question History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1428,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-60 overflow-y-auto\",\n                                                children: questionHistory.slice(-5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/30 rounded p-3 border-l-4 border-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 font-medium text-sm\",\n                                                                        children: [\n                                                                            \"Q\",\n                                                                            item.number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1436,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 text-xs\",\n                                                                        children: \"Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1437,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1435,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm mb-2\",\n                                                                children: [\n                                                                    item.question.substring(0, 100),\n                                                                    \"...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1439,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: [\n                                                                    \"Answer: \",\n                                                                    item.answer\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1440,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1434,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1432,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1427,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1426,\n                                    columnNumber: 15\n                                }, this),\n                                message && !showInitialSrpInput && !showBookConfirmation && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded p-4 inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white\",\n                                            children: message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 1452,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1451,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1450,\n                                    columnNumber: 15\n                                }, this),\n                                needsPlaywright && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-red-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1463,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1462,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Setup Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1465,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Playwright browsers need to be installed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1466,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1461,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-sm mb-2\",\n                                                        children: \"Run this command in your terminal:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-black text-blue-400 p-2 rounded block text-sm\",\n                                                        children: \"npx playwright install chromium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1471,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1469,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-xs text-center\",\n                                                children: \"After installation, refresh this page and try again.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1476,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1460,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1459,\n                                    columnNumber: 15\n                                }, this),\n                                showLicenseRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-8 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-500 text-2xl\",\n                                                            children: \"⚠️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1489,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1488,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-medium text-white mb-2\",\n                                                        children: \"License Renewal Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1491,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            licenseStatus && licenseStatus.license_status === \"expired\" && \"Your license has expired.\",\n                                                            licenseStatus && licenseStatus.license_status === \"maxed_out\" && \"Your license has reached maximum uses.\",\n                                                            licenseStatus && licenseStatus.license_status === \"inactive\" && \"Your license is inactive.\",\n                                                            (!licenseStatus || licenseStatus.license_status === \"valid\") && \"Your license is not valid.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1492,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mt-2\",\n                                                        children: \"Please enter a new license key to continue using the bot.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1498,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1487,\n                                                columnNumber: 19\n                                            }, this),\n                                            licenseStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 border border-gray-700 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-white font-medium mb-2\",\n                                                        children: \"Current License Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1505,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Key: \",\n                                                                    licenseStatus.key_code || \"N/A\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1507,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium \".concat(licenseStatus.license_status === \"expired\" ? \"text-red-400\" : licenseStatus.license_status === \"maxed_out\" ? \"text-orange-400\" : licenseStatus.license_status === \"inactive\" ? \"text-gray-400\" : \"text-green-400\"),\n                                                                        children: [\n                                                                            licenseStatus.license_status === \"expired\" && \"Expired\",\n                                                                            licenseStatus.license_status === \"maxed_out\" && \"Max Uses Reached\",\n                                                                            licenseStatus.license_status === \"inactive\" && \"Inactive\",\n                                                                            licenseStatus.license_status === \"valid\" && \"Valid\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1508,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1508,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            licenseStatus.expires_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Expires: \",\n                                                                    new Date(licenseStatus.expires_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1520,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            licenseStatus.max_uses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Uses: \",\n                                                                    licenseStatus.current_uses || 0,\n                                                                    \"/\",\n                                                                    licenseStatus.max_uses\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1523,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1506,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1504,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-white font-medium mb-2\",\n                                                        children: \"New License Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1530,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newLicenseKey,\n                                                        onChange: (e)=>setNewLicenseKey(e.target.value),\n                                                        placeholder: \"Enter your new license key (e.g., SRX-XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXXXX)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:border-blue-500 focus:outline-none\",\n                                                        disabled: licenseRenewalLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1529,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLicenseRenewal,\n                                                        disabled: licenseRenewalLoading || !newLicenseKey.trim(),\n                                                        className: \"flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded transition-colors duration-200\",\n                                                        children: licenseRenewalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1549,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Renewing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1548,\n                                                            columnNumber: 25\n                                                        }, this) : \"Renew License\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1542,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowLicenseRenewal(false);\n                                                            setNewLicenseKey(\"\");\n                                                            setMessage(\"\");\n                                                        },\n                                                        disabled: licenseRenewalLoading,\n                                                        className: \"px-6 py-3 border border-gray-600 text-gray-400 hover:text-white hover:border-gray-500 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1556,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1486,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1485,\n                                    columnNumber: 15\n                                }, this),\n                                screenshot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"S\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1577,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Browser State\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1576,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded border border-gray-700 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: screenshot,\n                                                    alt: \"Browser screenshot\",\n                                                    className: \"w-full h-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1581,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1580,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1575,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1574,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 890,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 889,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 836,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n        lineNumber: 832,\n        columnNumber: 5\n    }, this);\n}\n_s(SparxReaderPage, \"3tPyRYBhGPqvkw99Z4exibXXewo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SparxReaderPage;\nvar _c;\n$RefreshReg$(_c, \"SparxReaderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.jsx\n"));

/***/ })

});