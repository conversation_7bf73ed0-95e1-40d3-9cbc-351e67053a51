"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.jsx":
/*!****************************!*\
  !*** ./app/admin/page.jsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminDashboard() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"keys\");\n    const [keys, setKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [addons, setAddons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [queueConfig, setQueueConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        maxConcurrentJobs: 3,\n        browserTimeoutMinutes: 9\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keyForm, setKeyForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        duration: \"30\",\n        maxUses: \"1\",\n        priorityLevel: \"0\",\n        maxAccountsPerBatch: \"10\",\n        schedulingAccess: false\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        const userData = localStorage.getItem(\"user\");\n        if (!token || !userData) {\n            router.push(\"/login\");\n            return;\n        }\n        const parsedUser = JSON.parse(userData);\n        if (parsedUser.role !== \"admin\") {\n            router.push(\"/\");\n            return;\n        }\n        setUser(parsedUser);\n        fetchData(token);\n    }, []);\n    const fetchData = async (token)=>{\n        try {\n            const [keysRes, addonsRes, usersRes, queueRes] = await Promise.all([\n                fetch(\"/api/admin/keys\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                }),\n                fetch(\"/api/admin/addons\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                }),\n                fetch(\"/api/admin/users\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                }),\n                fetch(\"/api/admin/queue-config\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                })\n            ]);\n            const keysData = await keysRes.json();\n            const addonsData = await addonsRes.json();\n            const usersData = await usersRes.json();\n            const queueData = await queueRes.json();\n            if (keysData.success) {\n                setKeys(keysData.keys || []);\n            }\n            if (addonsData.success) {\n                setAddons(addonsData.addons || []);\n            }\n            if (usersData.success) {\n                setUsers(usersData.users || []);\n            }\n            if (queueData.success) {\n                setQueueConfig(queueData.config || {\n                    maxConcurrentJobs: 3,\n                    browserTimeoutMinutes: 9\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createKey = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/keys\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    duration: parseInt(keyForm.duration),\n                    maxUses: parseInt(keyForm.maxUses),\n                    priority_level: parseInt(keyForm.priorityLevel),\n                    max_accounts_per_batch: parseInt(keyForm.maxAccountsPerBatch),\n                    scheduling_access: keyForm.schedulingAccess\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                fetchData(token);\n                setKeyForm({\n                    duration: \"30\",\n                    maxUses: \"1\",\n                    selectedAddons: []\n                });\n                alert(\"License key created successfully!\\nKey: \".concat(data.key.keyCode));\n            } else {\n                alert(\"Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error(\"Error creating key:\", error);\n            alert(\"Failed to create license key\");\n        }\n    };\n    const toggleUserStatus = async (userId)=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/users\", {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    userId,\n                    action: \"toggle_status\"\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                fetchData(token);\n            } else {\n                alert(\"Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error(\"Error toggling user status:\", error);\n            alert(\"Failed to toggle user status\");\n        }\n    };\n    const logoutAllUserSessions = async (userId)=>{\n        if (!confirm(\"Are you sure you want to logout all sessions for this user?\")) {\n            return;\n        }\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/users\", {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    userId,\n                    action: \"logout_all\"\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                alert(\"All user sessions have been logged out\");\n            } else {\n                alert(\"Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error(\"Error logging out user sessions:\", error);\n            alert(\"Failed to logout user sessions\");\n        }\n    };\n    const deactivateLicenseKey = async (keyId)=>{\n        if (!confirm(\"Are you sure you want to deactivate this license key?\")) {\n            return;\n        }\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/keys\", {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    keyId,\n                    action: \"deactivate\"\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                fetchData(token);\n                alert(\"License key deactivated successfully\");\n            } else {\n                alert(\"Error: \".concat(data.error));\n            }\n        } catch (error) {\n            console.error(\"Error deactivating license key:\", error);\n            alert(\"Failed to deactivate license key\");\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/login\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n            lineNumber: 217,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b border-gray-800 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-light text-white\",\n                                            children: \"Admin Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Manage license keys and system settings\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-300\",\n                                            children: [\n                                                \"Welcome, \",\n                                                user === null || user === void 0 ? void 0 : user.username\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: logout,\n                                            className: \"px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-colors\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex space-x-8\",\n                                        children: [\n                                            {\n                                                id: \"keys\",\n                                                label: \"License Keys\"\n                                            },\n                                            {\n                                                id: \"users\",\n                                                label: \"Users\"\n                                            },\n                                            {\n                                                id: \"queue\",\n                                                label: \"Queue Config\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? \"border-blue-500 text-blue-400\" : \"border-transparent text-gray-400 hover:text-gray-300\"),\n                                                children: tab.label\n                                            }, tab.id, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            activeTab === \"keys\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-medium text-white mb-6\",\n                                                children: \"Create New License Key\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Duration (days)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: keyForm.duration,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        duration: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\",\n                                                                min: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Max Uses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: keyForm.maxUses,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        maxUses: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\",\n                                                                min: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Priority Level\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: keyForm.priorityLevel,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        priorityLevel: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\",\n                                                                min: \"0\",\n                                                                max: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Max Accounts Per Batch\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: keyForm.maxAccountsPerBatch,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        maxAccountsPerBatch: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\",\n                                                                min: \"0\",\n                                                                max: \"1000\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Scheduling Access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: keyForm.schedulingAccess,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        schedulingAccess: e.target.checked\n                                                                    }),\n                                                                className: \"mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: createKey,\n                                                className: \"mt-6 px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors\",\n                                                children: \"Generate Key\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-medium text-white mb-6\",\n                                                children: \"Existing Keys\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"border-b border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Key Code\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Created\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Expires\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Uses\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            children: keys.map((key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"border-b border-gray-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4 text-white font-mono text-sm\",\n                                                                            children: key.key_code\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                            children: new Date(key.created_at).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                            children: new Date(key.expires_at).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                            children: [\n                                                                                key.users_count,\n                                                                                \"/\",\n                                                                                key.max_uses\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded text-xs \".concat(new Date(key.expires_at) > new Date() && key.is_active ? \"bg-green-500/20 text-green-400\" : \"bg-red-500/20 text-red-400\"),\n                                                                                children: new Date(key.expires_at) > new Date() && key.is_active ? \"Active\" : \"Expired\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                                lineNumber: 378,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4\",\n                                                                            children: key.is_active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>deactivateLicenseKey(key.id),\n                                                                                className: \"px-2 py-1 bg-red-500/20 text-red-400 hover:bg-red-500/30 rounded text-xs transition-colors\",\n                                                                                children: \"Deactivate\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                                lineNumber: 388,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, key.id, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"users\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-medium text-white mb-6\",\n                                        children: \"User Management\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"License Key\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Last Login\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Actions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"border-b border-gray-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4 text-white font-medium\",\n                                                                        children: user.username\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded text-xs \".concat(user.role === \"admin\" ? \"bg-purple-500/20 text-purple-400\" : \"bg-blue-500/20 text-blue-400\"),\n                                                                            children: user.role\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4 text-gray-300 font-mono text-sm\",\n                                                                        children: user.key_code || \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                        children: new Date(user.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 439,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                        children: user.last_login ? new Date(user.last_login).toLocaleDateString() : \"Never\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded text-xs \".concat(user.is_active ? \"bg-green-500/20 text-green-400\" : \"bg-red-500/20 text-red-400\"),\n                                                                            children: user.is_active ? \"Active\" : \"Inactive\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>toggleUserStatus(user.id),\n                                                                                    className: \"px-2 py-1 rounded text-xs transition-colors \".concat(user.is_active ? \"bg-red-500/20 text-red-400 hover:bg-red-500/30\" : \"bg-green-500/20 text-green-400 hover:bg-green-500/30\"),\n                                                                                    children: user.is_active ? \"Deactivate\" : \"Activate\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                                    lineNumber: 456,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                user.role !== \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>logoutAllUserSessions(user.id),\n                                                                                    className: \"px-2 py-1 bg-orange-500/20 text-orange-400 hover:bg-orange-500/30 rounded text-xs transition-colors\",\n                                                                                    children: \"Logout All\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                                    lineNumber: 467,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, user.id, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-400\",\n                                                children: \"No users found\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"queue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-medium text-white mb-6\",\n                                                children: \"Queue Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Max Concurrent Jobs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                min: \"1\",\n                                                                max: \"10\",\n                                                                value: queueConfig.maxConcurrentJobs,\n                                                                onChange: (e)=>setQueueConfig((prev)=>({\n                                                                            ...prev,\n                                                                            maxConcurrentJobs: parseInt(e.target.value)\n                                                                        })),\n                                                                className: \"w-full px-3 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: \"Number of jobs that can process simultaneously (1-10)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Browser Timeout (minutes)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                min: \"1\",\n                                                                max: \"30\",\n                                                                value: queueConfig.browserTimeoutMinutes,\n                                                                onChange: (e)=>setQueueConfig((prev)=>({\n                                                                            ...prev,\n                                                                            browserTimeoutMinutes: parseInt(e.target.value)\n                                                                        })),\n                                                                className: \"w-full px-3 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: \"Auto-close browsers after this time to prevent stuck jobs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: updateQueueConfig,\n                                                    className: \"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors\",\n                                                    children: \"Update Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4\",\n                                                children: \"Current Queue Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-400\",\n                                                                children: queueConfig.maxConcurrentJobs\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Max Concurrent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                children: [\n                                                                    queueConfig.browserTimeoutMinutes,\n                                                                    \"m\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Browser Timeout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-yellow-400\",\n                                                                children: \"Active\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Queue Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 492,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"5ztHjnoqAzlknqCZulzw+SLUrII=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.jsx\n"));

/***/ })

});