"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/queue/page",{

/***/ "(app-pages-browser)/./app/queue/page.jsx":
/*!****************************!*\
  !*** ./app/queue/page.jsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QueueDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ScheduleCalendar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ScheduleCalendar */ \"(app-pages-browser)/./app/queue/components/ScheduleCalendar.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction QueueDashboard() {\n    var _queueStatus_global_overview_queue_status, _queueStatus_global_overview, _queueStatus_license_features, _queueStatus_global_overview_queue_status1, _queueStatus_global_overview1, _queueStatus_license_features1, _queueStatus_license_features2, _queueStatus_license_features3, _queueStatus_license_features4, _queueStatus_license_features5, _queueStatus_license_features6, _queueStatus_license_features7;\n    _s();\n    const [queueStatus, setQueueStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [batches, setBatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [priorityLevels, setPriorityLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [processingJobs, setProcessingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [realtimeUpdates, setRealtimeUpdates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Form states\n    const [batchForm, setBatchForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        batch_name: \"\",\n        login_type: \"\",\n        accounts: [\n            {\n                school: \"\",\n                email: \"\",\n                password: \"\",\n                login_type: \"regular\"\n            }\n        ],\n        scheduled_time: \"\",\n        priority_override: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadQueueData();\n    }, []);\n    // Dynamic auto-refresh based on activity\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _queueStatus_global_overview_queue_status, _queueStatus_global_overview;\n        if (!realtimeUpdates) return;\n        // Use faster refresh when there are processing jobs\n        const hasProcessingJobs = (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_global_overview = queueStatus.global_overview) === null || _queueStatus_global_overview === void 0 ? void 0 : (_queueStatus_global_overview_queue_status = _queueStatus_global_overview.queue_status) === null || _queueStatus_global_overview_queue_status === void 0 ? void 0 : _queueStatus_global_overview_queue_status.processing_jobs) > 0 || batches.some((batch)=>batch.status === \"processing\");\n        const refreshInterval = hasProcessingJobs ? 5000 : 30000; // 5s for active processing, 30s otherwise\n        const interval = setInterval(()=>{\n            loadQueueData();\n        }, refreshInterval);\n        return ()=>clearInterval(interval);\n    }, [\n        realtimeUpdates,\n        queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_global_overview = queueStatus.global_overview) === null || _queueStatus_global_overview === void 0 ? void 0 : (_queueStatus_global_overview_queue_status = _queueStatus_global_overview.queue_status) === null || _queueStatus_global_overview_queue_status === void 0 ? void 0 : _queueStatus_global_overview_queue_status.processing_jobs,\n        batches\n    ]);\n    const loadQueueData = async ()=>{\n        try {\n            setLoading(true);\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const headers = {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            };\n            // Load queue status\n            const statusResponse = await fetch(\"/api/queue/status?detailed=true\", {\n                headers\n            });\n            if (statusResponse.ok) {\n                const statusData = await statusResponse.json();\n                setQueueStatus(statusData);\n            }\n            // Load batches\n            const batchResponse = await fetch(\"/api/queue/batch\", {\n                headers\n            });\n            if (batchResponse.ok) {\n                const batchData = await batchResponse.json();\n                setBatches(batchData.batches || []);\n            }\n            // Load schedules\n            const scheduleResponse = await fetch(\"/api/queue/schedule\", {\n                headers\n            });\n            if (scheduleResponse.ok) {\n                const scheduleData = await scheduleResponse.json();\n                setSchedules(scheduleData.schedules || []);\n            }\n            // Load priority levels\n            const priorityResponse = await fetch(\"/api/queue/priority-levels\", {\n                headers\n            });\n            if (priorityResponse.ok) {\n                const priorityData = await priorityResponse.json();\n                setPriorityLevels(priorityData);\n            }\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBatchSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/queue/batch\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(batchForm)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                alert('Batch \"'.concat(result.batch.name, '\" created successfully!'));\n                setBatchForm({\n                    batch_name: \"\",\n                    login_type: \"\",\n                    accounts: [\n                        {\n                            school: \"\",\n                            email: \"\",\n                            password: \"\"\n                        }\n                    ],\n                    scheduled_time: \"\",\n                    priority_override: \"\"\n                });\n                loadQueueData(); // Refresh data\n            } else {\n                const error = await response.json();\n                alert(\"Error: \".concat(error.error));\n            }\n        } catch (err) {\n            alert(\"Error: \".concat(err.message));\n        }\n    };\n    const addAccount = ()=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: [\n                    ...prev.accounts,\n                    {\n                        school: \"\",\n                        email: \"\",\n                        password: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeAccount = (index)=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: prev.accounts.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateAccount = (index, field, value)=>{\n        setBatchForm((prev)=>({\n                ...prev,\n                accounts: prev.accounts.map((account, i)=>i === index ? {\n                        ...account,\n                        [field]: value\n                    } : account)\n            }));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-300\",\n                        children: \"Loading queue dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-400 text-xl mb-4\",\n                        children: \"⚠️ Error\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: loadQueueData,\n                        className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-white\",\n                                    children: \"Queue Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-gray-300\",\n                                    children: \"Manage your batch processing and scheduling\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: realtimeUpdates,\n                                                onChange: (e)=>setRealtimeUpdates(e.target.checked),\n                                                className: \"sr-only\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-11 h-6 rounded-full transition-colors \".concat(realtimeUpdates ? \"bg-blue-600\" : \"bg-gray-600\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform \".concat(realtimeUpdates ? \"translate-x-5\" : \"translate-x-0\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-gray-300\",\n                                                children: [\n                                                    \"Real-time \",\n                                                    realtimeUpdates ? \"ON\" : \"OFF\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/\"),\n                                    className: \"px-4 py-2 bg-gray-800 text-gray-300 rounded hover:bg-gray-700 hover:text-white transition-colors\",\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.license_features) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-gray-900 border border-gray-700 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-400 mb-2\",\n                            children: \"Your License Features\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.max_accounts_per_batch || \"Unlimited\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Max Accounts per Batch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.priority_level\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Priority Level\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-400\",\n                                            children: queueStatus.license_features.scheduling_access ? \"✅\" : \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: \"Scheduling Access\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex space-x-8\",\n                        children: [\n                            {\n                                id: \"overview\",\n                                name: \"Overview\"\n                            },\n                            ...(queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features = queueStatus.license_features) === null || _queueStatus_license_features === void 0 ? void 0 : _queueStatus_license_features.max_accounts_per_batch) > 0 ? [\n                                {\n                                    id: \"batches\",\n                                    name: \"Batches\"\n                                },\n                                {\n                                    id: \"create\",\n                                    name: \"Create Batch\"\n                                }\n                            ] : [],\n                            {\n                                id: \"schedule\",\n                                name: \"Schedule\"\n                            }\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeTab === tab.id ? \"border-blue-400 text-blue-400\" : \"border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-600\"),\n                                children: tab.name\n                            }, tab.id, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.global_overview) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Real-time Queue Overview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-400\",\n                                                            children: queueStatus.global_overview.total_users\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Total Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-400\",\n                                                            children: queueStatus.global_overview.total_batches_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Batches Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-yellow-400\",\n                                                            children: queueStatus.global_overview.queue_status.queued_jobs\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Jobs in Queue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-400\",\n                                                            children: queueStatus.global_overview.queue_status.processing_jobs\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Processing Now\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-green-500\",\n                                                            children: queueStatus.global_overview.queue_status.completed_jobs_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Completed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 bg-gray-800 border border-gray-700 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl font-bold text-red-400\",\n                                                            children: queueStatus.global_overview.queue_status.failed_jobs_today\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"Failed Today\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 281,\n                            columnNumber: 15\n                        }, this),\n                        queueStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white\",\n                                        children: \"Your Queue Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Total Batches\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: queueStatus.user_queue_status.total_batches\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Active Jobs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-blue-400\",\n                                                        children: queueStatus.user_queue_status.queued_jobs + queueStatus.user_queue_status.processing_jobs\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Completed Jobs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: queueStatus.user_queue_status.completed_jobs\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800 border border-gray-700 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-400\",\n                                                        children: \"Estimated Wait\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-orange-400\",\n                                                        children: queueStatus.estimated_wait_time.formatted\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 335,\n                            columnNumber: 15\n                        }, this),\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_global_overview1 = queueStatus.global_overview) === null || _queueStatus_global_overview1 === void 0 ? void 0 : (_queueStatus_global_overview_queue_status1 = _queueStatus_global_overview1.queue_status) === null || _queueStatus_global_overview_queue_status1 === void 0 ? void 0 : _queueStatus_global_overview_queue_status1.processing_jobs) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Live Processing (\",\n                                            queueStatus.global_overview.queue_status.processing_jobs,\n                                            \" active)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-white mb-2\",\n                                                children: \"Jobs Processing\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: [\n                                                    queueStatus.global_overview.queue_status.processing_jobs,\n                                                    \" job\",\n                                                    queueStatus.global_overview.queue_status.processing_jobs !== 1 ? \"s\" : \"\",\n                                                    \" currently being processed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-2\",\n                                                children: \"Real-time updates every 5 seconds\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 372,\n                            columnNumber: 15\n                        }, this),\n                        (queueStatus === null || queueStatus === void 0 ? void 0 : queueStatus.queue_positions) && queueStatus.queue_positions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4 border-b border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-white\",\n                                        children: \"Your Queue Positions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: queueStatus.queue_positions.map((position)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center p-3 bg-gray-800 border border-gray-700 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-white\",\n                                                                children: [\n                                                                    \"Job #\",\n                                                                    position.id\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: [\n                                                                    \"Priority: \",\n                                                                    position.effective_priority\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    position.status\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-blue-400\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    position.queue_position\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"in queue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, position.id, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 398,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 278,\n                    columnNumber: 11\n                }, this),\n                (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features1 = queueStatus.license_features) === null || _queueStatus_license_features1 === void 0 ? void 0 : _queueStatus_license_features1.max_accounts_per_batch) === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDC65\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 428,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white mb-2\",\n                                children: \"Multi-user Access Required\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 429,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-2\",\n                                children: \"Your current license doesn't include batch processing access.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 430,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: \"You can only process single homework assignments.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 431,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Please upgrade your license to create batches with multiple accounts.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 427,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 426,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"batches\" && (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features2 = queueStatus.license_features) === null || _queueStatus_license_features2 === void 0 ? void 0 : _queueStatus_license_features2.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white\",\n                                children: \"Your Batches\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 440,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 439,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full divide-y divide-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-800\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Batch Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Priority\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\",\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-gray-900 divide-y divide-gray-700\",\n                                        children: batches.map((batch)=>{\n                                            const progressPercentage = batch.total_accounts > 0 ? Math.round(batch.processed_accounts / batch.total_accounts * 100) : 0;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-800 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-white\",\n                                                                    children: batch.batch_name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between text-xs text-gray-400 mb-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Progress\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                    lineNumber: 476,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        progressPercentage,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                    lineNumber: 477,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-500 ease-out\",\n                                                                                style: {\n                                                                                    width: \"\".concat(progressPercentage, \"%\")\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                                lineNumber: 480,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(batch.status === \"completed\" ? \"bg-green-900 text-green-300 border border-green-700\" : batch.status === \"processing\" ? \"bg-blue-900 text-blue-300 border border-blue-700\" : batch.status === \"failed\" ? \"bg-red-900 text-red-300 border border-red-700\" : \"bg-yellow-900 text-yellow-300 border border-yellow-700\"),\n                                                                children: [\n                                                                    batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    batch.status\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: [\n                                                                                batch.processed_accounts,\n                                                                                \"/\",\n                                                                                batch.total_accounts\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        batch.status === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 text-blue-400 text-xs\",\n                                                                            children: \"processing\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                            lineNumber: 509,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.failed_accounts > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-red-400 text-xs mt-1\",\n                                                                    children: [\n                                                                        batch.failed_accounts,\n                                                                        \" failed\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 rounded-full mr-2 \".concat(batch.priority_level >= 8 ? \"bg-red-400\" : batch.priority_level >= 5 ? \"bg-yellow-400\" : \"bg-green-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                batch.priority_level\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: new Date(batch.created_at).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs\",\n                                                                    children: new Date(batch.created_at).toLocaleTimeString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, batch.id, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 443,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 442,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 438,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"schedule\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: !(queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features3 = queueStatus.license_features) === null || _queueStatus_license_features3 === void 0 ? void 0 : _queueStatus_license_features3.scheduling_access) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCC5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-white mb-2\",\n                                    children: \"Scheduling Not Available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Your current license doesn't include scheduling access.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Please upgrade your license to use this feature.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 548,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 547,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ScheduleCalendar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        schedules: schedules,\n                        onScheduleSelect: (schedule)=>{\n                            console.log(\"Selected schedule:\", schedule);\n                        // Handle schedule selection (e.g., show details modal)\n                        },\n                        onCreateSchedule: async (scheduleData)=>{\n                            try {\n                                const token = localStorage.getItem(\"token\");\n                                const response = await fetch(\"/api/queue/schedule\", {\n                                    method: \"POST\",\n                                    headers: {\n                                        \"Authorization\": \"Bearer \".concat(token),\n                                        \"Content-Type\": \"application/json\"\n                                    },\n                                    body: JSON.stringify(scheduleData)\n                                });\n                                if (response.ok) {\n                                    const result = await response.json();\n                                    alert(\"Schedule created successfully!\");\n                                    loadQueueData(); // Refresh data\n                                } else {\n                                    const error = await response.json();\n                                    alert(\"Error: \".concat(error.error));\n                                }\n                            } catch (err) {\n                                alert(\"Error: \".concat(err.message));\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                        lineNumber: 556,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 545,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"create\" && (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features4 = queueStatus.license_features) === null || _queueStatus_license_features4 === void 0 ? void 0 : _queueStatus_license_features4.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-700 rounded-lg shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-white\",\n                                children: \"Create New Batch\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                lineNumber: 594,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 593,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleBatchSubmit,\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300\",\n                                            children: \"Batch Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: batchForm.batch_name,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        batch_name: e.target.value\n                                                    })),\n                                            className: \"mt-1 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                            children: \"Login Type\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: batchForm.login_type,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        login_type: e.target.value\n                                                    })),\n                                            className: \"mb-4 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select Login Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"normal\",\n                                                    children: \"\\uD83D\\uDC64 Normal Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"google\",\n                                                    children: \"\\uD83D\\uDD0D Google Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"microsoft\",\n                                                    children: \"\\uD83C\\uDFE2 Microsoft Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                            children: \"Accounts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 17\n                                        }, this),\n                                        batchForm.accounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-600 bg-gray-800 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"School\",\n                                                        value: account.school,\n                                                        onChange: (e)=>updateAccount(index, \"school\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        placeholder: \"Email\",\n                                                        value: account.email,\n                                                        onChange: (e)=>updateAccount(index, \"email\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        placeholder: \"Password\",\n                                                        value: account.password,\n                                                        onChange: (e)=>updateAccount(index, \"password\", e.target.value),\n                                                        className: \"bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeAccount(index),\n                                                        className: \"px-3 py-2 text-red-400 border border-red-600 rounded-md hover:bg-red-900 transition-colors\",\n                                                        disabled: batchForm.accounts.length === 1,\n                                                        children: \"Remove\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 19\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addAccount,\n                                            className: \"px-4 py-2 text-blue-400 border border-blue-600 rounded-md hover:bg-blue-900 transition-colors\",\n                                            disabled: (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features5 = queueStatus.license_features) === null || _queueStatus_license_features5 === void 0 ? void 0 : _queueStatus_license_features5.max_accounts_per_batch) > 0 && batchForm.accounts.length >= queueStatus.license_features.max_accounts_per_batch,\n                                            children: \"Add Account\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features6 = queueStatus.license_features) === null || _queueStatus_license_features6 === void 0 ? void 0 : _queueStatus_license_features6.max_accounts_per_batch) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-1\",\n                                            children: [\n                                                \"Maximum \",\n                                                queueStatus.license_features.max_accounts_per_batch,\n                                                \" accounts per batch\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 15\n                                }, this),\n                                (queueStatus === null || queueStatus === void 0 ? void 0 : (_queueStatus_license_features7 = queueStatus.license_features) === null || _queueStatus_license_features7 === void 0 ? void 0 : _queueStatus_license_features7.scheduling_access) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-300\",\n                                            children: \"Schedule Time (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"datetime-local\",\n                                            value: batchForm.scheduled_time,\n                                            onChange: (e)=>setBatchForm((prev)=>({\n                                                        ...prev,\n                                                        scheduled_time: e.target.value\n                                                    })),\n                                            className: \"mt-1 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setBatchForm({\n                                                    batch_name: \"\",\n                                                    login_type: \"\",\n                                                    accounts: [\n                                                        {\n                                                            school: \"\",\n                                                            email: \"\",\n                                                            password: \"\"\n                                                        }\n                                                    ],\n                                                    scheduled_time: \"\",\n                                                    priority_override: \"\"\n                                                }),\n                                            className: \"px-4 py-2 text-gray-300 border border-gray-600 rounded-md hover:bg-gray-800 transition-colors\",\n                                            children: \"Reset\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\",\n                                            children: \"Create Batch\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                            lineNumber: 596,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n                    lineNumber: 592,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\queue\\\\page.jsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n_s(QueueDashboard, \"JugoJYrb/5NgGCTNa5EUIbUkvWU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = QueueDashboard;\nvar _c;\n$RefreshReg$(_c, \"QueueDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/queue/page.jsx\n"));

/***/ })

});