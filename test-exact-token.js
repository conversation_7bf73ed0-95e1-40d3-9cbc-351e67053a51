const { getAuthManager } = require('./lib/auth');
const { getDatabase } = require('./lib/database');

async function testExactToken() {
  console.log('🔍 Testing Exact Token from API Logs...\n');
  
  try {
    const auth = getAuthManager();
    const db = getDatabase();
    
    // The exact token from the latest API request logs
    const exactToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEwLCJ1c2VybmFtZSI6InF1ZXVldGVzdF8xNzUxNzY3MTQxODMwIiwicm9sZSI6InVzZXIiLCJpYXQiOjE3NTE3NjgxNTYsImV4cCI6MTc1MjM3Mjk1Nn0.BwuBqTlN3AoW3HfOP_8VYBOIB6uokcJETfQgZEbZclg';
    
    console.log('Testing exact token from API logs:');
    console.log(`Token: ${exactToken.substring(0, 50)}...`);
    
    // Decode the JWT to see what's in it
    const parts = exactToken.split('.');
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    console.log('JWT payload:', payload);
    
    // Calculate the hash
    const tokenHash = auth.hashToken(exactToken);
    console.log(`Token hash: ${tokenHash.substring(0, 20)}...`);
    
    // Check if this hash exists in the database
    const session = db.db.prepare(`
      SELECT s.*, u.username 
      FROM user_sessions s 
      JOIN users u ON s.user_id = u.id 
      WHERE s.token_hash = ?
    `).get(tokenHash);
    
    if (session) {
      console.log('✅ Session found in database:');
      console.log(`   User: ${session.username} (ID: ${session.user_id})`);
      console.log(`   Session ID: ${session.id}`);
      console.log(`   Is Active: ${session.is_active}`);
      console.log(`   User Active: ${session.user_active}`);
      console.log(`   Expires: ${session.expires_at}`);
      console.log(`   Current time: ${new Date().toISOString()}`);
      
      // Check if expired
      const isExpired = new Date(session.expires_at) < new Date();
      console.log(`   Is Expired: ${isExpired}`);
      
    } else {
      console.log('❌ Session not found in database');
      
      // Show all sessions for this user
      console.log('\nAll sessions for user ID 10:');
      const allSessions = db.db.prepare(`
        SELECT s.*, u.username 
        FROM user_sessions s 
        JOIN users u ON s.user_id = u.id 
        WHERE s.user_id = 10
        ORDER BY s.created_at DESC 
        LIMIT 10
      `).all();
      
      allSessions.forEach((s, i) => {
        console.log(`${i + 1}. Hash: ${s.token_hash.substring(0, 20)}... (Active: ${s.is_active}, Created: ${s.created_at})`);
      });
    }
    
    // Test the database validation directly
    console.log('\n🧪 Testing database validation directly:');
    const dbValidation = db.validateSession(tokenHash);
    if (dbValidation) {
      console.log('✅ Database validation successful:', {
        user_id: dbValidation.user_id,
        username: dbValidation.username,
        user_active: dbValidation.user_active,
        is_active: dbValidation.is_active
      });
    } else {
      console.log('❌ Database validation failed');
    }
    
    // Test the full auth validation
    console.log('\n🔍 Testing full auth validation:');
    try {
      const authResult = auth.validateSession(exactToken);
      console.log('✅ Auth validation successful:', authResult);
    } catch (error) {
      console.log('❌ Auth validation failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testExactToken();
}

module.exports = { testExactToken };
