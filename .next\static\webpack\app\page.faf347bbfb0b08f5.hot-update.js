"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.jsx":
/*!**********************!*\
  !*** ./app/page.jsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SparxReaderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SparxReaderPage() {\n    _s();\n    // Authentication state\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [authLoading, setAuthLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bookTitle, setBookTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [needsPlaywright, setNeedsPlaywright] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [screenshot, setScreenshot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [storyContent, setStoryContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showBookConfirmation, setShowBookConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSrp, setCurrentSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [targetSrp, setTargetSrp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showInitialSrpInput, setShowInitialSrpInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // New state for enhanced UI\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentAnswer, setCurrentAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [questionNumber, setQuestionNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [srpEarned, setSrpEarned] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutomationRunning, setIsAutomationRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionHistory, setQuestionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [animationKey, setAnimationKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [automationComplete, setAutomationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [queuePosition, setQueuePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobId, setJobId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isInQueue, setIsInQueue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginMethod, setLoginMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\"); // 'normal' or 'microsoft'\n    // Credential system states\n    const [showCredentialInput, setShowCredentialInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [credentialMode, setCredentialMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"enter\"); // 'enter' or 'key'\n    const [userSchool, setUserSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userEmail, setUserEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [userPassword, setUserPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loginKey, setLoginKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [savedCredentials, setSavedCredentials] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // License renewal states\n    const [showLicenseRenewal, setShowLicenseRenewal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [licenseStatus, setLicenseStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newLicenseKey, setNewLicenseKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [licenseRenewalLoading, setLicenseRenewalLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check authentication on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuthentication();\n    }, []);\n    // Simulate question-solving process AFTER automation completes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated || !isAutomationRunning || !automationComplete) return;\n        if (isAutomationRunning && automationComplete) {\n            const interval = setInterval(()=>{\n                // Simulate question solving progress\n                setQuestionNumber((prev)=>{\n                    const newNum = prev + 1;\n                    setSrpEarned((prevSrp)=>prevSrp + Math.floor(Math.random() * 3) + 2);\n                    setAnimationKey((prevKey)=>prevKey + 1);\n                    // Simulate new question\n                    const sampleQuestions = [\n                        \"What was the main character's motivation in chapter 3?\",\n                        \"How did the setting influence the story's outcome?\",\n                        \"What literary device was used in the opening paragraph?\",\n                        \"Why did the protagonist make that crucial decision?\",\n                        \"What theme is most prominent throughout the narrative?\",\n                        \"How does the author develop the central conflict?\",\n                        \"What role does symbolism play in the narrative?\",\n                        \"are these questions fake and is youru\"\n                    ];\n                    const sampleAnswers = [\n                        \"To find their lost family member\",\n                        \"The harsh winter created urgency\",\n                        \"Metaphor and symbolism\",\n                        \"To protect their friends\",\n                        \"The importance of friendship\",\n                        \"Through escalating tension\",\n                        \"It reinforces the main themes\",\n                        \"They grow through adversity\"\n                    ];\n                    const randomIndex = Math.floor(Math.random() * sampleQuestions.length);\n                    setCurrentQuestion(sampleQuestions[randomIndex]);\n                    setCurrentAnswer(sampleAnswers[randomIndex]);\n                    // Add to history\n                    setQuestionHistory((prev)=>[\n                            ...prev,\n                            {\n                                number: newNum,\n                                question: sampleQuestions[randomIndex],\n                                answer: sampleAnswers[randomIndex]\n                            }\n                        ]);\n                    // Stop after reaching target or max questions\n                    if (newNum >= 8 || srpEarned >= parseInt(targetSrp || 50)) {\n                        setTimeout(()=>{\n                            setIsAutomationRunning(false);\n                            setMessage(\"Target SRP reached! Automation completed successfully.\");\n                        }, 1500); // Show the last question for a bit\n                        clearInterval(interval);\n                        return newNum;\n                    }\n                    return newNum;\n                });\n            }, 2500); // Show new question every 2.5 seconds\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isAuthenticated,\n        isAutomationRunning,\n        automationComplete,\n        srpEarned,\n        targetSrp\n    ]);\n    // Authentication functions\n    const checkAuthentication = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const response = await fetch(\"/api/auth/validate\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success && data.valid) {\n                setIsAuthenticated(true);\n                setUser(data.user);\n                setLicenseStatus(data.licenseStatus);\n            } else {\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"user\");\n                router.push(\"/login\");\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        } finally{\n            setAuthLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token)\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            router.push(\"/login\");\n        }\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    // Handle login method selection (doesn't start the process)\n    const handleLoginMethodSelect = (method)=>{\n        setLoginMethod(method);\n    };\n    // Check license validity before starting bot\n    const checkLicenseValidity = ()=>{\n        // Admin users don't need license validation\n        if (user && user.role === \"admin\") {\n            return {\n                valid: true\n            };\n        }\n        if (!licenseStatus) {\n            return {\n                valid: false,\n                error: \"License information not available\"\n            };\n        }\n        if (licenseStatus.license_status !== \"valid\") {\n            let errorMessage = \"Your license is not valid\";\n            switch(licenseStatus.license_status){\n                case \"expired\":\n                    errorMessage = \"Your license has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"Your license has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"Your license is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage,\n                status: licenseStatus.license_status\n            };\n        }\n        return {\n            valid: true\n        };\n    };\n    // Handle license renewal\n    const handleLicenseRenewal = async ()=>{\n        if (!newLicenseKey.trim()) {\n            setMessage(\"Please enter a valid license key\");\n            return;\n        }\n        setLicenseRenewalLoading(true);\n        setMessage(\"Renewing license...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/auth/renew-license\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    newLicenseKey: newLicenseKey.trim()\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setMessage(\"License renewed successfully! You can now start the bot.\");\n                setShowLicenseRenewal(false);\n                setNewLicenseKey(\"\");\n                // Refresh authentication to get updated license status\n                await checkAuthentication();\n            } else {\n                setMessage(data.error || \"Failed to renew license\");\n            }\n        } catch (error) {\n            setMessage(\"Error occurred while renewing license\");\n        } finally{\n            setLicenseRenewalLoading(false);\n        }\n    };\n    // Handle the actual start process\n    const handleBeginClick = async ()=>{\n        // Check authentication before starting\n        if (!isAuthenticated || !user) {\n            setMessage(\"Please login to use this feature\");\n            router.push(\"/login\");\n            return;\n        }\n        // Check license validity before proceeding\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            return;\n        }\n        // Reset all states\n        setMessage(\"\");\n        setBookTitle(\"\");\n        setNeedsPlaywright(false);\n        setScreenshot(\"\");\n        setStoryContent(\"\");\n        setShowBookConfirmation(false);\n        setCurrentSrp(\"\");\n        setTargetSrp(\"\");\n        setShowInitialSrpInput(false);\n        setAutomationComplete(false);\n        // Reset credential states\n        setUserSchool(\"\");\n        setUserEmail(\"\");\n        setUserPassword(\"\");\n        setLoginKey(\"\");\n        // Mark that user has started the process\n        setHasStarted(true);\n        // Show credential input first\n        setShowCredentialInput(true);\n        // Load user's saved credentials\n        await loadSavedCredentials();\n    };\n    // Load user's saved credentials\n    const loadSavedCredentials = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/credentials/list\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSavedCredentials(data.credentials);\n            }\n        } catch (error) {\n            console.error(\"Failed to load saved credentials:\", error);\n        }\n    };\n    // Handle credential submission\n    const handleCredentialSubmit = async ()=>{\n        if (credentialMode === \"key\") {\n            // Use saved credentials with login key\n            if (!loginKey) {\n                setMessage(\"Please enter your login key\");\n                return;\n            }\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    // Proceed with automation using saved credentials\n                    setShowCredentialInput(false);\n                    setMessage(\"Please enter how much SRP you need to earn:\");\n                    setShowInitialSrpInput(true);\n                } else {\n                    setMessage(data.error || \"Invalid login key\");\n                }\n            } catch (error) {\n                setMessage(\"Error retrieving credentials\");\n            }\n        } else {\n            // Use entered credentials\n            if (!userSchool || !userEmail || !userPassword) {\n                setMessage(\"Please enter school, email and password\");\n                return;\n            }\n            // Ask if user wants to save credentials\n            if (confirm(\"Would you like to save these credentials for future use? You will receive a secure login key.\")) {\n                try {\n                    const token = localStorage.getItem(\"token\");\n                    const response = await fetch(\"/api/credentials/save\", {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            \"Authorization\": \"Bearer \".concat(token)\n                        },\n                        body: JSON.stringify({\n                            loginMethod,\n                            school: userSchool,\n                            email: userEmail,\n                            password: userPassword\n                        })\n                    });\n                    const data = await response.json();\n                    if (data.success) {\n                        alert(\"Credentials saved! Your login key is: \".concat(data.loginKey, \"\\n\\nPlease save this key securely. You can use it for future logins.\"));\n                    }\n                } catch (error) {\n                    console.error(\"Failed to save credentials:\", error);\n                }\n            }\n            // Proceed with automation\n            setShowCredentialInput(false);\n            setMessage(\"Please enter how much SRP you need to earn:\");\n            setShowInitialSrpInput(true);\n        }\n    };\n    const handleSrpSubmit = async ()=>{\n        // Validate SRP input\n        if (!targetSrp || isNaN(targetSrp) || parseInt(targetSrp) <= 0) {\n            setMessage(\"Please enter a valid SRP target (positive number)\");\n            return;\n        }\n        // Check license validity again before starting automation\n        const licenseCheck = checkLicenseValidity();\n        if (!licenseCheck.valid) {\n            setMessage(licenseCheck.error);\n            setShowLicenseRenewal(true);\n            setShowInitialSrpInput(false);\n            return;\n        }\n        setLoading(true);\n        setShowInitialSrpInput(false);\n        const isNormalLogin = loginMethod === \"normal\";\n        const isMicrosoftLogin = loginMethod === \"microsoft\";\n        const isGoogleLogin = loginMethod === \"google\";\n        // Get credentials\n        let credentials = null;\n        if (credentialMode === \"key\" && loginKey) {\n            // Get credentials from login key\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/credentials/get\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        loginKey\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    credentials = data.credentials;\n                } else {\n                    setLoading(false);\n                    setMessage(data.error || \"Failed to retrieve credentials\");\n                    return;\n                }\n            } catch (error) {\n                setLoading(false);\n                setMessage(\"Error retrieving credentials\");\n                return;\n            }\n        } else if (credentialMode === \"enter\" && userSchool && userEmail && userPassword) {\n            // Use entered credentials\n            credentials = {\n                school: userSchool,\n                email: userEmail,\n                password: userPassword,\n                loginMethod: loginMethod\n            };\n        } else {\n            setLoading(false);\n            setMessage(\"No credentials available\");\n            return;\n        }\n        if (isNormalLogin) {\n            setMessage(\"Preparing to start...\");\n        } else if (isMicrosoftLogin) {\n            setMessage(\"Starting Microsoft login automation...\");\n        } else if (isGoogleLogin) {\n            setMessage(\"Starting Google login automation...\");\n        }\n        try {\n            let apiEndpoint, requestBody;\n            if (isNormalLogin) {\n                apiEndpoint = \"/api/sparxreader/start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isMicrosoftLogin) {\n                apiEndpoint = \"/api/sparxreader/microsoft-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            } else if (isGoogleLogin) {\n                apiEndpoint = \"/api/sparxreader/google-start\";\n                requestBody = {\n                    url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                    targetSrp: parseInt(targetSrp),\n                    credentials: credentials\n                };\n            }\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(apiEndpoint, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Book title and SRP extracted, show confirmation dialog\n                setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                setBookTitle(data.bookTitle);\n                setCurrentSrp(data.currentSrp);\n                setShowBookConfirmation(true);\n                if (data.screenshot) {\n                    setScreenshot(data.screenshot);\n                }\n            } else {\n                setMessage(data.error || \"Failed to start\");\n                if (data.needsPlaywright) {\n                    setNeedsPlaywright(true);\n                }\n            }\n        } catch (error) {\n            setMessage(\"Error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleYesClick = async ()=>{\n        setLoading(true);\n        setShowBookConfirmation(false);\n        setIsInQueue(true);\n        setMessage(\"Adding to queue...\");\n        try {\n            const token = localStorage.getItem(\"token\");\n            // Create a queue job instead of direct automation\n            const response = await fetch(\"/api/queue/jobs\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    job_type: \"sparx_reader\",\n                    job_data: {\n                        school: selectedSchool,\n                        email: email,\n                        username: username,\n                        password: password,\n                        login_type: loginMethod,\n                        bookTitle: bookTitle\n                    },\n                    srp_target: targetSrp ? parseInt(targetSrp) : 100,\n                    priority: 0\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setJobId(data.job_id);\n                setMessage(\"Added to queue! Checking position...\");\n                // Start monitoring queue position\n                startQueueMonitoring(data.job_id);\n            } else {\n                setIsInQueue(false);\n                setMessage(data.error || \"Failed to add job to queue\");\n            }\n        } catch (error) {\n            setIsInQueue(false);\n            setMessage(\"Error occurred while adding to queue\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNoClick = async ()=>{\n        setLoading(true);\n        setMessage(\"Finding a different book with same SRP target...\");\n        try {\n            // Close current session\n            const token = localStorage.getItem(\"token\");\n            await fetch(\"/api/sparxreader/close\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            // Restart automation with existing target SRP\n            setLoading(true);\n            setShowInitialSrpInput(false);\n            setMessage(\"Preparing to start with new book...\");\n            try {\n                const token = localStorage.getItem(\"token\");\n                const response = await fetch(\"/api/sparxreader/start\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        url: \"https://selectschool.sparx-learning.com/?app=sparx_learning&forget=1\",\n                        targetSrp: parseInt(targetSrp)\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    setMessage(\"Book found - please confirm (Target SRP: \".concat(targetSrp, \")\"));\n                    setBookTitle(data.bookTitle);\n                    setCurrentSrp(data.currentSrp);\n                    setShowBookConfirmation(true);\n                    if (data.screenshot) {\n                        setScreenshot(data.screenshot);\n                    }\n                } else {\n                    setMessage(data.error || \"Failed to start\");\n                    if (data.needsPlaywright) {\n                        setNeedsPlaywright(true);\n                    }\n                }\n            } catch (error) {\n                setMessage(\"Error occurred while restarting\");\n            }\n        } catch (error) {\n            setMessage(\"Error closing previous session\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleBackClick = ()=>{\n        setIsAutomationRunning(false);\n        setIsProcessing(false);\n        setCurrentQuestion(\"\");\n        setCurrentAnswer(\"\");\n        setQuestionNumber(0);\n        setSrpEarned(0);\n        setQuestionHistory([]);\n        setMessage(\"\");\n        setShowBookConfirmation(false);\n        setShowInitialSrpInput(false);\n        setHasStarted(false);\n        setAutomationComplete(false);\n    };\n    // Show loading screen while checking authentication\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 674,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 676,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Checking authentication...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 677,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                    lineNumber: 675,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n            lineNumber: 673,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render main content if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 691,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"p-6 border-b border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto relative\",\n                            children: [\n                                (isAutomationRunning || isProcessing) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackClick,\n                                    className: \"absolute left-0 top-1/2 transform -translate-y-1/2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-all duration-200 flex items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"<\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Back\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 top-1/2 transform -translate-y-1/2 flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        user === null || user === void 0 ? void 0 : user.username\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-xs\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.role) === \"admin\" ? \"Administrator\" : \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                (user === null || user === void 0 ? void 0 : user.role) === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/admin\"),\n                                                    className: \"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Admin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(\"/queue\"),\n                                                    className: \"px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Queue\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleLogout,\n                                                    className: \"px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white text-sm rounded transition-colors\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-light text-white text-center\",\n                                    children: \"Sparx Reader\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-center text-gray-400 mt-1 text-sm\",\n                                    children: \"Automated Question Solving\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 696,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 695,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 flex items-center justify-center p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-4xl mx-auto\",\n                            children: [\n                                isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-8 text-center shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-20 h-20 mx-auto mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 border-4 border-blue-500/30 rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 bg-blue-500 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400 rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.5s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-4\",\n                                                children: \"\\uD83D\\uDE80 Processing Automation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-6 text-lg\",\n                                                children: \"AI is solving questions in the background...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full max-w-md mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 border-4 border-blue-500 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"Processing your request...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 15\n                                }, this),\n                                isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                    children: \"\\uD83D\\uDCCA Live Progress Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-24 h-24 mx-auto mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-24 h-24 transform -rotate-90\",\n                                                                        viewBox: \"0 0 100 100\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                className: \"text-gray-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 798,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                cx: \"50\",\n                                                                                cy: \"50\",\n                                                                                r: \"40\",\n                                                                                stroke: \"url(#srpGradient)\",\n                                                                                strokeWidth: \"4\",\n                                                                                fill: \"transparent\",\n                                                                                strokeDasharray: \"\".concat(srpEarned / parseInt(targetSrp || 1) * 251.2, \" 251.2\"),\n                                                                                className: \"transition-all duration-1000 ease-out drop-shadow-lg\",\n                                                                                strokeLinecap: \"round\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 799,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                                                    id: \"srpGradient\",\n                                                                                    x1: \"0%\",\n                                                                                    y1: \"0%\",\n                                                                                    x2: \"100%\",\n                                                                                    y2: \"100%\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"0%\",\n                                                                                            stopColor: \"#10B981\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 810,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"50%\",\n                                                                                            stopColor: \"#3B82F6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 811,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                                            offset: \"100%\",\n                                                                                            stopColor: \"#8B5CF6\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                            lineNumber: 812,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 809,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 808,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xl font-bold text-white block\",\n                                                                                    children: srpEarned\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 818,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-400\",\n                                                                                    children: \"SRP\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 817,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 rounded-full bg-gradient-to-r from-green-500/20 to-blue-500/20 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 823,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium mb-1\",\n                                                                children: \"SRP Earned\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-400 text-xs\",\n                                                                children: [\n                                                                    \"Target: \",\n                                                                    targetSrp\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-xs text-gray-400\",\n                                                                children: [\n                                                                    Math.round(srpEarned / parseInt(targetSrp || 1) * 100),\n                                                                    \"% Complete\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold text-blue-400 mb-1 animate-pulse\",\n                                                                        children: questionNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"Questions Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 836,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-700 rounded-full h-3 overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-blue-500 to-cyan-400 h-3 rounded-full transition-all duration-1000 ease-out relative\",\n                                                                            style: {\n                                                                                width: \"\".concat(Math.min(questionNumber / 10 * 100, 100), \"%\")\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-white/30 animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 842,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 838,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 837,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: [\n                                                                            Math.min(Math.round(questionNumber / 10 * 100), 100),\n                                                                            \"% of estimated session\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83E\\uDDE0 AI Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mx-auto mb-3 flex items-center justify-center animate-pulse\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mb-3\",\n                                                                        children: \"System Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-700 rounded-lg p-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-green-400 mb-2\",\n                                                                                children: \"\\uD83D\\uDFE2 Active & Processing\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 861,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400 mb-1\",\n                                                                                children: \"Performance Metrics\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 862,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-white\",\n                                                                                children: [\n                                                                                    \"⚡ \",\n                                                                                    questionNumber > 0 ? Math.round(elapsedTime / questionNumber) : 0,\n                                                                                    \"s per question\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 863,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-400\",\n                                                                                children: [\n                                                                                    \"\\uD83C\\uDFAF \",\n                                                                                    questionNumber > 0 ? Math.round(questionNumber / elapsedTime * 60) : 0,\n                                                                                    \" questions/min\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 866,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-purple-400 mt-1\",\n                                                                                children: [\n                                                                                    \"⏱️ \",\n                                                                                    formatTime(elapsedTime),\n                                                                                    \" elapsed\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 869,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 854,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm font-medium\",\n                                                                children: \"\\uD83D\\uDE80 AI Engine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 15\n                                }, this),\n                                currentQuestion && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 animate-slide-up\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4 animate-pulse\",\n                                                        children: questionNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                                children: \"✅ Question Solved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"AI successfully processed this question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-auto flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400 font-medium\",\n                                                                children: \"COMPLETED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 897,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 885,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/60 border border-gray-600 rounded-xl p-6 mb-6 backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"Q\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-base leading-relaxed mb-3\",\n                                                                    children: currentQuestion\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 bg-green-600 text-white text-xs rounded font-medium\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                Math.floor(Math.random() * 3) + 1,\n                                                                                \" SRP\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 909,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"• Solved automatically\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 912,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 908,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentAnswer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-xl p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-sm font-bold\",\n                                                                children: \"AI\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-400 font-medium mb-2\",\n                                                                    children: \"\\uD83E\\uDD16 AI Solution:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-200 leading-relaxed\",\n                                                                    children: currentAnswer\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 920,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 17\n                                    }, this)\n                                }, animationKey, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 15\n                                }, this),\n                                !hasStarted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border border-gray-700 rounded-xl p-10 max-w-lg mx-auto shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-16 h-16 mx-auto mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-bounce\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white text-2xl\",\n                                                                    children: \"\\uD83D\\uDE80\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 942,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-blue-400 rounded-full animate-ping\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -left-1 w-3 h-3 bg-purple-400 rounded-full animate-ping\",\n                                                                style: {\n                                                                    animationDelay: \"0.5s\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-3\",\n                                                        children: \"Start Automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-base\",\n                                                        children: \"\\uD83E\\uDD16 AI-powered question solving for Sparx Reader\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                        children: \"\\uD83D\\uDD10 Choose Login Method\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 955,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"normal\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"normal\" ? \"bg-gradient-to-r from-green-600 to-green-500 text-white ring-2 ring-green-400 shadow-lg shadow-green-500/25\" : \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-500 text-white shadow-lg hover:shadow-green-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDC64\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 968,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Normal Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 969,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"normal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 970,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"microsoft\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"microsoft\" ? \"bg-gradient-to-r from-blue-600 to-blue-500 text-white ring-2 ring-blue-400 shadow-lg shadow-blue-500/25\" : \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-500 text-white shadow-lg hover:shadow-blue-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83C\\uDFE2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Microsoft Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 983,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"microsoft\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-3 text-lg\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 57\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleLoginMethodSelect(\"google\"),\n                                                                disabled: loading,\n                                                                className: \"w-full py-4 px-6 font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transform hover:scale-105 \".concat(loginMethod === \"google\" ? \"bg-gradient-to-r from-red-600 to-red-500 text-white ring-2 ring-red-400 shadow-lg shadow-red-500/25\" : \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-500 text-white shadow-lg hover:shadow-red-500/25\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 text-lg\",\n                                                                        children: \"\\uD83D\\uDD0D\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1\",\n                                                                        children: \"Google Login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    loginMethod === \"google\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2\",\n                                                                        children: \"✓\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 998,\n                                                                        columnNumber: 54\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: [\n                                                                \"Selected: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: [\n                                                                        loginMethod === \"normal\" && \"Normal Login\",\n                                                                        loginMethod === \"microsoft\" && \"Microsoft Login\",\n                                                                        loginMethod === \"google\" && \"Google Login\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1005,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBeginClick,\n                                                        disabled: loading,\n                                                        className: \"w-full py-3 px-6 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Begin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 938,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 15\n                                }, this),\n                                showInitialSrpInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1031,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Set SRP Target\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"How much SRP do you want to earn?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1029,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: targetSrp,\n                                                        onChange: (e)=>setTargetSrp(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleSrpSubmit(),\n                                                        placeholder: \"Enter target (e.g., 50)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center\",\n                                                        min: \"1\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 text-center\",\n                                                        children: \"Automation will stop when target is reached\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSrpSubmit,\n                                                                disabled: loading || !targetSrp,\n                                                                className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                            lineNumber: 1060,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Starting...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1059,\n                                                                    columnNumber: 27\n                                                                }, this) : \"Start\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowInitialSrpInput(false),\n                                                                disabled: loading,\n                                                                className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1027,\n                                    columnNumber: 15\n                                }, this),\n                                showCredentialInput && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"\\uD83D\\uDD10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1084,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1083,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Login Credentials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1086,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            loginMethod === \"normal\" && \"Enter your Sparx Learning credentials\",\n                                                            loginMethod === \"microsoft\" && \"Enter your Microsoft account credentials\",\n                                                            loginMethod === \"google\" && \"Enter your Google account credentials\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1087,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex bg-gray-800 rounded-lg p-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"enter\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"enter\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Enter Credentials\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1097,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCredentialMode(\"key\"),\n                                                            className: \"flex-1 py-2 px-3 rounded text-sm font-medium transition-all \".concat(credentialMode === \"key\" ? \"bg-blue-500 text-white\" : \"text-gray-400 hover:text-white\"),\n                                                            children: \"Use Login Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 19\n                                            }, this),\n                                            credentialMode === \"enter\" ? /* Enter Credentials Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: userSchool,\n                                                        onChange: (e)=>setUserSchool(e.target.value),\n                                                        placeholder: \"School name (e.g., theangmeringschool)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: userEmail,\n                                                        onChange: (e)=>setUserEmail(e.target.value),\n                                                        placeholder: \"Email address\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1131,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        value: userPassword,\n                                                        onChange: (e)=>setUserPassword(e.target.value),\n                                                        placeholder: \"Password\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDCA1 Your credentials will be encrypted and stored securely. You'll receive a login key for future use.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1145,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1122,\n                                                columnNumber: 21\n                                            }, this) : /* Use Login Key Mode */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: loginKey,\n                                                        onChange: (e)=>setLoginKey(e.target.value),\n                                                        placeholder: \"Enter your login key (e.g., SLK-ABC12345)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500 text-center font-mono\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    savedCredentials.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mb-2\",\n                                                                children: \"Your saved login keys:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1163,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1 max-h-32 overflow-y-auto\",\n                                                                children: savedCredentials.map((cred, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setLoginKey(cred.loginKey),\n                                                                        className: \"w-full text-left px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-sm text-gray-300 hover:text-white transition-colors\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-mono text-blue-400\",\n                                                                                children: cred.loginKey\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1171,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-2 text-xs\",\n                                                                                children: [\n                                                                                    \"(\",\n                                                                                    cred.loginMethod,\n                                                                                    \")\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                                lineNumber: 1172,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1166,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1164,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1162,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"\\uD83D\\uDD11 Use your previously generated login key to access saved credentials.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1179,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1151,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleCredentialSubmit,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1193,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Processing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1192,\n                                                            columnNumber: 25\n                                                        }, this) : \"Continue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1186,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowCredentialInput(false);\n                                                            setHasStarted(false);\n                                                        },\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed text-sm\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1080,\n                                    columnNumber: 15\n                                }, this),\n                                showBookConfirmation && bookTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-lg w-full mx-4 animate-scale-in\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"B\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1219,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1218,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Book Found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1221,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Confirm to start automation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1222,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1217,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/30 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-white mb-4\",\n                                                        children: bookTitle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1226,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Current SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: currentSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1230,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"Target SRP\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1233,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl font-medium text-blue-400\",\n                                                                        children: targetSrp\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1234,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1232,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleYesClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1247,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Starting...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1246,\n                                                            columnNumber: 25\n                                                        }, this) : \"Yes, Start\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleNoClick,\n                                                        disabled: loading,\n                                                        className: \"flex-1 py-2.5 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                        children: loading ? \"Finding...\" : \"Find Different\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1252,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1239,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1216,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 15\n                                }, this),\n                                questionHistory.length > 0 && isAutomationRunning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"H\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Question History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-60 overflow-y-auto\",\n                                                children: questionHistory.slice(-5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-black/30 rounded p-3 border-l-4 border-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 font-medium text-sm\",\n                                                                        children: [\n                                                                            \"Q\",\n                                                                            item.number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1276,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-400 text-xs\",\n                                                                        children: \"Solved\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1277,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1275,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white text-sm mb-2\",\n                                                                children: [\n                                                                    item.question.substring(0, 100),\n                                                                    \"...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1279,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-xs\",\n                                                                children: [\n                                                                    \"Answer: \",\n                                                                    item.answer\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1280,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1274,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1272,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1267,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 15\n                                }, this),\n                                message && !showInitialSrpInput && !showBookConfirmation && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded p-4 inline-block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white\",\n                                            children: message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                            lineNumber: 1292,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1291,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1290,\n                                    columnNumber: 15\n                                }, this),\n                                needsPlaywright && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/70 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-red-500 rounded mx-auto mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg text-white\",\n                                                            children: \"!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1302,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-lg font-medium text-white mb-2\",\n                                                        children: \"Setup Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Playwright browsers need to be installed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1301,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-sm mb-2\",\n                                                        children: \"Run this command in your terminal:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"bg-black text-blue-400 p-2 rounded block text-sm\",\n                                                        children: \"npx playwright install chromium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1311,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-xs text-center\",\n                                                children: \"After installation, refresh this page and try again.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1316,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1300,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1299,\n                                    columnNumber: 15\n                                }, this),\n                                showLicenseRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-8 max-w-md w-full mx-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-500 text-2xl\",\n                                                            children: \"⚠️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1328,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-medium text-white mb-2\",\n                                                        children: \"License Renewal Required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: [\n                                                            licenseStatus && licenseStatus.license_status === \"expired\" && \"Your license has expired.\",\n                                                            licenseStatus && licenseStatus.license_status === \"maxed_out\" && \"Your license has reached maximum uses.\",\n                                                            licenseStatus && licenseStatus.license_status === \"inactive\" && \"Your license is inactive.\",\n                                                            (!licenseStatus || licenseStatus.license_status === \"valid\") && \"Your license is not valid.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1332,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mt-2\",\n                                                        children: \"Please enter a new license key to continue using the bot.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1327,\n                                                columnNumber: 19\n                                            }, this),\n                                            licenseStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/50 border border-gray-700 rounded p-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-white font-medium mb-2\",\n                                                        children: \"Current License Info:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1345,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Key: \",\n                                                                    licenseStatus.key_code || \"N/A\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1347,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Status: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium \".concat(licenseStatus.license_status === \"expired\" ? \"text-red-400\" : licenseStatus.license_status === \"maxed_out\" ? \"text-orange-400\" : licenseStatus.license_status === \"inactive\" ? \"text-gray-400\" : \"text-green-400\"),\n                                                                        children: [\n                                                                            licenseStatus.license_status === \"expired\" && \"Expired\",\n                                                                            licenseStatus.license_status === \"maxed_out\" && \"Max Uses Reached\",\n                                                                            licenseStatus.license_status === \"inactive\" && \"Inactive\",\n                                                                            licenseStatus.license_status === \"valid\" && \"Valid\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                        lineNumber: 1348,\n                                                                        columnNumber: 36\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            licenseStatus.expires_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Expires: \",\n                                                                    new Date(licenseStatus.expires_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1360,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            licenseStatus.max_uses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"Uses: \",\n                                                                    licenseStatus.current_uses || 0,\n                                                                    \"/\",\n                                                                    licenseStatus.max_uses\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1344,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-white font-medium mb-2\",\n                                                        children: \"New License Key\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newLicenseKey,\n                                                        onChange: (e)=>setNewLicenseKey(e.target.value),\n                                                        placeholder: \"Enter your new license key (e.g., SRX-XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXXXX)\",\n                                                        className: \"w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:border-blue-500 focus:outline-none\",\n                                                        disabled: licenseRenewalLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1371,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1369,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLicenseRenewal,\n                                                        disabled: licenseRenewalLoading || !newLicenseKey.trim(),\n                                                        className: \"flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded transition-colors duration-200\",\n                                                        children: licenseRenewalLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                                    lineNumber: 1389,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Renewing...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                            lineNumber: 1388,\n                                                            columnNumber: 25\n                                                        }, this) : \"Renew License\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowLicenseRenewal(false);\n                                                            setNewLicenseKey(\"\");\n                                                            setMessage(\"\");\n                                                        },\n                                                        disabled: licenseRenewalLoading,\n                                                        className: \"px-6 py-3 border border-gray-600 text-gray-400 hover:text-white hover:border-gray-500 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1396,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1381,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1326,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1325,\n                                    columnNumber: 15\n                                }, this),\n                                screenshot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 animate-fade-in\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3\",\n                                                        children: \"S\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                        lineNumber: 1417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Browser State\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1416,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded border border-gray-700 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: screenshot,\n                                                    alt: \"Browser screenshot\",\n                                                    className: \"w-full h-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                    lineNumber: 1421,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                                lineNumber: 1420,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                        lineNumber: 1415,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                                    lineNumber: 1414,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                            lineNumber: 747,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                        lineNumber: 746,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n                lineNumber: 693,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\page.jsx\",\n        lineNumber: 689,\n        columnNumber: 5\n    }, this);\n}\n_s(SparxReaderPage, \"3tPyRYBhGPqvkw99Z4exibXXewo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SparxReaderPage;\nvar _c;\n$RefreshReg$(_c, \"SparxReaderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.jsx\n"));

/***/ })

});