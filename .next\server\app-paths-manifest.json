{"/api/queue/status/route": "app/api/queue/status/route.js", "/queue/page": "app/queue/page.js", "/api/queue/batch/route": "app/api/queue/batch/route.js", "/api/queue/schedule/route": "app/api/queue/schedule/route.js", "/api/queue/priority-levels/route": "app/api/queue/priority-levels/route.js", "/page": "app/page.js", "/login/page": "app/login/page.js", "/api/credentials/save/route": "app/api/credentials/save/route.js", "/api/sparxreader/start/route": "app/api/sparxreader/start/route.js", "/api/sparxreader/navigate/route": "app/api/sparxreader/navigate/route.js"}