{"/page": "app/page.js", "/login/page": "app/login/page.js", "/admin/page": "app/admin/page.js", "/api/auth/validate/route": "app/api/auth/validate/route.js", "/api/credentials/list/route": "app/api/credentials/list/route.js", "/api/credentials/get/route": "app/api/credentials/get/route.js", "/api/sparxreader/start/route": "app/api/sparxreader/start/route.js", "/api/queue/jobs/route": "app/api/queue/jobs/route.js", "/api/queue/position/route": "app/api/queue/position/route.js"}