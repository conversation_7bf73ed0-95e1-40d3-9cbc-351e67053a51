"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/queue/schedule/route";
exports.ids = ["app/api/queue/schedule/route"];
exports.modules = {

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fschedule%2Froute&page=%2Fapi%2Fqueue%2Fschedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fschedule%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fschedule%2Froute&page=%2Fapi%2Fqueue%2Fschedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fschedule%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_queue_schedule_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/queue/schedule/route.js */ \"(rsc)/./app/api/queue/schedule/route.js\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/queue/schedule/route\",\n        pathname: \"/api/queue/schedule\",\n        filename: \"route\",\n        bundlePath: \"app/api/queue/schedule/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\queue\\\\schedule\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_queue_schedule_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/queue/schedule/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fschedule%2Froute&page=%2Fapi%2Fqueue%2Fschedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fschedule%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/queue/schedule/route.js":
/*!*****************************************!*\
  !*** ./app/api/queue/schedule/route.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/database */ \"(rsc)/./lib/database.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_lib_database__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_webhook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/webhook */ \"(rsc)/./lib/webhook.js\");\n/* harmony import */ var _lib_webhook__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lib_webhook__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/queueMiddleware */ \"(rsc)/./lib/queueMiddleware.js\");\n/* harmony import */ var _lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// Middleware wrapper for Next.js API routes\nfunction withMiddleware(handler, middlewares) {\n    return async (request, context)=>{\n        const req = {\n            ...request,\n            body: await request.json().catch(()=>({})),\n            user: null,\n            licenseFeatures: null,\n            degradedMode: false\n        };\n        const res = {\n            status: (code)=>({\n                    json: (data)=>next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n                            status: code\n                        })\n                }),\n            json: (data)=>next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data)\n        };\n        // Authenticate user first\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const authHeader = request.headers.get(\"authorization\");\n            if (!authHeader?.startsWith(\"Bearer \")) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            const token = authHeader.substring(7);\n            const session = db.validateSession(token);\n            if (!session) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Invalid or expired session\"\n                }, {\n                    status: 401\n                });\n            }\n            req.user = {\n                id: session.user_id,\n                username: session.username,\n                role: session.role\n            };\n        } catch (error) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Authentication failed\"\n            }, {\n                status: 401\n            });\n        }\n        // Apply middlewares\n        for (const middleware of middlewares){\n            try {\n                let nextCalled = false;\n                const next = ()=>{\n                    nextCalled = true;\n                };\n                const result = await middleware(req, res, next);\n                if (result instanceof next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n                    return result;\n                }\n                if (!nextCalled) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Middleware blocked request\"\n                    }, {\n                        status: 403\n                    });\n                }\n            } catch (error) {\n                console.error(\"Middleware error:\", error);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Request processing failed\"\n                }, {\n                    status: 500\n                });\n            }\n        }\n        return handler(req, context);\n    };\n}\n// GET - Get user's schedules\nasync function GET(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const url = new URL(request.url);\n            const startDate = url.searchParams.get(\"start_date\");\n            const endDate = url.searchParams.get(\"end_date\");\n            const view = url.searchParams.get(\"view\") || \"month\"; // month, week, day\n            // Calculate date range based on view if not provided\n            let calculatedStartDate = startDate;\n            let calculatedEndDate = endDate;\n            if (!startDate || !endDate) {\n                const now = new Date();\n                switch(view){\n                    case \"day\":\n                        calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();\n                        calculatedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1).toISOString();\n                        break;\n                    case \"week\":\n                        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));\n                        calculatedStartDate = startOfWeek.toISOString();\n                        calculatedEndDate = new Date(startOfWeek.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();\n                        break;\n                    case \"month\":\n                    default:\n                        calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();\n                        calculatedEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString();\n                        break;\n                }\n            }\n            const schedules = db.getUserSchedules(req.user.id, calculatedStartDate, calculatedEndDate);\n            // Format schedules for calendar view\n            const formattedSchedules = schedules.map((schedule)=>({\n                    id: schedule.id,\n                    title: schedule.batch_name || `${schedule.job_type} Job`,\n                    start: schedule.scheduled_time,\n                    end: new Date(new Date(schedule.scheduled_time).getTime() + schedule.duration_minutes * 60 * 1000).toISOString(),\n                    status: schedule.status,\n                    job_type: schedule.job_type,\n                    batch_id: schedule.batch_id,\n                    job_id: schedule.job_id,\n                    duration_minutes: schedule.duration_minutes\n                }));\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                schedules: formattedSchedules,\n                view_info: {\n                    view,\n                    start_date: calculatedStartDate,\n                    end_date: calculatedEndDate,\n                    total_schedules: schedules.length\n                },\n                license_info: {\n                    scheduling_access: req.licenseFeatures.scheduling_access,\n                    priority_level: req.licenseFeatures.priority_level\n                }\n            });\n        } catch (error) {\n            console.error(\"Get schedules error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to retrieve schedules\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().validateLicenseFeatures),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().logQueueActivity)\n    ]);\n    return handler(request);\n}\n// POST - Create scheduled job/batch\nasync function POST(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n            const { scheduled_time, duration_minutes = 30, job_type = \"sparx_reader\", job_data, batch_data, admin_override = false } = req.body;\n            // Validate required fields\n            if (!scheduled_time) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"scheduled_time is required\"\n                }, {\n                    status: 400\n                });\n            }\n            // Validate scheduled time is in the future\n            const scheduledDate = new Date(scheduled_time);\n            const now = new Date();\n            if (scheduledDate <= now && !admin_override) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Scheduled time must be in the future\"\n                }, {\n                    status: 400\n                });\n            }\n            // Handle degraded mode\n            if (req.degradedMode) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Scheduling not available\",\n                    reason: req.degradationReason,\n                    alternative: \"Please upgrade your license for scheduling access\"\n                }, {\n                    status: 403\n                });\n            }\n            let scheduleId, jobId, batchId;\n            if (batch_data) {\n                // Create scheduled batch\n                const { batch_name, accounts } = batch_data;\n                if (!batch_name || !accounts || !Array.isArray(accounts)) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Invalid batch data\",\n                        details: \"batch_name and accounts array are required for batch scheduling\"\n                    }, {\n                        status: 400\n                    });\n                }\n                // Create the batch with scheduled time\n                batchId = db.createQueueBatch(req.user.id, batch_name, accounts, scheduled_time);\n                // The batch creation already creates the schedule entry\n                const schedules = db.getUserSchedules(req.user.id, scheduled_time, scheduled_time);\n                scheduleId = schedules.find((s)=>s.batch_id === batchId)?.id;\n                await webhook.sendBatchCreated(req.user.username, batch_name, accounts.length, scheduled_time);\n            } else if (job_data) {\n                // Create scheduled individual job\n                if (!job_data.school || !job_data.email || !job_data.password) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Invalid job data\",\n                        details: \"school, email, and password are required for job scheduling\"\n                    }, {\n                        status: 400\n                    });\n                }\n                // Create individual job\n                const jobStmt = db.db.prepare(`\r\n          INSERT INTO queue_jobs (user_id, job_type, job_data, priority_level, effective_priority, scheduled_time)\r\n          VALUES (?, ?, ?, ?, ?, ?)\r\n        `);\n                const effectivePriority = db.calculateEffectivePriority(req.licenseFeatures.priority_level, scheduled_time);\n                const jobResult = jobStmt.run(req.user.id, job_type, JSON.stringify(job_data), req.licenseFeatures.priority_level, effectivePriority, scheduled_time);\n                jobId = jobResult.lastInsertRowid;\n                // Create schedule entry\n                scheduleId = db.createScheduleEntry(req.user.id, scheduled_time, jobId, null, duration_minutes);\n            } else {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Either batch_data or job_data is required\"\n                }, {\n                    status: 400\n                });\n            }\n            // Log activity\n            db.logActivity(req.user.id, \"SCHEDULE_CREATED\", `Scheduled ${batch_data ? \"batch\" : \"job\"} for ${scheduled_time}`);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                schedule: {\n                    id: scheduleId,\n                    scheduled_time,\n                    duration_minutes,\n                    job_id: jobId,\n                    batch_id: batchId,\n                    status: \"scheduled\"\n                },\n                message: `${batch_data ? \"Batch\" : \"Job\"} scheduled successfully`\n            });\n        } catch (error) {\n            console.error(\"Schedule creation error:\", error);\n            const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n            await webhook.sendQueueAlert(\"system_error\", \"Schedule creation failed\", {\n                user: req.user?.username || \"unknown\",\n                error: error.message,\n                scheduled_time: req.body?.scheduled_time || \"unknown\"\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Schedule creation failed\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().validateLicenseFeatures),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().checkScheduleConflicts),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().logQueueActivity)\n    ]);\n    return handler(request);\n}\n// PATCH - Update schedule (reschedule, cancel, etc.)\nasync function PATCH(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const webhook = (0,_lib_webhook__WEBPACK_IMPORTED_MODULE_2__.getWebhookManager)();\n            const { schedule_id, action, new_scheduled_time, duration_minutes } = req.body;\n            if (!schedule_id || !action) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"schedule_id and action are required\"\n                }, {\n                    status: 400\n                });\n            }\n            // Get schedule and verify ownership\n            const schedules = db.getUserSchedules(req.user.id);\n            const schedule = schedules.find((s)=>s.id === schedule_id);\n            if (!schedule && req.user.role !== \"admin\") {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Schedule not found or access denied\"\n                }, {\n                    status: 404\n                });\n            }\n            let result;\n            switch(action){\n                case \"cancel\":\n                    const cancelStmt = db.db.prepare(`\r\n            UPDATE queue_schedules \r\n            SET status = 'cancelled' \r\n            WHERE id = ?\r\n          `);\n                    result = cancelStmt.run(schedule_id);\n                    // Cancel associated jobs/batches\n                    if (schedule.job_id) {\n                        db.updateJobStatus(schedule.job_id, \"cancelled\");\n                    }\n                    if (schedule.batch_id) {\n                        db.updateBatchStatus(schedule.batch_id, \"cancelled\");\n                    }\n                    await webhook.sendQueueAlert(\"info\", \"Schedule cancelled\", {\n                        schedule_id,\n                        user: req.user.username,\n                        original_time: schedule.scheduled_time\n                    });\n                    break;\n                case \"reschedule\":\n                    if (!new_scheduled_time) {\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            error: \"new_scheduled_time is required for reschedule action\"\n                        }, {\n                            status: 400\n                        });\n                    }\n                    // Check for conflicts at new time\n                    const conflicts = db.checkScheduleConflicts(req.user.id, new_scheduled_time, duration_minutes || schedule.duration_minutes);\n                    if (conflicts.length > 0 && req.user.role !== \"admin\") {\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            error: \"Schedule conflict at new time\",\n                            conflicts: conflicts\n                        }, {\n                            status: 409\n                        });\n                    }\n                    // Update schedule\n                    const rescheduleStmt = db.db.prepare(`\r\n            UPDATE queue_schedules \r\n            SET scheduled_time = ?, duration_minutes = COALESCE(?, duration_minutes)\r\n            WHERE id = ?\r\n          `);\n                    result = rescheduleStmt.run(new_scheduled_time, duration_minutes, schedule_id);\n                    // Update associated jobs/batches\n                    if (schedule.job_id) {\n                        const updateJobStmt = db.db.prepare(`\r\n              UPDATE queue_jobs \r\n              SET scheduled_time = ?, effective_priority = ?\r\n              WHERE id = ?\r\n            `);\n                        const newEffectivePriority = db.calculateEffectivePriority(req.licenseFeatures.priority_level, new_scheduled_time);\n                        updateJobStmt.run(new_scheduled_time, newEffectivePriority, schedule.job_id);\n                    }\n                    if (schedule.batch_id) {\n                        const updateBatchStmt = db.db.prepare(`\r\n              UPDATE queue_batches \r\n              SET scheduled_time = ?\r\n              WHERE id = ?\r\n            `);\n                        updateBatchStmt.run(new_scheduled_time, schedule.batch_id);\n                    }\n                    await webhook.sendQueueAlert(\"info\", \"Schedule rescheduled\", {\n                        schedule_id,\n                        user: req.user.username,\n                        old_time: schedule.scheduled_time,\n                        new_time: new_scheduled_time\n                    });\n                    break;\n                default:\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Invalid action\",\n                        valid_actions: [\n                            \"cancel\",\n                            \"reschedule\"\n                        ]\n                    }, {\n                        status: 400\n                    });\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                action,\n                schedule_id,\n                message: `Schedule ${action} completed successfully`\n            });\n        } catch (error) {\n            console.error(\"Schedule update error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Schedule update failed\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().validateLicenseFeatures),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().logQueueActivity)\n    ]);\n    return handler(request);\n}\n// DELETE - Delete schedule\nasync function DELETE(request) {\n    const handler = withMiddleware(async (req)=>{\n        try {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            const url = new URL(request.url);\n            const scheduleId = url.searchParams.get(\"schedule_id\");\n            if (!scheduleId) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"schedule_id parameter is required\"\n                }, {\n                    status: 400\n                });\n            }\n            // Verify ownership or admin privileges\n            const schedules = db.getUserSchedules(req.user.id);\n            const schedule = schedules.find((s)=>s.id === parseInt(scheduleId));\n            if (!schedule && req.user.role !== \"admin\") {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Schedule not found or access denied\"\n                }, {\n                    status: 404\n                });\n            }\n            // Delete schedule\n            const deleteStmt = db.db.prepare(\"DELETE FROM queue_schedules WHERE id = ?\");\n            deleteStmt.run(scheduleId);\n            // Log activity\n            db.logActivity(req.user.id, \"SCHEDULE_DELETED\", `Deleted schedule ${scheduleId}`);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: \"Schedule deleted successfully\"\n            });\n        } catch (error) {\n            console.error(\"Schedule deletion error:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Schedule deletion failed\",\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n    }, [\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().validateLicenseFeatures),\n        (_lib_queueMiddleware__WEBPACK_IMPORTED_MODULE_3___default().logQueueActivity)\n    ]);\n    return handler(request);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/queue/schedule/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/database.js":
/*!*************************!*\
  !*** ./lib/database.js ***!
  \*************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\nconst bcrypt = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/umd/index.js\");\nconst { v4: uuidv4 } = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/cjs/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nclass DatabaseManager {\n    constructor(){\n        const dbPath = path.join(process.cwd(), \"data\", \"app.db\");\n        this.db = new Database(dbPath);\n        this.initializeTables();\n        this.createDefaultAdmin();\n    }\n    initializeTables() {\n        // Users table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        username TEXT UNIQUE NOT NULL,\n        password_hash TEXT NOT NULL,\n        role TEXT DEFAULT 'user' CHECK(role IN ('user', 'admin')),\n        license_key_id INTEGER,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        last_login DATETIME,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id)\n      )\n    `);\n        // License keys table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_keys (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        key_code TEXT UNIQUE NOT NULL,\n        duration_days INTEGER NOT NULL,\n        max_uses INTEGER NOT NULL DEFAULT 1,\n        current_uses INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        expires_at DATETIME NOT NULL,\n        is_active BOOLEAN DEFAULT 1,\n        created_by INTEGER,\n        features TEXT DEFAULT '[]',\n        FOREIGN KEY (created_by) REFERENCES users (id)\n      )\n    `);\n        // User sessions table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS user_sessions (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        token_hash TEXT NOT NULL,\n        expires_at DATETIME NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        ip_address TEXT,\n        user_agent TEXT,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Activity logs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER,\n        action TEXT NOT NULL,\n        details TEXT,\n        ip_address TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // System settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        setting_key TEXT UNIQUE NOT NULL,\n        setting_value TEXT,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_by INTEGER,\n        FOREIGN KEY (updated_by) REFERENCES users (id)\n      )\n    `);\n        // Encrypted login credentials table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS encrypted_credentials (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        login_key TEXT UNIQUE NOT NULL,\n        login_method TEXT NOT NULL CHECK(login_method IN ('normal', 'microsoft', 'google')),\n        encrypted_school TEXT NOT NULL,\n        encrypted_email TEXT NOT NULL,\n        encrypted_password TEXT NOT NULL,\n        encryption_iv TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // License feature settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_feature_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        license_key_id INTEGER NOT NULL,\n        max_accounts_per_batch INTEGER DEFAULT 0,\n        priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),\n        scheduling_access BOOLEAN DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id),\n        UNIQUE(license_key_id)\n      )\n    `);\n        // Queue batches table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_batches (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        batch_name TEXT,\n        total_accounts INTEGER NOT NULL,\n        processed_accounts INTEGER DEFAULT 0,\n        failed_accounts INTEGER DEFAULT 0,\n        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        scheduled_time DATETIME,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Queue jobs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_jobs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        batch_id INTEGER,\n        user_id INTEGER NOT NULL,\n        job_type TEXT NOT NULL DEFAULT 'sparx_reader',\n        job_data TEXT NOT NULL,\n        status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        effective_priority INTEGER DEFAULT 0,\n        scheduled_time DATETIME,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        error_message TEXT,\n        retry_count INTEGER DEFAULT 0,\n        max_retries INTEGER DEFAULT 3,\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id),\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Queue schedules table for conflict detection\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_schedules (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        scheduled_time DATETIME NOT NULL,\n        duration_minutes INTEGER DEFAULT 30,\n        job_id INTEGER,\n        batch_id INTEGER,\n        status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (job_id) REFERENCES queue_jobs (id),\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id)\n      )\n    `);\n        // Create indexes for better performance\n        this.db.exec(`\n      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n      CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_code ON license_keys(key_code);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_expires ON license_keys(expires_at);\n      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);\n      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_created ON activity_logs(created_at);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_user ON encrypted_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_key ON encrypted_credentials(login_key);\n      CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time);\n    `);\n    }\n    createDefaultAdmin() {\n        const adminExists = this.db.prepare(\"SELECT id FROM users WHERE role = ? LIMIT 1\").get(\"admin\");\n        if (!adminExists) {\n            const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);\n            const stmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, role, is_active)\n        VALUES (?, ?, ?, ?)\n      `);\n            stmt.run(process.env.DEFAULT_ADMIN_USERNAME, hashedPassword, \"admin\", 1);\n            console.log(`Default admin user created: ${process.env.DEFAULT_ADMIN_USERNAME}/[hidden]`);\n        }\n    }\n    // User management methods\n    createUser(username, password, licenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // Validate license key\n            const licenseStmt = this.db.prepare(`\n        SELECT id, max_uses, current_uses, expires_at, is_active \n        FROM license_keys \n        WHERE key_code = ? AND is_active = 1\n      `);\n            const license = licenseStmt.get(licenseKey);\n            if (!license) {\n                throw new Error(\"Invalid license key\");\n            }\n            if (new Date(license.expires_at) < new Date()) {\n                throw new Error(\"License key has expired\");\n            }\n            if (license.current_uses >= license.max_uses) {\n                throw new Error(\"License key has reached maximum uses\");\n            }\n            // Check if username already exists\n            const userExists = this.db.prepare(\"SELECT id FROM users WHERE username = ?\").get(username);\n            if (userExists) {\n                throw new Error(\"Username already exists\");\n            }\n            // Create user\n            const hashedPassword = bcrypt.hashSync(password, 12);\n            const userStmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, license_key_id, role)\n        VALUES (?, ?, ?, ?)\n      `);\n            const result = userStmt.run(username, hashedPassword, license.id, \"user\");\n            // Update license key usage\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(license.id);\n            return result.lastInsertRowid;\n        });\n        return transaction();\n    }\n    authenticateUser(username, password) {\n        const stmt = this.db.prepare(`\n      SELECT u.*, lk.expires_at as license_expires, lk.is_active as license_active\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.username = ? AND u.is_active = 1\n    `);\n        const user = stmt.get(username);\n        if (!user) {\n            throw new Error(\"Invalid credentials\");\n        }\n        const isValidPassword = bcrypt.compareSync(password, user.password_hash);\n        if (!isValidPassword) {\n            throw new Error(\"Invalid credentials\");\n        }\n        // Check license validity for non-admin users\n        if (user.role !== \"admin\") {\n            if (!user.license_active || new Date(user.license_expires) < new Date()) {\n                throw new Error(\"License has expired\");\n            }\n        }\n        // Update last login\n        const updateStmt = this.db.prepare(\"UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?\");\n        updateStmt.run(user.id);\n        // Remove sensitive data\n        delete user.password_hash;\n        return user;\n    }\n    // License key management methods\n    createLicenseKey(durationDays, maxUses = 1, features = [], createdBy = null) {\n        const keyCode = this.generateLicenseKey();\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + durationDays);\n        const stmt = this.db.prepare(`\n      INSERT INTO license_keys (key_code, duration_days, max_uses, expires_at, created_by, features)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(keyCode, durationDays, maxUses, expiresAt.toISOString(), createdBy, JSON.stringify(features));\n        return {\n            id: result.lastInsertRowid,\n            keyCode,\n            durationDays,\n            maxUses,\n            expiresAt: expiresAt.toISOString(),\n            features\n        };\n    }\n    generateLicenseKey() {\n        const segments = [];\n        for(let i = 0; i < 4; i++){\n            segments.push(uuidv4().replace(/-/g, \"\").substring(0, 8).toUpperCase());\n        }\n        return `SRX-${segments.join(\"-\")}`;\n    }\n    getLicenseKeys(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        lk.*,\n        u.username as created_by_username,\n        COUNT(users.id) as users_count\n      FROM license_keys lk\n      LEFT JOIN users u ON lk.created_by = u.id\n      LEFT JOIN users ON users.license_key_id = lk.id\n      GROUP BY lk.id\n      ORDER BY lk.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    deactivateLicenseKey(keyId) {\n        const stmt = this.db.prepare(\"UPDATE license_keys SET is_active = 0 WHERE id = ?\");\n        return stmt.run(keyId);\n    }\n    // Get detailed license information for a user\n    getUserLicenseStatus(userId) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id as user_id,\n        u.username,\n        lk.id as license_id,\n        lk.key_code,\n        lk.max_uses,\n        lk.current_uses,\n        lk.expires_at,\n        lk.is_active,\n        CASE \n          WHEN lk.expires_at <= datetime('now') THEN 'expired'\n          WHEN lk.current_uses >= lk.max_uses THEN 'maxed_out'\n          WHEN lk.is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as license_status\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.id = ?\n    `);\n        return stmt.get(userId);\n    }\n    // Validate a license key for renewal (check if it's valid and has available uses)\n    validateLicenseForRenewal(licenseKey) {\n        const stmt = this.db.prepare(`\n      SELECT \n        id,\n        key_code,\n        max_uses,\n        current_uses,\n        expires_at,\n        is_active,\n        CASE \n          WHEN expires_at <= datetime('now') THEN 'expired'\n          WHEN current_uses >= max_uses THEN 'maxed_out'\n          WHEN is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as status\n      FROM license_keys \n      WHERE key_code = ?\n    `);\n        const license = stmt.get(licenseKey);\n        if (!license) {\n            return {\n                valid: false,\n                error: \"License key not found\"\n            };\n        }\n        if (license.status !== \"valid\") {\n            let errorMessage = \"License key is not valid\";\n            switch(license.status){\n                case \"expired\":\n                    errorMessage = \"License key has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"License key has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"License key is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage\n            };\n        }\n        return {\n            valid: true,\n            license\n        };\n    }\n    // Renew user's license with a new license key\n    renewUserLicense(userId, newLicenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // First validate the new license key\n            const validation = this.validateLicenseForRenewal(newLicenseKey);\n            if (!validation.valid) {\n                throw new Error(validation.error);\n            }\n            const newLicense = validation.license;\n            // Update user's license_key_id to the new license\n            const updateUserStmt = this.db.prepare(`\n        UPDATE users \n        SET license_key_id = ?, updated_at = CURRENT_TIMESTAMP \n        WHERE id = ?\n      `);\n            updateUserStmt.run(newLicense.id, userId);\n            // Increment the new license's current_uses\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(newLicense.id);\n            // Log the renewal activity\n            this.logActivity(userId, \"LICENSE_RENEWED\", `License renewed with key: ${newLicenseKey}`);\n            return {\n                success: true,\n                newLicenseId: newLicense.id,\n                newLicenseKey: newLicenseKey,\n                expiresAt: newLicense.expires_at,\n                maxUses: newLicense.max_uses,\n                currentUses: newLicense.current_uses + 1\n            };\n        });\n        return transaction();\n    }\n    // Session management\n    createSession(userId, tokenHash, expiresAt, ipAddress = null, userAgent = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, tokenHash, expiresAt, ipAddress, userAgent);\n    }\n    validateSession(tokenHash) {\n        const stmt = this.db.prepare(`\n      SELECT s.*, u.username, u.role, u.is_active as user_active\n      FROM user_sessions s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')\n    `);\n        return stmt.get(tokenHash);\n    }\n    invalidateSession(tokenHash) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?\");\n        return stmt.run(tokenHash);\n    }\n    invalidateAllUserSessions(userId) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE user_id = ?\");\n        return stmt.run(userId);\n    }\n    // Activity logging\n    logActivity(userId, action, details = null, ipAddress = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO activity_logs (user_id, action, details, ip_address)\n      VALUES (?, ?, ?, ?)\n    `);\n        return stmt.run(userId, action, details, ipAddress);\n    }\n    getActivityLogs(userId = null, limit = 100, offset = 0) {\n        let query = `\n      SELECT \n        al.*,\n        u.username\n      FROM activity_logs al\n      LEFT JOIN users u ON al.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE al.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY al.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Analytics methods\n    getSystemStats() {\n        const stats = {};\n        // Total users\n        stats.totalUsers = this.db.prepare(\"SELECT COUNT(*) as count FROM users WHERE role = 'user'\").get().count;\n        // Active users (logged in within last 30 days)\n        stats.activeUsers = this.db.prepare(`\n      SELECT COUNT(*) as count FROM users \n      WHERE role = 'user' AND last_login > datetime('now', '-30 days')\n    `).get().count;\n        // Total license keys\n        stats.totalLicenseKeys = this.db.prepare(\"SELECT COUNT(*) as count FROM license_keys\").get().count;\n        // Active license keys\n        stats.activeLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE is_active = 1 AND expires_at > datetime('now')\n    `).get().count;\n        // Expired license keys\n        stats.expiredLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE expires_at <= datetime('now')\n    `).get().count;\n        // Recent activity (last 24 hours)\n        stats.recentActivity = this.db.prepare(`\n      SELECT COUNT(*) as count FROM activity_logs \n      WHERE created_at > datetime('now', '-1 day')\n    `).get().count;\n        return stats;\n    }\n    // User management for admin\n    getUsers(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id,\n        u.username,\n        u.role,\n        u.created_at,\n        u.last_login,\n        u.is_active,\n        lk.key_code,\n        lk.expires_at as license_expires\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    toggleUserStatus(userId) {\n        const stmt = this.db.prepare(\"UPDATE users SET is_active = NOT is_active WHERE id = ?\");\n        return stmt.run(userId);\n    }\n    // Cleanup methods\n    cleanupExpiredSessions() {\n        const stmt = this.db.prepare('DELETE FROM user_sessions WHERE expires_at <= datetime(\"now\")');\n        return stmt.run();\n    }\n    cleanupOldLogs(daysToKeep = 90) {\n        const stmt = this.db.prepare(`\n      DELETE FROM activity_logs \n      WHERE created_at <= datetime('now', '-${daysToKeep} days')\n    `);\n        return stmt.run();\n    }\n    // Encrypted credentials methods\n    saveEncryptedCredentials(userId, loginMethod, school, email, password, encryptionKey) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        // Generate a unique login key\n        const loginKey = \"SLK-\" + crypto.randomBytes(8).toString(\"hex\").toUpperCase();\n        // Create encryption IV\n        const iv = crypto.randomBytes(16);\n        const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n        // Encrypt school, email and password\n        const cipher1 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedSchool = cipher1.update(school, \"utf8\", \"hex\") + cipher1.final(\"hex\");\n        const cipher2 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedEmail = cipher2.update(email, \"utf8\", \"hex\") + cipher2.final(\"hex\");\n        const cipher3 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedPassword = cipher3.update(password, \"utf8\", \"hex\") + cipher3.final(\"hex\");\n        const stmt = this.db.prepare(`\n      INSERT INTO encrypted_credentials (user_id, login_key, login_method, encrypted_school, encrypted_email, encrypted_password, encryption_iv)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        stmt.run(userId, loginKey, loginMethod, encryptedSchool, encryptedEmail, encryptedPassword, iv.toString(\"hex\"));\n        return loginKey;\n    }\n    getEncryptedCredentials(loginKey, encryptionKey) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM encrypted_credentials \n      WHERE login_key = ? AND is_active = 1\n    `);\n        const result = stmt.get(loginKey);\n        if (!result) return null;\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        try {\n            const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n            const iv = Buffer.from(result.encryption_iv, \"hex\");\n            // Decrypt school, email and password\n            const decipher1 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const school = result.encrypted_school ? decipher1.update(result.encrypted_school, \"hex\", \"utf8\") + decipher1.final(\"utf8\") : null;\n            const decipher2 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const email = result.encrypted_email ? decipher2.update(result.encrypted_email, \"hex\", \"utf8\") + decipher2.final(\"utf8\") : null;\n            const decipher3 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const password = result.encrypted_password ? decipher3.update(result.encrypted_password, \"hex\", \"utf8\") + decipher3.final(\"utf8\") : null;\n            return {\n                loginMethod: result.login_method,\n                school,\n                email,\n                password,\n                userId: result.user_id\n            };\n        } catch (error) {\n            console.error(\"Failed to decrypt credentials:\", error);\n            return null;\n        }\n    }\n    getUserCredentials(userId) {\n        const stmt = this.db.prepare(`\n      SELECT login_key, login_method, created_at FROM encrypted_credentials \n      WHERE user_id = ? AND is_active = 1\n      ORDER BY created_at DESC\n    `);\n        return stmt.all(userId);\n    }\n    deactivateCredentials(loginKey) {\n        const stmt = this.db.prepare(\"UPDATE encrypted_credentials SET is_active = 0 WHERE login_key = ?\");\n        return stmt.run(loginKey);\n    }\n    // License Feature Settings Methods\n    setLicenseFeatures(licenseKeyId, features) {\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO license_feature_settings \n      (license_key_id, max_accounts_per_batch, priority_level, scheduling_access, updated_at)\n      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(licenseKeyId, features.max_accounts_per_batch || 0, features.priority_level || 0, features.scheduling_access ? 1 : 0);\n    }\n    getLicenseFeatures(licenseKeyId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM license_feature_settings WHERE license_key_id = ?\n    `);\n        const result = stmt.get(licenseKeyId);\n        if (!result) {\n            // Return default features if none set\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access)\n        };\n    }\n    getUserLicenseFeatures(userId) {\n        const stmt = this.db.prepare(`\n      SELECT lfs.* FROM license_feature_settings lfs\n      JOIN users u ON u.license_key_id = lfs.license_key_id\n      WHERE u.id = ?\n    `);\n        const result = stmt.get(userId);\n        if (!result) {\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access)\n        };\n    }\n    // Queue Batch Methods\n    createQueueBatch(userId, batchName, accounts, scheduledTime = null) {\n        const transaction = this.db.transaction(()=>{\n            // Get user's license features\n            const features = this.getUserLicenseFeatures(userId);\n            // Validate batch size against license limits\n            if (features.max_accounts_per_batch > 0 && accounts.length > features.max_accounts_per_batch) {\n                throw new Error(`Batch size (${accounts.length}) exceeds license limit (${features.max_accounts_per_batch})`);\n            }\n            // Validate scheduling access\n            if (scheduledTime && !features.scheduling_access) {\n                throw new Error(\"Scheduling access not available for this license\");\n            }\n            // Create batch\n            const batchStmt = this.db.prepare(`\n        INSERT INTO queue_batches (user_id, batch_name, total_accounts, priority_level, scheduled_time)\n        VALUES (?, ?, ?, ?, ?)\n      `);\n            const batchResult = batchStmt.run(userId, batchName, accounts.length, features.priority_level, scheduledTime);\n            const batchId = batchResult.lastInsertRowid;\n            // Create individual jobs for each account\n            const jobStmt = this.db.prepare(`\n        INSERT INTO queue_jobs (batch_id, user_id, job_data, priority_level, effective_priority, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?)\n      `);\n            accounts.forEach((account)=>{\n                const effectivePriority = this.calculateEffectivePriority(features.priority_level, scheduledTime);\n                jobStmt.run(batchId, userId, JSON.stringify(account), features.priority_level, effectivePriority, scheduledTime);\n            });\n            // Create schedule entry if scheduled\n            if (scheduledTime) {\n                this.createScheduleEntry(userId, scheduledTime, null, batchId);\n            }\n            // Log activity\n            this.logActivity(userId, \"BATCH_CREATED\", `Created batch: ${batchName} with ${accounts.length} accounts`);\n            return batchId;\n        });\n        return transaction();\n    }\n    calculateEffectivePriority(basePriority, scheduledTime) {\n        let effectivePriority = basePriority;\n        // Boost priority for scheduled jobs approaching their time\n        if (scheduledTime) {\n            const now = new Date();\n            const scheduled = new Date(scheduledTime);\n            const timeDiff = scheduled.getTime() - now.getTime();\n            const hoursUntil = timeDiff / (1000 * 60 * 60);\n            if (hoursUntil <= 1) {\n                effectivePriority += 5; // High boost for jobs due within an hour\n            } else if (hoursUntil <= 6) {\n                effectivePriority += 2; // Medium boost for jobs due within 6 hours\n            }\n        }\n        // Apply starvation prevention (boost priority for old jobs)\n        // This would be implemented in a background process\n        return Math.min(effectivePriority, 10); // Cap at maximum priority\n    }\n    getQueueBatches(userId = null, status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT qb.*, u.username\n      FROM queue_batches qb\n      JOIN users u ON qb.user_id = u.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (userId) {\n            query += \" AND qb.user_id = ?\";\n            params.push(userId);\n        }\n        if (status) {\n            query += \" AND qb.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY qb.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getBatchJobs(batchId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_jobs \n      WHERE batch_id = ? \n      ORDER BY effective_priority DESC, created_at ASC\n    `);\n        return stmt.all(batchId);\n    }\n    updateBatchStatus(batchId, status, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_batches \n      SET status = ?, completed_at = ?, updated_at = CURRENT_TIMESTAMP\n      WHERE id = ?\n    `);\n        return stmt.run(status, completedAt, batchId);\n    }\n    // Queue Job Methods\n    getNextQueueJob() {\n        const stmt = this.db.prepare(`\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n      WHERE qj.status = 'queued' \n      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))\n      ORDER BY qj.effective_priority DESC, qj.created_at ASC\n      LIMIT 1\n    `);\n        return stmt.get();\n    }\n    updateJobStatus(jobId, status, errorMessage = null, startedAt = null, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET status = ?, error_message = ?, started_at = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, errorMessage, startedAt, completedAt, jobId);\n    }\n    incrementJobRetry(jobId) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET retry_count = retry_count + 1, status = 'queued'\n      WHERE id = ? AND retry_count < max_retries\n    `);\n        return stmt.run(jobId);\n    }\n    // Scheduling Methods\n    createScheduleEntry(userId, scheduledTime, jobId = null, batchId = null, durationMinutes = 30) {\n        // Check for conflicts\n        const conflicts = this.checkScheduleConflicts(userId, scheduledTime, durationMinutes);\n        if (conflicts.length > 0) {\n            throw new Error(`Schedule conflict detected at ${scheduledTime}`);\n        }\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_schedules (user_id, scheduled_time, duration_minutes, job_id, batch_id)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, scheduledTime, durationMinutes, jobId, batchId);\n    }\n    checkScheduleConflicts(userId, scheduledTime, durationMinutes) {\n        const startTime = new Date(scheduledTime);\n        const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_schedules\n      WHERE user_id = ? \n      AND status IN ('scheduled', 'active')\n      AND (\n        (scheduled_time <= ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') > ?) OR\n        (scheduled_time < ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') >= ?)\n      )\n    `);\n        return stmt.all(userId, startTime.toISOString(), startTime.toISOString(), endTime.toISOString(), endTime.toISOString());\n    }\n    getUserSchedules(userId, startDate = null, endDate = null) {\n        let query = `\n      SELECT qs.*, qj.job_type, qb.batch_name\n      FROM queue_schedules qs\n      LEFT JOIN queue_jobs qj ON qs.job_id = qj.id\n      LEFT JOIN queue_batches qb ON qs.batch_id = qb.id\n      WHERE qs.user_id = ?\n    `;\n        const params = [\n            userId\n        ];\n        if (startDate) {\n            query += \" AND qs.scheduled_time >= ?\";\n            params.push(startDate);\n        }\n        if (endDate) {\n            query += \" AND qs.scheduled_time <= ?\";\n            params.push(endDate);\n        }\n        query += \" ORDER BY qs.scheduled_time ASC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Priority Management Methods\n    updateJobPriority(jobId, newPriority, adminOverride = false) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = ?, priority_level = ?\n      WHERE id = ?\n    `);\n        const result = stmt.run(newPriority, adminOverride ? newPriority : null, jobId);\n        if (adminOverride) {\n            this.logActivity(null, \"ADMIN_PRIORITY_OVERRIDE\", `Job ${jobId} priority set to ${newPriority}`);\n        }\n        return result;\n    }\n    applyStarvationPrevention() {\n        // Boost priority for jobs that have been waiting too long\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = CASE \n        WHEN datetime('now') > datetime(created_at, '+2 hours') THEN MIN(effective_priority + 2, 10)\n        WHEN datetime('now') > datetime(created_at, '+1 hour') THEN MIN(effective_priority + 1, 10)\n        ELSE effective_priority\n      END\n      WHERE status = 'queued'\n    `);\n        return stmt.run();\n    }\n    getQueueStats() {\n        const stats = {};\n        // Total jobs by status\n        const statusStmt = this.db.prepare(`\n      SELECT status, COUNT(*) as count \n      FROM queue_jobs \n      GROUP BY status\n    `);\n        stats.jobsByStatus = statusStmt.all();\n        // Jobs by priority level\n        const priorityStmt = this.db.prepare(`\n      SELECT effective_priority, COUNT(*) as count \n      FROM queue_jobs \n      WHERE status = 'queued'\n      GROUP BY effective_priority\n      ORDER BY effective_priority DESC\n    `);\n        stats.jobsByPriority = priorityStmt.all();\n        // Average wait time\n        const waitTimeStmt = this.db.prepare(`\n      SELECT AVG(julianday(started_at) - julianday(created_at)) * 24 * 60 as avg_wait_minutes\n      FROM queue_jobs \n      WHERE started_at IS NOT NULL\n    `);\n        stats.averageWaitTime = waitTimeStmt.get()?.avg_wait_minutes || 0;\n        return stats;\n    }\n    close() {\n        this.db.close();\n    }\n}\n// Create data directory if it doesn't exist\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst dataDir = path.join(process.cwd(), \"data\");\nif (!fs.existsSync(dataDir)) {\n    fs.mkdirSync(dataDir, {\n        recursive: true\n    });\n}\n// Export singleton instance\nlet dbInstance = null;\nfunction getDatabase() {\n    if (!dbInstance) {\n        dbInstance = new DatabaseManager();\n    }\n    return dbInstance;\n}\nmodule.exports = {\n    getDatabase,\n    DatabaseManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGF0YWJhc2UuanMiLCJtYXBwaW5ncyI6IjtBQUFBLE1BQU1BLFdBQVdDLG1CQUFPQSxDQUFDO0FBQ3pCLE1BQU1DLFNBQVNELG1CQUFPQSxDQUFDO0FBQ3ZCLE1BQU0sRUFBRUUsSUFBSUMsTUFBTSxFQUFFLEdBQUdILG1CQUFPQSxDQUFDO0FBQy9CLE1BQU1JLE9BQU9KLG1CQUFPQSxDQUFDO0FBRXJCLE1BQU1LO0lBQ0pDLGFBQWM7UUFDWixNQUFNQyxTQUFTSCxLQUFLSSxJQUFJLENBQUNDLFFBQVFDLEdBQUcsSUFBSSxRQUFRO1FBQ2hELElBQUksQ0FBQ0MsRUFBRSxHQUFHLElBQUlaLFNBQVNRO1FBQ3ZCLElBQUksQ0FBQ0ssZ0JBQWdCO1FBQ3JCLElBQUksQ0FBQ0Msa0JBQWtCO0lBQ3pCO0lBRUFELG1CQUFtQjtRQUNqQixjQUFjO1FBQ2QsSUFBSSxDQUFDRCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7O0lBYWQsQ0FBQztRQUVELHFCQUFxQjtRQUNyQixJQUFJLENBQUNILEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7O0lBY2QsQ0FBQztRQUVELHNCQUFzQjtRQUN0QixJQUFJLENBQUNILEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7Ozs7OztJQVlkLENBQUM7UUFFRCxzQkFBc0I7UUFDdEIsSUFBSSxDQUFDSCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7O0lBVWQsQ0FBQztRQUVELHdCQUF3QjtRQUN4QixJQUFJLENBQUNILEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7OztJQVNkLENBQUM7UUFFRCxvQ0FBb0M7UUFDcEMsSUFBSSxDQUFDSCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7SUFlZCxDQUFDO1FBRUQsaUNBQWlDO1FBQ2pDLElBQUksQ0FBQ0gsRUFBRSxDQUFDRyxJQUFJLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7O0lBWWQsQ0FBQztRQUVELHNCQUFzQjtRQUN0QixJQUFJLENBQUNILEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7SUFnQmQsQ0FBQztRQUVELG1CQUFtQjtRQUNuQixJQUFJLENBQUNILEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBb0JkLENBQUM7UUFFRCwrQ0FBK0M7UUFDL0MsSUFBSSxDQUFDSCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7OztJQWNkLENBQUM7UUFFRCx3Q0FBd0M7UUFDeEMsSUFBSSxDQUFDSCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBc0JkLENBQUM7SUFDSDtJQUVBRCxxQkFBcUI7UUFDbkIsTUFBTUUsY0FBYyxJQUFJLENBQUNKLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLCtDQUErQ0MsR0FBRyxDQUFDO1FBRXZGLElBQUksQ0FBQ0YsYUFBYTtZQUNoQixNQUFNRyxpQkFBaUJqQixPQUFPa0IsUUFBUSxDQUFDVixRQUFRVyxHQUFHLENBQUNDLHNCQUFzQixFQUFFO1lBQzNFLE1BQU1DLE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7TUFHOUIsQ0FBQztZQUVETSxLQUFLQyxHQUFHLENBQUNkLFFBQVFXLEdBQUcsQ0FBQ0ksc0JBQXNCLEVBQUVOLGdCQUFnQixTQUFTO1lBQ3RFTyxRQUFRQyxHQUFHLENBQUMsQ0FBQyw0QkFBNEIsRUFBRWpCLFFBQVFXLEdBQUcsQ0FBQ0ksc0JBQXNCLENBQUMsU0FBUyxDQUFDO1FBQzFGO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUJHLFdBQVdDLFFBQVEsRUFBRUMsUUFBUSxFQUFFQyxVQUFVLEVBQUU7UUFDekMsTUFBTUMsY0FBYyxJQUFJLENBQUNwQixFQUFFLENBQUNvQixXQUFXLENBQUM7WUFDdEMsdUJBQXVCO1lBQ3ZCLE1BQU1DLGNBQWMsSUFBSSxDQUFDckIsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7OztNQUlyQyxDQUFDO1lBQ0QsTUFBTWlCLFVBQVVELFlBQVlmLEdBQUcsQ0FBQ2E7WUFFaEMsSUFBSSxDQUFDRyxTQUFTO2dCQUNaLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLElBQUksSUFBSUMsS0FBS0YsUUFBUUcsVUFBVSxJQUFJLElBQUlELFFBQVE7Z0JBQzdDLE1BQU0sSUFBSUQsTUFBTTtZQUNsQjtZQUVBLElBQUlELFFBQVFJLFlBQVksSUFBSUosUUFBUUssUUFBUSxFQUFFO2dCQUM1QyxNQUFNLElBQUlKLE1BQU07WUFDbEI7WUFFQSxtQ0FBbUM7WUFDbkMsTUFBTUssYUFBYSxJQUFJLENBQUM1QixFQUFFLENBQUNLLE9BQU8sQ0FBQywyQ0FBMkNDLEdBQUcsQ0FBQ1c7WUFDbEYsSUFBSVcsWUFBWTtnQkFDZCxNQUFNLElBQUlMLE1BQU07WUFDbEI7WUFFQSxjQUFjO1lBQ2QsTUFBTWhCLGlCQUFpQmpCLE9BQU9rQixRQUFRLENBQUNVLFVBQVU7WUFDakQsTUFBTVcsV0FBVyxJQUFJLENBQUM3QixFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7TUFHbEMsQ0FBQztZQUVELE1BQU15QixTQUFTRCxTQUFTakIsR0FBRyxDQUFDSyxVQUFVVixnQkFBZ0JlLFFBQVFTLEVBQUUsRUFBRTtZQUVsRSwyQkFBMkI7WUFDM0IsTUFBTUMsb0JBQW9CLElBQUksQ0FBQ2hDLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7Ozs7TUFJM0MsQ0FBQztZQUNEMkIsa0JBQWtCcEIsR0FBRyxDQUFDVSxRQUFRUyxFQUFFO1lBRWhDLE9BQU9ELE9BQU9HLGVBQWU7UUFDL0I7UUFFQSxPQUFPYjtJQUNUO0lBRUFjLGlCQUFpQmpCLFFBQVEsRUFBRUMsUUFBUSxFQUFFO1FBQ25DLE1BQU1QLE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7OztJQUs5QixDQUFDO1FBRUQsTUFBTThCLE9BQU94QixLQUFLTCxHQUFHLENBQUNXO1FBRXRCLElBQUksQ0FBQ2tCLE1BQU07WUFDVCxNQUFNLElBQUlaLE1BQU07UUFDbEI7UUFFQSxNQUFNYSxrQkFBa0I5QyxPQUFPK0MsV0FBVyxDQUFDbkIsVUFBVWlCLEtBQUtHLGFBQWE7UUFDdkUsSUFBSSxDQUFDRixpQkFBaUI7WUFDcEIsTUFBTSxJQUFJYixNQUFNO1FBQ2xCO1FBRUEsNkNBQTZDO1FBQzdDLElBQUlZLEtBQUtJLElBQUksS0FBSyxTQUFTO1lBQ3pCLElBQUksQ0FBQ0osS0FBS0ssY0FBYyxJQUFJLElBQUloQixLQUFLVyxLQUFLTSxlQUFlLElBQUksSUFBSWpCLFFBQVE7Z0JBQ3ZFLE1BQU0sSUFBSUQsTUFBTTtZQUNsQjtRQUNGO1FBRUEsb0JBQW9CO1FBQ3BCLE1BQU1tQixhQUFhLElBQUksQ0FBQzFDLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDO1FBQ25DcUMsV0FBVzlCLEdBQUcsQ0FBQ3VCLEtBQUtKLEVBQUU7UUFFdEIsd0JBQXdCO1FBQ3hCLE9BQU9JLEtBQUtHLGFBQWE7UUFDekIsT0FBT0g7SUFDVDtJQUVBLGlDQUFpQztJQUNqQ1EsaUJBQWlCQyxZQUFZLEVBQUVDLFVBQVUsQ0FBQyxFQUFFQyxXQUFXLEVBQUUsRUFBRUMsWUFBWSxJQUFJLEVBQUU7UUFDM0UsTUFBTUMsVUFBVSxJQUFJLENBQUNDLGtCQUFrQjtRQUN2QyxNQUFNQyxZQUFZLElBQUkxQjtRQUN0QjBCLFVBQVVDLE9BQU8sQ0FBQ0QsVUFBVUUsT0FBTyxLQUFLUjtRQUV4QyxNQUFNakMsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7OztJQUc5QixDQUFDO1FBRUQsTUFBTXlCLFNBQVNuQixLQUFLQyxHQUFHLENBQ3JCb0MsU0FDQUosY0FDQUMsU0FDQUssVUFBVUcsV0FBVyxJQUNyQk4sV0FDQU8sS0FBS0MsU0FBUyxDQUFDVDtRQUdqQixPQUFPO1lBQ0xmLElBQUlELE9BQU9HLGVBQWU7WUFDMUJlO1lBQ0FKO1lBQ0FDO1lBQ0FLLFdBQVdBLFVBQVVHLFdBQVc7WUFDaENQO1FBQ0Y7SUFDRjtJQUVBRyxxQkFBcUI7UUFDbkIsTUFBTU8sV0FBVyxFQUFFO1FBQ25CLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJLEdBQUdBLElBQUs7WUFDMUJELFNBQVNFLElBQUksQ0FBQ2xFLFNBQVNtRSxPQUFPLENBQUMsTUFBTSxJQUFJQyxTQUFTLENBQUMsR0FBRyxHQUFHQyxXQUFXO1FBQ3RFO1FBQ0EsT0FBTyxDQUFDLElBQUksRUFBRUwsU0FBUzNELElBQUksQ0FBQyxLQUFLLENBQUM7SUFDcEM7SUFFQWlFLGVBQWVDLFFBQVEsRUFBRSxFQUFFQyxTQUFTLENBQUMsRUFBRTtRQUNyQyxNQUFNckQsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7Ozs7Ozs7Ozs7O0lBVzlCLENBQUM7UUFFRCxPQUFPTSxLQUFLc0QsR0FBRyxDQUFDRixPQUFPQztJQUN6QjtJQUVBRSxxQkFBcUJDLEtBQUssRUFBRTtRQUMxQixNQUFNeEQsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDO1FBQzdCLE9BQU9NLEtBQUtDLEdBQUcsQ0FBQ3VEO0lBQ2xCO0lBRUEsOENBQThDO0lBQzlDQyxxQkFBcUJDLE1BQU0sRUFBRTtRQUMzQixNQUFNMUQsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFtQjlCLENBQUM7UUFFRCxPQUFPTSxLQUFLTCxHQUFHLENBQUMrRDtJQUNsQjtJQUVBLGtGQUFrRjtJQUNsRkMsMEJBQTBCbkQsVUFBVSxFQUFFO1FBQ3BDLE1BQU1SLE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7O0lBZ0I5QixDQUFDO1FBRUQsTUFBTWlCLFVBQVVYLEtBQUtMLEdBQUcsQ0FBQ2E7UUFFekIsSUFBSSxDQUFDRyxTQUFTO1lBQ1osT0FBTztnQkFBRWlELE9BQU87Z0JBQU9DLE9BQU87WUFBd0I7UUFDeEQ7UUFFQSxJQUFJbEQsUUFBUW1ELE1BQU0sS0FBSyxTQUFTO1lBQzlCLElBQUlDLGVBQWU7WUFDbkIsT0FBUXBELFFBQVFtRCxNQUFNO2dCQUNwQixLQUFLO29CQUNIQyxlQUFlO29CQUNmO2dCQUNGLEtBQUs7b0JBQ0hBLGVBQWU7b0JBQ2Y7Z0JBQ0YsS0FBSztvQkFDSEEsZUFBZTtvQkFDZjtZQUNKO1lBQ0EsT0FBTztnQkFBRUgsT0FBTztnQkFBT0MsT0FBT0U7WUFBYTtRQUM3QztRQUVBLE9BQU87WUFBRUgsT0FBTztZQUFNakQ7UUFBUTtJQUNoQztJQUVBLDhDQUE4QztJQUM5Q3FELGlCQUFpQk4sTUFBTSxFQUFFTyxhQUFhLEVBQUU7UUFDdEMsTUFBTXhELGNBQWMsSUFBSSxDQUFDcEIsRUFBRSxDQUFDb0IsV0FBVyxDQUFDO1lBQ3RDLHFDQUFxQztZQUNyQyxNQUFNeUQsYUFBYSxJQUFJLENBQUNQLHlCQUF5QixDQUFDTTtZQUNsRCxJQUFJLENBQUNDLFdBQVdOLEtBQUssRUFBRTtnQkFDckIsTUFBTSxJQUFJaEQsTUFBTXNELFdBQVdMLEtBQUs7WUFDbEM7WUFFQSxNQUFNTSxhQUFhRCxXQUFXdkQsT0FBTztZQUVyQyxrREFBa0Q7WUFDbEQsTUFBTXlELGlCQUFpQixJQUFJLENBQUMvRSxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7O01BSXhDLENBQUM7WUFDRDBFLGVBQWVuRSxHQUFHLENBQUNrRSxXQUFXL0MsRUFBRSxFQUFFc0M7WUFFbEMsMkNBQTJDO1lBQzNDLE1BQU1yQyxvQkFBb0IsSUFBSSxDQUFDaEMsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7OztNQUkzQyxDQUFDO1lBQ0QyQixrQkFBa0JwQixHQUFHLENBQUNrRSxXQUFXL0MsRUFBRTtZQUVuQywyQkFBMkI7WUFDM0IsSUFBSSxDQUFDaUQsV0FBVyxDQUFDWCxRQUFRLG1CQUFtQixDQUFDLDBCQUEwQixFQUFFTyxjQUFjLENBQUM7WUFFeEYsT0FBTztnQkFDTEssU0FBUztnQkFDVEMsY0FBY0osV0FBVy9DLEVBQUU7Z0JBQzNCNkMsZUFBZUE7Z0JBQ2YxQixXQUFXNEIsV0FBV3JELFVBQVU7Z0JBQ2hDb0IsU0FBU2lDLFdBQVduRCxRQUFRO2dCQUM1QndELGFBQWFMLFdBQVdwRCxZQUFZLEdBQUc7WUFDekM7UUFDRjtRQUVBLE9BQU9OO0lBQ1Q7SUFFQSxxQkFBcUI7SUFDckJnRSxjQUFjZixNQUFNLEVBQUVnQixTQUFTLEVBQUVuQyxTQUFTLEVBQUVvQyxZQUFZLElBQUksRUFBRUMsWUFBWSxJQUFJLEVBQUU7UUFDOUUsTUFBTTVFLE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7SUFHOUIsQ0FBQztRQUVELE9BQU9NLEtBQUtDLEdBQUcsQ0FBQ3lELFFBQVFnQixXQUFXbkMsV0FBV29DLFdBQVdDO0lBQzNEO0lBRUFDLGdCQUFnQkgsU0FBUyxFQUFFO1FBQ3pCLE1BQU0xRSxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7Ozs7SUFLOUIsQ0FBQztRQUVELE9BQU9NLEtBQUtMLEdBQUcsQ0FBQytFO0lBQ2xCO0lBRUFJLGtCQUFrQkosU0FBUyxFQUFFO1FBQzNCLE1BQU0xRSxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUM7UUFDN0IsT0FBT00sS0FBS0MsR0FBRyxDQUFDeUU7SUFDbEI7SUFFQUssMEJBQTBCckIsTUFBTSxFQUFFO1FBQ2hDLE1BQU0xRCxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUM7UUFDN0IsT0FBT00sS0FBS0MsR0FBRyxDQUFDeUQ7SUFDbEI7SUFFQSxtQkFBbUI7SUFDbkJXLFlBQVlYLE1BQU0sRUFBRXNCLE1BQU0sRUFBRUMsVUFBVSxJQUFJLEVBQUVOLFlBQVksSUFBSSxFQUFFO1FBQzVELE1BQU0zRSxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7O0lBRzlCLENBQUM7UUFFRCxPQUFPTSxLQUFLQyxHQUFHLENBQUN5RCxRQUFRc0IsUUFBUUMsU0FBU047SUFDM0M7SUFFQU8sZ0JBQWdCeEIsU0FBUyxJQUFJLEVBQUVOLFFBQVEsR0FBRyxFQUFFQyxTQUFTLENBQUMsRUFBRTtRQUN0RCxJQUFJOEIsUUFBUSxDQUFDOzs7Ozs7SUFNYixDQUFDO1FBRUQsTUFBTUMsU0FBUyxFQUFFO1FBRWpCLElBQUkxQixRQUFRO1lBQ1Z5QixTQUFTO1lBQ1RDLE9BQU9yQyxJQUFJLENBQUNXO1FBQ2Q7UUFFQXlCLFNBQVM7UUFDVEMsT0FBT3JDLElBQUksQ0FBQ0ssT0FBT0M7UUFFbkIsTUFBTXJELE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQ3lGO1FBQzdCLE9BQU9uRixLQUFLc0QsR0FBRyxJQUFJOEI7SUFDckI7SUFFQSxvQkFBb0I7SUFDcEJDLGlCQUFpQjtRQUNmLE1BQU1DLFFBQVEsQ0FBQztRQUVmLGNBQWM7UUFDZEEsTUFBTUMsVUFBVSxHQUFHLElBQUksQ0FBQ2xHLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLDJEQUE2REMsR0FBRyxHQUFHNkYsS0FBSztRQUUzRywrQ0FBK0M7UUFDL0NGLE1BQU1HLFdBQVcsR0FBRyxJQUFJLENBQUNwRyxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7SUFHckMsQ0FBQyxFQUFFQyxHQUFHLEdBQUc2RixLQUFLO1FBRWQscUJBQXFCO1FBQ3JCRixNQUFNSSxnQkFBZ0IsR0FBRyxJQUFJLENBQUNyRyxFQUFFLENBQUNLLE9BQU8sQ0FBQyw4Q0FBOENDLEdBQUcsR0FBRzZGLEtBQUs7UUFFbEcsc0JBQXNCO1FBQ3RCRixNQUFNSyxpQkFBaUIsR0FBRyxJQUFJLENBQUN0RyxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7SUFHM0MsQ0FBQyxFQUFFQyxHQUFHLEdBQUc2RixLQUFLO1FBRWQsdUJBQXVCO1FBQ3ZCRixNQUFNTSxrQkFBa0IsR0FBRyxJQUFJLENBQUN2RyxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7SUFHNUMsQ0FBQyxFQUFFQyxHQUFHLEdBQUc2RixLQUFLO1FBRWQsa0NBQWtDO1FBQ2xDRixNQUFNTyxjQUFjLEdBQUcsSUFBSSxDQUFDeEcsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7O0lBR3hDLENBQUMsRUFBRUMsR0FBRyxHQUFHNkYsS0FBSztRQUVkLE9BQU9GO0lBQ1Q7SUFFQSw0QkFBNEI7SUFDNUJRLFNBQVMxQyxRQUFRLEVBQUUsRUFBRUMsU0FBUyxDQUFDLEVBQUU7UUFDL0IsTUFBTXJELE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7OztJQWM5QixDQUFDO1FBRUQsT0FBT00sS0FBS3NELEdBQUcsQ0FBQ0YsT0FBT0M7SUFDekI7SUFFQTBDLGlCQUFpQnJDLE1BQU0sRUFBRTtRQUN2QixNQUFNMUQsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDO1FBQzdCLE9BQU9NLEtBQUtDLEdBQUcsQ0FBQ3lEO0lBQ2xCO0lBRUEsa0JBQWtCO0lBQ2xCc0MseUJBQXlCO1FBQ3ZCLE1BQU1oRyxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUM7UUFDN0IsT0FBT00sS0FBS0MsR0FBRztJQUNqQjtJQUVBZ0csZUFBZUMsYUFBYSxFQUFFLEVBQUU7UUFDOUIsTUFBTWxHLE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs0Q0FFVSxFQUFFd0csV0FBVztJQUNyRCxDQUFDO1FBQ0QsT0FBT2xHLEtBQUtDLEdBQUc7SUFDakI7SUFFQSxnQ0FBZ0M7SUFDaENrRyx5QkFBeUJ6QyxNQUFNLEVBQUUwQyxXQUFXLEVBQUVDLE1BQU0sRUFBRUMsS0FBSyxFQUFFL0YsUUFBUSxFQUFFZ0csYUFBYSxFQUFFO1FBQ3BGLE1BQU1DLFNBQVM5SCxtQkFBT0EsQ0FBQztRQUV2Qiw4QkFBOEI7UUFDOUIsTUFBTStILFdBQVcsU0FBU0QsT0FBT0UsV0FBVyxDQUFDLEdBQUdDLFFBQVEsQ0FBQyxPQUFPekQsV0FBVztRQUUzRSx1QkFBdUI7UUFDdkIsTUFBTTBELEtBQUtKLE9BQU9FLFdBQVcsQ0FBQztRQUM5QixNQUFNRyxNQUFNTCxPQUFPTSxVQUFVLENBQUNQLGVBQWUsUUFBUTtRQUVyRCxxQ0FBcUM7UUFDckMsTUFBTVEsVUFBVVAsT0FBT1EsY0FBYyxDQUFDLGVBQWVILEtBQUtEO1FBQzFELE1BQU1LLGtCQUFrQkYsUUFBUUcsTUFBTSxDQUFDYixRQUFRLFFBQVEsU0FBU1UsUUFBUUksS0FBSyxDQUFDO1FBRTlFLE1BQU1DLFVBQVVaLE9BQU9RLGNBQWMsQ0FBQyxlQUFlSCxLQUFLRDtRQUMxRCxNQUFNUyxpQkFBaUJELFFBQVFGLE1BQU0sQ0FBQ1osT0FBTyxRQUFRLFNBQVNjLFFBQVFELEtBQUssQ0FBQztRQUU1RSxNQUFNRyxVQUFVZCxPQUFPUSxjQUFjLENBQUMsZUFBZUgsS0FBS0Q7UUFDMUQsTUFBTVcsb0JBQW9CRCxRQUFRSixNQUFNLENBQUMzRyxVQUFVLFFBQVEsU0FBUytHLFFBQVFILEtBQUssQ0FBQztRQUVsRixNQUFNbkgsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7OztJQUc5QixDQUFDO1FBRURNLEtBQUtDLEdBQUcsQ0FBQ3lELFFBQVErQyxVQUFVTCxhQUFhYSxpQkFBaUJJLGdCQUFnQkUsbUJBQW1CWCxHQUFHRCxRQUFRLENBQUM7UUFFeEcsT0FBT0Y7SUFDVDtJQUVBZSx3QkFBd0JmLFFBQVEsRUFBRUYsYUFBYSxFQUFFO1FBQy9DLE1BQU12RyxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7O0lBRzlCLENBQUM7UUFFRCxNQUFNeUIsU0FBU25CLEtBQUtMLEdBQUcsQ0FBQzhHO1FBQ3hCLElBQUksQ0FBQ3RGLFFBQVEsT0FBTztRQUVwQixNQUFNcUYsU0FBUzlILG1CQUFPQSxDQUFDO1FBRXZCLElBQUk7WUFDRixNQUFNbUksTUFBTUwsT0FBT00sVUFBVSxDQUFDUCxlQUFlLFFBQVE7WUFDckQsTUFBTUssS0FBS2EsT0FBT0MsSUFBSSxDQUFDdkcsT0FBT3dHLGFBQWEsRUFBRTtZQUU3QyxxQ0FBcUM7WUFDckMsTUFBTUMsWUFBWXBCLE9BQU9xQixnQkFBZ0IsQ0FBQyxlQUFlaEIsS0FBS0Q7WUFDOUQsTUFBTVAsU0FBU2xGLE9BQU8yRyxnQkFBZ0IsR0FBR0YsVUFBVVYsTUFBTSxDQUFDL0YsT0FBTzJHLGdCQUFnQixFQUFFLE9BQU8sVUFBVUYsVUFBVVQsS0FBSyxDQUFDLFVBQVU7WUFFOUgsTUFBTVksWUFBWXZCLE9BQU9xQixnQkFBZ0IsQ0FBQyxlQUFlaEIsS0FBS0Q7WUFDOUQsTUFBTU4sUUFBUW5GLE9BQU82RyxlQUFlLEdBQUdELFVBQVViLE1BQU0sQ0FBQy9GLE9BQU82RyxlQUFlLEVBQUUsT0FBTyxVQUFVRCxVQUFVWixLQUFLLENBQUMsVUFBVTtZQUUzSCxNQUFNYyxZQUFZekIsT0FBT3FCLGdCQUFnQixDQUFDLGVBQWVoQixLQUFLRDtZQUM5RCxNQUFNckcsV0FBV1ksT0FBTytHLGtCQUFrQixHQUFHRCxVQUFVZixNQUFNLENBQUMvRixPQUFPK0csa0JBQWtCLEVBQUUsT0FBTyxVQUFVRCxVQUFVZCxLQUFLLENBQUMsVUFBVTtZQUVwSSxPQUFPO2dCQUNMZixhQUFhakYsT0FBT2dILFlBQVk7Z0JBQ2hDOUI7Z0JBQ0FDO2dCQUNBL0Y7Z0JBQ0FtRCxRQUFRdkMsT0FBT2lILE9BQU87WUFDeEI7UUFDRixFQUFFLE9BQU92RSxPQUFPO1lBQ2QxRCxRQUFRMEQsS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaEQsT0FBTztRQUNUO0lBQ0Y7SUFFQXdFLG1CQUFtQjNFLE1BQU0sRUFBRTtRQUN6QixNQUFNMUQsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7Ozs7SUFJOUIsQ0FBQztRQUVELE9BQU9NLEtBQUtzRCxHQUFHLENBQUNJO0lBQ2xCO0lBRUE0RSxzQkFBc0I3QixRQUFRLEVBQUU7UUFDOUIsTUFBTXpHLE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQztRQUM3QixPQUFPTSxLQUFLQyxHQUFHLENBQUN3RztJQUNsQjtJQUVBLG1DQUFtQztJQUNuQzhCLG1CQUFtQkMsWUFBWSxFQUFFckcsUUFBUSxFQUFFO1FBQ3pDLE1BQU1uQyxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7OztJQUk5QixDQUFDO1FBRUQsT0FBT00sS0FBS0MsR0FBRyxDQUNidUksY0FDQXJHLFNBQVNzRyxzQkFBc0IsSUFBSSxHQUNuQ3RHLFNBQVN1RyxjQUFjLElBQUksR0FDM0J2RyxTQUFTd0csaUJBQWlCLEdBQUcsSUFBSTtJQUVyQztJQUVBQyxtQkFBbUJKLFlBQVksRUFBRTtRQUMvQixNQUFNeEksT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7O0lBRTlCLENBQUM7UUFFRCxNQUFNeUIsU0FBU25CLEtBQUtMLEdBQUcsQ0FBQzZJO1FBQ3hCLElBQUksQ0FBQ3JILFFBQVE7WUFDWCxzQ0FBc0M7WUFDdEMsT0FBTztnQkFDTHNILHdCQUF3QjtnQkFDeEJDLGdCQUFnQjtnQkFDaEJDLG1CQUFtQjtZQUNyQjtRQUNGO1FBRUEsT0FBTztZQUNMRix3QkFBd0J0SCxPQUFPc0gsc0JBQXNCO1lBQ3JEQyxnQkFBZ0J2SCxPQUFPdUgsY0FBYztZQUNyQ0MsbUJBQW1CRSxRQUFRMUgsT0FBT3dILGlCQUFpQjtRQUNyRDtJQUNGO0lBRUFHLHVCQUF1QnBGLE1BQU0sRUFBRTtRQUM3QixNQUFNMUQsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7Ozs7SUFJOUIsQ0FBQztRQUVELE1BQU15QixTQUFTbkIsS0FBS0wsR0FBRyxDQUFDK0Q7UUFDeEIsSUFBSSxDQUFDdkMsUUFBUTtZQUNYLE9BQU87Z0JBQ0xzSCx3QkFBd0I7Z0JBQ3hCQyxnQkFBZ0I7Z0JBQ2hCQyxtQkFBbUI7WUFDckI7UUFDRjtRQUVBLE9BQU87WUFDTEYsd0JBQXdCdEgsT0FBT3NILHNCQUFzQjtZQUNyREMsZ0JBQWdCdkgsT0FBT3VILGNBQWM7WUFDckNDLG1CQUFtQkUsUUFBUTFILE9BQU93SCxpQkFBaUI7UUFDckQ7SUFDRjtJQUVBLHNCQUFzQjtJQUN0QkksaUJBQWlCckYsTUFBTSxFQUFFc0YsU0FBUyxFQUFFQyxRQUFRLEVBQUVDLGdCQUFnQixJQUFJLEVBQUU7UUFDbEUsTUFBTXpJLGNBQWMsSUFBSSxDQUFDcEIsRUFBRSxDQUFDb0IsV0FBVyxDQUFDO1lBQ3RDLDhCQUE4QjtZQUM5QixNQUFNMEIsV0FBVyxJQUFJLENBQUMyRyxzQkFBc0IsQ0FBQ3BGO1lBRTdDLDZDQUE2QztZQUM3QyxJQUFJdkIsU0FBU3NHLHNCQUFzQixHQUFHLEtBQUtRLFNBQVNFLE1BQU0sR0FBR2hILFNBQVNzRyxzQkFBc0IsRUFBRTtnQkFDNUYsTUFBTSxJQUFJN0gsTUFBTSxDQUFDLFlBQVksRUFBRXFJLFNBQVNFLE1BQU0sQ0FBQyx5QkFBeUIsRUFBRWhILFNBQVNzRyxzQkFBc0IsQ0FBQyxDQUFDLENBQUM7WUFDOUc7WUFFQSw2QkFBNkI7WUFDN0IsSUFBSVMsaUJBQWlCLENBQUMvRyxTQUFTd0csaUJBQWlCLEVBQUU7Z0JBQ2hELE1BQU0sSUFBSS9ILE1BQU07WUFDbEI7WUFFQSxlQUFlO1lBQ2YsTUFBTXdJLFlBQVksSUFBSSxDQUFDL0osRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7O01BR25DLENBQUM7WUFFRCxNQUFNMkosY0FBY0QsVUFBVW5KLEdBQUcsQ0FDL0J5RCxRQUNBc0YsV0FDQUMsU0FBU0UsTUFBTSxFQUNmaEgsU0FBU3VHLGNBQWMsRUFDdkJRO1lBR0YsTUFBTUksVUFBVUQsWUFBWS9ILGVBQWU7WUFFM0MsMENBQTBDO1lBQzFDLE1BQU1pSSxVQUFVLElBQUksQ0FBQ2xLLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7OztNQUdqQyxDQUFDO1lBRUR1SixTQUFTTyxPQUFPLENBQUNDLENBQUFBO2dCQUNmLE1BQU1DLG9CQUFvQixJQUFJLENBQUNDLDBCQUEwQixDQUFDeEgsU0FBU3VHLGNBQWMsRUFBRVE7Z0JBQ25GSyxRQUFRdEosR0FBRyxDQUNUcUosU0FDQTVGLFFBQ0FmLEtBQUtDLFNBQVMsQ0FBQzZHLFVBQ2Z0SCxTQUFTdUcsY0FBYyxFQUN2QmdCLG1CQUNBUjtZQUVKO1lBRUEscUNBQXFDO1lBQ3JDLElBQUlBLGVBQWU7Z0JBQ2pCLElBQUksQ0FBQ1UsbUJBQW1CLENBQUNsRyxRQUFRd0YsZUFBZSxNQUFNSTtZQUN4RDtZQUVBLGVBQWU7WUFDZixJQUFJLENBQUNqRixXQUFXLENBQUNYLFFBQVEsaUJBQWlCLENBQUMsZUFBZSxFQUFFc0YsVUFBVSxNQUFNLEVBQUVDLFNBQVNFLE1BQU0sQ0FBQyxTQUFTLENBQUM7WUFFeEcsT0FBT0c7UUFDVDtRQUVBLE9BQU83STtJQUNUO0lBRUFrSiwyQkFBMkJFLFlBQVksRUFBRVgsYUFBYSxFQUFFO1FBQ3RELElBQUlRLG9CQUFvQkc7UUFFeEIsMkRBQTJEO1FBQzNELElBQUlYLGVBQWU7WUFDakIsTUFBTVksTUFBTSxJQUFJako7WUFDaEIsTUFBTWtKLFlBQVksSUFBSWxKLEtBQUtxSTtZQUMzQixNQUFNYyxXQUFXRCxVQUFVRSxPQUFPLEtBQUtILElBQUlHLE9BQU87WUFDbEQsTUFBTUMsYUFBYUYsV0FBWSxRQUFPLEtBQUssRUFBQztZQUU1QyxJQUFJRSxjQUFjLEdBQUc7Z0JBQ25CUixxQkFBcUIsR0FBRyx5Q0FBeUM7WUFDbkUsT0FBTyxJQUFJUSxjQUFjLEdBQUc7Z0JBQzFCUixxQkFBcUIsR0FBRywyQ0FBMkM7WUFDckU7UUFDRjtRQUVBLDREQUE0RDtRQUM1RCxvREFBb0Q7UUFFcEQsT0FBT1MsS0FBS0MsR0FBRyxDQUFDVixtQkFBbUIsS0FBSywwQkFBMEI7SUFDcEU7SUFFQVcsZ0JBQWdCM0csU0FBUyxJQUFJLEVBQUVJLFNBQVMsSUFBSSxFQUFFVixRQUFRLEVBQUUsRUFBRUMsU0FBUyxDQUFDLEVBQUU7UUFDcEUsSUFBSThCLFFBQVEsQ0FBQzs7Ozs7SUFLYixDQUFDO1FBQ0QsTUFBTUMsU0FBUyxFQUFFO1FBRWpCLElBQUkxQixRQUFRO1lBQ1Z5QixTQUFTO1lBQ1RDLE9BQU9yQyxJQUFJLENBQUNXO1FBQ2Q7UUFFQSxJQUFJSSxRQUFRO1lBQ1ZxQixTQUFTO1lBQ1RDLE9BQU9yQyxJQUFJLENBQUNlO1FBQ2Q7UUFFQXFCLFNBQVM7UUFDVEMsT0FBT3JDLElBQUksQ0FBQ0ssT0FBT0M7UUFFbkIsTUFBTXJELE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQ3lGO1FBQzdCLE9BQU9uRixLQUFLc0QsR0FBRyxJQUFJOEI7SUFDckI7SUFFQWtGLGFBQWFoQixPQUFPLEVBQUU7UUFDcEIsTUFBTXRKLE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7O0lBSTlCLENBQUM7UUFFRCxPQUFPTSxLQUFLc0QsR0FBRyxDQUFDZ0c7SUFDbEI7SUFFQWlCLGtCQUFrQmpCLE9BQU8sRUFBRXhGLE1BQU0sRUFBRTBHLGNBQWMsSUFBSSxFQUFFO1FBQ3JELE1BQU14SyxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7OztJQUk5QixDQUFDO1FBRUQsT0FBT00sS0FBS0MsR0FBRyxDQUFDNkQsUUFBUTBHLGFBQWFsQjtJQUN2QztJQUVBLG9CQUFvQjtJQUNwQm1CLGtCQUFrQjtRQUNoQixNQUFNekssT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7Ozs7Ozs7OztJQVM5QixDQUFDO1FBRUQsT0FBT00sS0FBS0wsR0FBRztJQUNqQjtJQUVBK0ssZ0JBQWdCQyxLQUFLLEVBQUU3RyxNQUFNLEVBQUVDLGVBQWUsSUFBSSxFQUFFNkcsWUFBWSxJQUFJLEVBQUVKLGNBQWMsSUFBSSxFQUFFO1FBQ3hGLE1BQU14SyxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7OztJQUk5QixDQUFDO1FBRUQsT0FBT00sS0FBS0MsR0FBRyxDQUFDNkQsUUFBUUMsY0FBYzZHLFdBQVdKLGFBQWFHO0lBQ2hFO0lBRUFFLGtCQUFrQkYsS0FBSyxFQUFFO1FBQ3ZCLE1BQU0zSyxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7OztJQUk5QixDQUFDO1FBRUQsT0FBT00sS0FBS0MsR0FBRyxDQUFDMEs7SUFDbEI7SUFFQSxxQkFBcUI7SUFDckJmLG9CQUFvQmxHLE1BQU0sRUFBRXdGLGFBQWEsRUFBRXlCLFFBQVEsSUFBSSxFQUFFckIsVUFBVSxJQUFJLEVBQUV3QixrQkFBa0IsRUFBRSxFQUFFO1FBQzdGLHNCQUFzQjtRQUN0QixNQUFNQyxZQUFZLElBQUksQ0FBQ0Msc0JBQXNCLENBQUN0SCxRQUFRd0YsZUFBZTRCO1FBQ3JFLElBQUlDLFVBQVU1QixNQUFNLEdBQUcsR0FBRztZQUN4QixNQUFNLElBQUl2SSxNQUFNLENBQUMsOEJBQThCLEVBQUVzSSxjQUFjLENBQUM7UUFDbEU7UUFFQSxNQUFNbEosT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDLENBQUM7OztJQUc5QixDQUFDO1FBRUQsT0FBT00sS0FBS0MsR0FBRyxDQUFDeUQsUUFBUXdGLGVBQWU0QixpQkFBaUJILE9BQU9yQjtJQUNqRTtJQUVBMEIsdUJBQXVCdEgsTUFBTSxFQUFFd0YsYUFBYSxFQUFFNEIsZUFBZSxFQUFFO1FBQzdELE1BQU1HLFlBQVksSUFBSXBLLEtBQUtxSTtRQUMzQixNQUFNZ0MsVUFBVSxJQUFJckssS0FBS29LLFVBQVVoQixPQUFPLEtBQU1hLGtCQUFrQixLQUFLO1FBRXZFLE1BQU05SyxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7Ozs7Ozs7SUFROUIsQ0FBQztRQUVELE9BQU9NLEtBQUtzRCxHQUFHLENBQUNJLFFBQVF1SCxVQUFVdkksV0FBVyxJQUFJdUksVUFBVXZJLFdBQVcsSUFDdkR3SSxRQUFReEksV0FBVyxJQUFJd0ksUUFBUXhJLFdBQVc7SUFDM0Q7SUFFQXlJLGlCQUFpQnpILE1BQU0sRUFBRTBILFlBQVksSUFBSSxFQUFFQyxVQUFVLElBQUksRUFBRTtRQUN6RCxJQUFJbEcsUUFBUSxDQUFDOzs7Ozs7SUFNYixDQUFDO1FBQ0QsTUFBTUMsU0FBUztZQUFDMUI7U0FBTztRQUV2QixJQUFJMEgsV0FBVztZQUNiakcsU0FBUztZQUNUQyxPQUFPckMsSUFBSSxDQUFDcUk7UUFDZDtRQUVBLElBQUlDLFNBQVM7WUFDWGxHLFNBQVM7WUFDVEMsT0FBT3JDLElBQUksQ0FBQ3NJO1FBQ2Q7UUFFQWxHLFNBQVM7UUFFVCxNQUFNbkYsT0FBTyxJQUFJLENBQUNYLEVBQUUsQ0FBQ0ssT0FBTyxDQUFDeUY7UUFDN0IsT0FBT25GLEtBQUtzRCxHQUFHLElBQUk4QjtJQUNyQjtJQUVBLDhCQUE4QjtJQUM5QmtHLGtCQUFrQlgsS0FBSyxFQUFFWSxXQUFXLEVBQUVDLGdCQUFnQixLQUFLLEVBQUU7UUFDM0QsTUFBTXhMLE9BQU8sSUFBSSxDQUFDWCxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7O0lBSTlCLENBQUM7UUFFRCxNQUFNeUIsU0FBU25CLEtBQUtDLEdBQUcsQ0FBQ3NMLGFBQWFDLGdCQUFnQkQsY0FBYyxNQUFNWjtRQUV6RSxJQUFJYSxlQUFlO1lBQ2pCLElBQUksQ0FBQ25ILFdBQVcsQ0FBQyxNQUFNLDJCQUEyQixDQUFDLElBQUksRUFBRXNHLE1BQU0saUJBQWlCLEVBQUVZLFlBQVksQ0FBQztRQUNqRztRQUVBLE9BQU9wSztJQUNUO0lBRUFzSyw0QkFBNEI7UUFDMUIsMERBQTBEO1FBQzFELE1BQU16TCxPQUFPLElBQUksQ0FBQ1gsRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7Ozs7Ozs7SUFROUIsQ0FBQztRQUVELE9BQU9NLEtBQUtDLEdBQUc7SUFDakI7SUFFQXlMLGdCQUFnQjtRQUNkLE1BQU1wRyxRQUFRLENBQUM7UUFFZix1QkFBdUI7UUFDdkIsTUFBTXFHLGFBQWEsSUFBSSxDQUFDdE0sRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7OztJQUlwQyxDQUFDO1FBQ0Q0RixNQUFNc0csWUFBWSxHQUFHRCxXQUFXckksR0FBRztRQUVuQyx5QkFBeUI7UUFDekIsTUFBTXVJLGVBQWUsSUFBSSxDQUFDeE0sRUFBRSxDQUFDSyxPQUFPLENBQUMsQ0FBQzs7Ozs7O0lBTXRDLENBQUM7UUFDRDRGLE1BQU13RyxjQUFjLEdBQUdELGFBQWF2SSxHQUFHO1FBRXZDLG9CQUFvQjtRQUNwQixNQUFNeUksZUFBZSxJQUFJLENBQUMxTSxFQUFFLENBQUNLLE9BQU8sQ0FBQyxDQUFDOzs7O0lBSXRDLENBQUM7UUFDRDRGLE1BQU0wRyxlQUFlLEdBQUdELGFBQWFwTSxHQUFHLElBQUlzTSxvQkFBb0I7UUFFaEUsT0FBTzNHO0lBQ1Q7SUFFQTRHLFFBQVE7UUFDTixJQUFJLENBQUM3TSxFQUFFLENBQUM2TSxLQUFLO0lBQ2Y7QUFDRjtBQUVBLDRDQUE0QztBQUM1QyxNQUFNQyxLQUFLek4sbUJBQU9BLENBQUM7QUFDbkIsTUFBTTBOLFVBQVV0TixLQUFLSSxJQUFJLENBQUNDLFFBQVFDLEdBQUcsSUFBSTtBQUN6QyxJQUFJLENBQUMrTSxHQUFHRSxVQUFVLENBQUNELFVBQVU7SUFDM0JELEdBQUdHLFNBQVMsQ0FBQ0YsU0FBUztRQUFFRyxXQUFXO0lBQUs7QUFDMUM7QUFFQSw0QkFBNEI7QUFDNUIsSUFBSUMsYUFBYTtBQUVqQixTQUFTQztJQUNQLElBQUksQ0FBQ0QsWUFBWTtRQUNmQSxhQUFhLElBQUl6TjtJQUNuQjtJQUNBLE9BQU95TjtBQUNUO0FBRUFFLE9BQU9DLE9BQU8sR0FBRztJQUFFRjtJQUFhMU47QUFBZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFkZXItYXV0by8uL2xpYi9kYXRhYmFzZS5qcz9lZWE1Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IERhdGFiYXNlID0gcmVxdWlyZSgnYmV0dGVyLXNxbGl0ZTMnKTtcbmNvbnN0IGJjcnlwdCA9IHJlcXVpcmUoJ2JjcnlwdGpzJyk7XG5jb25zdCB7IHY0OiB1dWlkdjQgfSA9IHJlcXVpcmUoJ3V1aWQnKTtcbmNvbnN0IHBhdGggPSByZXF1aXJlKCdwYXRoJyk7XG5cbmNsYXNzIERhdGFiYXNlTWFuYWdlciB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIGNvbnN0IGRiUGF0aCA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCAnZGF0YScsICdhcHAuZGInKTtcbiAgICB0aGlzLmRiID0gbmV3IERhdGFiYXNlKGRiUGF0aCk7XG4gICAgdGhpcy5pbml0aWFsaXplVGFibGVzKCk7XG4gICAgdGhpcy5jcmVhdGVEZWZhdWx0QWRtaW4oKTtcbiAgfVxuXG4gIGluaXRpYWxpemVUYWJsZXMoKSB7XG4gICAgLy8gVXNlcnMgdGFibGVcbiAgICB0aGlzLmRiLmV4ZWMoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgdXNlcnMgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIHVzZXJuYW1lIFRFWFQgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBwYXNzd29yZF9oYXNoIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIHJvbGUgVEVYVCBERUZBVUxUICd1c2VyJyBDSEVDSyhyb2xlIElOICgndXNlcicsICdhZG1pbicpKSxcbiAgICAgICAgbGljZW5zZV9rZXlfaWQgSU5URUdFUixcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgMSxcbiAgICAgICAgbGFzdF9sb2dpbiBEQVRFVElNRSxcbiAgICAgICAgRk9SRUlHTiBLRVkgKGxpY2Vuc2Vfa2V5X2lkKSBSRUZFUkVOQ0VTIGxpY2Vuc2Vfa2V5cyAoaWQpXG4gICAgICApXG4gICAgYCk7XG5cbiAgICAvLyBMaWNlbnNlIGtleXMgdGFibGVcbiAgICB0aGlzLmRiLmV4ZWMoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgbGljZW5zZV9rZXlzIChcbiAgICAgICAgaWQgSU5URUdFUiBQUklNQVJZIEtFWSBBVVRPSU5DUkVNRU5ULFxuICAgICAgICBrZXlfY29kZSBURVhUIFVOSVFVRSBOT1QgTlVMTCxcbiAgICAgICAgZHVyYXRpb25fZGF5cyBJTlRFR0VSIE5PVCBOVUxMLFxuICAgICAgICBtYXhfdXNlcyBJTlRFR0VSIE5PVCBOVUxMIERFRkFVTFQgMSxcbiAgICAgICAgY3VycmVudF91c2VzIElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBjcmVhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIGV4cGlyZXNfYXQgREFURVRJTUUgTk9UIE5VTEwsXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgMSxcbiAgICAgICAgY3JlYXRlZF9ieSBJTlRFR0VSLFxuICAgICAgICBmZWF0dXJlcyBURVhUIERFRkFVTFQgJ1tdJyxcbiAgICAgICAgRk9SRUlHTiBLRVkgKGNyZWF0ZWRfYnkpIFJFRkVSRU5DRVMgdXNlcnMgKGlkKVxuICAgICAgKVxuICAgIGApO1xuXG4gICAgLy8gVXNlciBzZXNzaW9ucyB0YWJsZVxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyB1c2VyX3Nlc3Npb25zIChcbiAgICAgICAgaWQgSU5URUdFUiBQUklNQVJZIEtFWSBBVVRPSU5DUkVNRU5ULFxuICAgICAgICB1c2VyX2lkIElOVEVHRVIgTk9UIE5VTEwsXG4gICAgICAgIHRva2VuX2hhc2ggVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgZXhwaXJlc19hdCBEQVRFVElNRSBOT1QgTlVMTCxcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICBpcF9hZGRyZXNzIFRFWFQsXG4gICAgICAgIHVzZXJfYWdlbnQgVEVYVCxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCAxLFxuICAgICAgICBGT1JFSUdOIEtFWSAodXNlcl9pZCkgUkVGRVJFTkNFUyB1c2VycyAoaWQpXG4gICAgICApXG4gICAgYCk7XG5cbiAgICAvLyBBY3Rpdml0eSBsb2dzIHRhYmxlXG4gICAgdGhpcy5kYi5leGVjKGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIGFjdGl2aXR5X2xvZ3MgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIHVzZXJfaWQgSU5URUdFUixcbiAgICAgICAgYWN0aW9uIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIGRldGFpbHMgVEVYVCxcbiAgICAgICAgaXBfYWRkcmVzcyBURVhULFxuICAgICAgICBjcmVhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIEZPUkVJR04gS0VZICh1c2VyX2lkKSBSRUZFUkVOQ0VTIHVzZXJzIChpZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIFN5c3RlbSBzZXR0aW5ncyB0YWJsZVxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBzeXN0ZW1fc2V0dGluZ3MgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIHNldHRpbmdfa2V5IFRFWFQgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBzZXR0aW5nX3ZhbHVlIFRFWFQsXG4gICAgICAgIHVwZGF0ZWRfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9ieSBJTlRFR0VSLFxuICAgICAgICBGT1JFSUdOIEtFWSAodXBkYXRlZF9ieSkgUkVGRVJFTkNFUyB1c2VycyAoaWQpXG4gICAgICApXG4gICAgYCk7XG5cbiAgICAvLyBFbmNyeXB0ZWQgbG9naW4gY3JlZGVudGlhbHMgdGFibGVcbiAgICB0aGlzLmRiLmV4ZWMoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgZW5jcnlwdGVkX2NyZWRlbnRpYWxzIChcbiAgICAgICAgaWQgSU5URUdFUiBQUklNQVJZIEtFWSBBVVRPSU5DUkVNRU5ULFxuICAgICAgICB1c2VyX2lkIElOVEVHRVIgTk9UIE5VTEwsXG4gICAgICAgIGxvZ2luX2tleSBURVhUIFVOSVFVRSBOT1QgTlVMTCxcbiAgICAgICAgbG9naW5fbWV0aG9kIFRFWFQgTk9UIE5VTEwgQ0hFQ0sobG9naW5fbWV0aG9kIElOICgnbm9ybWFsJywgJ21pY3Jvc29mdCcsICdnb29nbGUnKSksXG4gICAgICAgIGVuY3J5cHRlZF9zY2hvb2wgVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgZW5jcnlwdGVkX2VtYWlsIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIGVuY3J5cHRlZF9wYXNzd29yZCBURVhUIE5PVCBOVUxMLFxuICAgICAgICBlbmNyeXB0aW9uX2l2IFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIGNyZWF0ZWRfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICBpc19hY3RpdmUgQk9PTEVBTiBERUZBVUxUIDEsXG4gICAgICAgIEZPUkVJR04gS0VZICh1c2VyX2lkKSBSRUZFUkVOQ0VTIHVzZXJzIChpZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIExpY2Vuc2UgZmVhdHVyZSBzZXR0aW5ncyB0YWJsZVxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBsaWNlbnNlX2ZlYXR1cmVfc2V0dGluZ3MgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIGxpY2Vuc2Vfa2V5X2lkIElOVEVHRVIgTk9UIE5VTEwsXG4gICAgICAgIG1heF9hY2NvdW50c19wZXJfYmF0Y2ggSU5URUdFUiBERUZBVUxUIDAsXG4gICAgICAgIHByaW9yaXR5X2xldmVsIElOVEVHRVIgREVGQVVMVCAwIENIRUNLKHByaW9yaXR5X2xldmVsID49IDAgQU5EIHByaW9yaXR5X2xldmVsIDw9IDEwKSxcbiAgICAgICAgc2NoZWR1bGluZ19hY2Nlc3MgQk9PTEVBTiBERUZBVUxUIDAsXG4gICAgICAgIGNyZWF0ZWRfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICBGT1JFSUdOIEtFWSAobGljZW5zZV9rZXlfaWQpIFJFRkVSRU5DRVMgbGljZW5zZV9rZXlzIChpZCksXG4gICAgICAgIFVOSVFVRShsaWNlbnNlX2tleV9pZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIFF1ZXVlIGJhdGNoZXMgdGFibGVcbiAgICB0aGlzLmRiLmV4ZWMoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgcXVldWVfYmF0Y2hlcyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgdXNlcl9pZCBJTlRFR0VSIE5PVCBOVUxMLFxuICAgICAgICBiYXRjaF9uYW1lIFRFWFQsXG4gICAgICAgIHRvdGFsX2FjY291bnRzIElOVEVHRVIgTk9UIE5VTEwsXG4gICAgICAgIHByb2Nlc3NlZF9hY2NvdW50cyBJTlRFR0VSIERFRkFVTFQgMCxcbiAgICAgICAgZmFpbGVkX2FjY291bnRzIElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBzdGF0dXMgVEVYVCBERUZBVUxUICdwZW5kaW5nJyBDSEVDSyhzdGF0dXMgSU4gKCdwZW5kaW5nJywgJ3Byb2Nlc3NpbmcnLCAnY29tcGxldGVkJywgJ2ZhaWxlZCcsICdjYW5jZWxsZWQnKSksXG4gICAgICAgIHByaW9yaXR5X2xldmVsIElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBjcmVhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIHN0YXJ0ZWRfYXQgREFURVRJTUUsXG4gICAgICAgIGNvbXBsZXRlZF9hdCBEQVRFVElNRSxcbiAgICAgICAgc2NoZWR1bGVkX3RpbWUgREFURVRJTUUsXG4gICAgICAgIEZPUkVJR04gS0VZICh1c2VyX2lkKSBSRUZFUkVOQ0VTIHVzZXJzIChpZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIFF1ZXVlIGpvYnMgdGFibGVcbiAgICB0aGlzLmRiLmV4ZWMoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgcXVldWVfam9icyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgYmF0Y2hfaWQgSU5URUdFUixcbiAgICAgICAgdXNlcl9pZCBJTlRFR0VSIE5PVCBOVUxMLFxuICAgICAgICBqb2JfdHlwZSBURVhUIE5PVCBOVUxMIERFRkFVTFQgJ3NwYXJ4X3JlYWRlcicsXG4gICAgICAgIGpvYl9kYXRhIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIHN0YXR1cyBURVhUIERFRkFVTFQgJ3F1ZXVlZCcgQ0hFQ0soc3RhdHVzIElOICgncXVldWVkJywgJ3Byb2Nlc3NpbmcnLCAnY29tcGxldGVkJywgJ2ZhaWxlZCcsICdjYW5jZWxsZWQnKSksXG4gICAgICAgIHByaW9yaXR5X2xldmVsIElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBlZmZlY3RpdmVfcHJpb3JpdHkgSU5URUdFUiBERUZBVUxUIDAsXG4gICAgICAgIHNjaGVkdWxlZF90aW1lIERBVEVUSU1FLFxuICAgICAgICBjcmVhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIHN0YXJ0ZWRfYXQgREFURVRJTUUsXG4gICAgICAgIGNvbXBsZXRlZF9hdCBEQVRFVElNRSxcbiAgICAgICAgZXJyb3JfbWVzc2FnZSBURVhULFxuICAgICAgICByZXRyeV9jb3VudCBJTlRFR0VSIERFRkFVTFQgMCxcbiAgICAgICAgbWF4X3JldHJpZXMgSU5URUdFUiBERUZBVUxUIDMsXG4gICAgICAgIEZPUkVJR04gS0VZIChiYXRjaF9pZCkgUkVGRVJFTkNFUyBxdWV1ZV9iYXRjaGVzIChpZCksXG4gICAgICAgIEZPUkVJR04gS0VZICh1c2VyX2lkKSBSRUZFUkVOQ0VTIHVzZXJzIChpZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIFF1ZXVlIHNjaGVkdWxlcyB0YWJsZSBmb3IgY29uZmxpY3QgZGV0ZWN0aW9uXG4gICAgdGhpcy5kYi5leGVjKGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHF1ZXVlX3NjaGVkdWxlcyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgdXNlcl9pZCBJTlRFR0VSIE5PVCBOVUxMLFxuICAgICAgICBzY2hlZHVsZWRfdGltZSBEQVRFVElNRSBOT1QgTlVMTCxcbiAgICAgICAgZHVyYXRpb25fbWludXRlcyBJTlRFR0VSIERFRkFVTFQgMzAsXG4gICAgICAgIGpvYl9pZCBJTlRFR0VSLFxuICAgICAgICBiYXRjaF9pZCBJTlRFR0VSLFxuICAgICAgICBzdGF0dXMgVEVYVCBERUZBVUxUICdzY2hlZHVsZWQnIENIRUNLKHN0YXR1cyBJTiAoJ3NjaGVkdWxlZCcsICdhY3RpdmUnLCAnY29tcGxldGVkJywgJ2NhbmNlbGxlZCcpKSxcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICBGT1JFSUdOIEtFWSAodXNlcl9pZCkgUkVGRVJFTkNFUyB1c2VycyAoaWQpLFxuICAgICAgICBGT1JFSUdOIEtFWSAoam9iX2lkKSBSRUZFUkVOQ0VTIHF1ZXVlX2pvYnMgKGlkKSxcbiAgICAgICAgRk9SRUlHTiBLRVkgKGJhdGNoX2lkKSBSRUZFUkVOQ0VTIHF1ZXVlX2JhdGNoZXMgKGlkKVxuICAgICAgKVxuICAgIGApO1xuXG4gICAgLy8gQ3JlYXRlIGluZGV4ZXMgZm9yIGJldHRlciBwZXJmb3JtYW5jZVxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfdXNlcnNfdXNlcm5hbWUgT04gdXNlcnModXNlcm5hbWUpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X3VzZXJzX2xpY2Vuc2Vfa2V5IE9OIHVzZXJzKGxpY2Vuc2Vfa2V5X2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9saWNlbnNlX2tleXNfY29kZSBPTiBsaWNlbnNlX2tleXMoa2V5X2NvZGUpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X2xpY2Vuc2Vfa2V5c19leHBpcmVzIE9OIGxpY2Vuc2Vfa2V5cyhleHBpcmVzX2F0KTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9zZXNzaW9uc190b2tlbiBPTiB1c2VyX3Nlc3Npb25zKHRva2VuX2hhc2gpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X3Nlc3Npb25zX3VzZXIgT04gdXNlcl9zZXNzaW9ucyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9hY3Rpdml0eV91c2VyIE9OIGFjdGl2aXR5X2xvZ3ModXNlcl9pZCk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfYWN0aXZpdHlfY3JlYXRlZCBPTiBhY3Rpdml0eV9sb2dzKGNyZWF0ZWRfYXQpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X2VuY3J5cHRlZF9jcmVkZW50aWFsc191c2VyIE9OIGVuY3J5cHRlZF9jcmVkZW50aWFscyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9lbmNyeXB0ZWRfY3JlZGVudGlhbHNfa2V5IE9OIGVuY3J5cHRlZF9jcmVkZW50aWFscyhsb2dpbl9rZXkpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X2xpY2Vuc2VfZmVhdHVyZXMgT04gbGljZW5zZV9mZWF0dXJlX3NldHRpbmdzKGxpY2Vuc2Vfa2V5X2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9xdWV1ZV9iYXRjaGVzX3VzZXIgT04gcXVldWVfYmF0Y2hlcyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9xdWV1ZV9iYXRjaGVzX3N0YXR1cyBPTiBxdWV1ZV9iYXRjaGVzKHN0YXR1cyk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfYmF0Y2hlc19zY2hlZHVsZWQgT04gcXVldWVfYmF0Y2hlcyhzY2hlZHVsZWRfdGltZSk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfam9ic19iYXRjaCBPTiBxdWV1ZV9qb2JzKGJhdGNoX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9xdWV1ZV9qb2JzX3VzZXIgT04gcXVldWVfam9icyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9xdWV1ZV9qb2JzX3N0YXR1cyBPTiBxdWV1ZV9qb2JzKHN0YXR1cyk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfam9ic19wcmlvcml0eSBPTiBxdWV1ZV9qb2JzKGVmZmVjdGl2ZV9wcmlvcml0eSk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfam9ic19zY2hlZHVsZWQgT04gcXVldWVfam9icyhzY2hlZHVsZWRfdGltZSk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfc2NoZWR1bGVzX3VzZXIgT04gcXVldWVfc2NoZWR1bGVzKHVzZXJfaWQpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X3F1ZXVlX3NjaGVkdWxlc190aW1lIE9OIHF1ZXVlX3NjaGVkdWxlcyhzY2hlZHVsZWRfdGltZSk7XG4gICAgYCk7XG4gIH1cblxuICBjcmVhdGVEZWZhdWx0QWRtaW4oKSB7XG4gICAgY29uc3QgYWRtaW5FeGlzdHMgPSB0aGlzLmRiLnByZXBhcmUoJ1NFTEVDVCBpZCBGUk9NIHVzZXJzIFdIRVJFIHJvbGUgPSA/IExJTUlUIDEnKS5nZXQoJ2FkbWluJyk7XG4gICAgXG4gICAgaWYgKCFhZG1pbkV4aXN0cykge1xuICAgICAgY29uc3QgaGFzaGVkUGFzc3dvcmQgPSBiY3J5cHQuaGFzaFN5bmMocHJvY2Vzcy5lbnYuREVGQVVMVF9BRE1JTl9QQVNTV09SRCwgMTIpO1xuICAgICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICAgIElOU0VSVCBJTlRPIHVzZXJzICh1c2VybmFtZSwgcGFzc3dvcmRfaGFzaCwgcm9sZSwgaXNfYWN0aXZlKVxuICAgICAgICBWQUxVRVMgKD8sID8sID8sID8pXG4gICAgICBgKTtcbiAgICAgIFxuICAgICAgc3RtdC5ydW4ocHJvY2Vzcy5lbnYuREVGQVVMVF9BRE1JTl9VU0VSTkFNRSwgaGFzaGVkUGFzc3dvcmQsICdhZG1pbicsIDEpO1xuICAgICAgY29uc29sZS5sb2coYERlZmF1bHQgYWRtaW4gdXNlciBjcmVhdGVkOiAke3Byb2Nlc3MuZW52LkRFRkFVTFRfQURNSU5fVVNFUk5BTUV9L1toaWRkZW5dYCk7XG4gICAgfVxuICB9XG5cbiAgLy8gVXNlciBtYW5hZ2VtZW50IG1ldGhvZHNcbiAgY3JlYXRlVXNlcih1c2VybmFtZSwgcGFzc3dvcmQsIGxpY2Vuc2VLZXkpIHtcbiAgICBjb25zdCB0cmFuc2FjdGlvbiA9IHRoaXMuZGIudHJhbnNhY3Rpb24oKCkgPT4ge1xuICAgICAgLy8gVmFsaWRhdGUgbGljZW5zZSBrZXlcbiAgICAgIGNvbnN0IGxpY2Vuc2VTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgICAgU0VMRUNUIGlkLCBtYXhfdXNlcywgY3VycmVudF91c2VzLCBleHBpcmVzX2F0LCBpc19hY3RpdmUgXG4gICAgICAgIEZST00gbGljZW5zZV9rZXlzIFxuICAgICAgICBXSEVSRSBrZXlfY29kZSA9ID8gQU5EIGlzX2FjdGl2ZSA9IDFcbiAgICAgIGApO1xuICAgICAgY29uc3QgbGljZW5zZSA9IGxpY2Vuc2VTdG10LmdldChsaWNlbnNlS2V5KTtcblxuICAgICAgaWYgKCFsaWNlbnNlKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBsaWNlbnNlIGtleScpO1xuICAgICAgfVxuXG4gICAgICBpZiAobmV3IERhdGUobGljZW5zZS5leHBpcmVzX2F0KSA8IG5ldyBEYXRlKCkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdMaWNlbnNlIGtleSBoYXMgZXhwaXJlZCcpO1xuICAgICAgfVxuXG4gICAgICBpZiAobGljZW5zZS5jdXJyZW50X3VzZXMgPj0gbGljZW5zZS5tYXhfdXNlcykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xpY2Vuc2Uga2V5IGhhcyByZWFjaGVkIG1heGltdW0gdXNlcycpO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB1c2VybmFtZSBhbHJlYWR5IGV4aXN0c1xuICAgICAgY29uc3QgdXNlckV4aXN0cyA9IHRoaXMuZGIucHJlcGFyZSgnU0VMRUNUIGlkIEZST00gdXNlcnMgV0hFUkUgdXNlcm5hbWUgPSA/JykuZ2V0KHVzZXJuYW1lKTtcbiAgICAgIGlmICh1c2VyRXhpc3RzKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVXNlcm5hbWUgYWxyZWFkeSBleGlzdHMnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIHVzZXJcbiAgICAgIGNvbnN0IGhhc2hlZFBhc3N3b3JkID0gYmNyeXB0Lmhhc2hTeW5jKHBhc3N3b3JkLCAxMik7XG4gICAgICBjb25zdCB1c2VyU3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICAgIElOU0VSVCBJTlRPIHVzZXJzICh1c2VybmFtZSwgcGFzc3dvcmRfaGFzaCwgbGljZW5zZV9rZXlfaWQsIHJvbGUpXG4gICAgICAgIFZBTFVFUyAoPywgPywgPywgPylcbiAgICAgIGApO1xuICAgICAgXG4gICAgICBjb25zdCByZXN1bHQgPSB1c2VyU3RtdC5ydW4odXNlcm5hbWUsIGhhc2hlZFBhc3N3b3JkLCBsaWNlbnNlLmlkLCAndXNlcicpO1xuXG4gICAgICAvLyBVcGRhdGUgbGljZW5zZSBrZXkgdXNhZ2VcbiAgICAgIGNvbnN0IHVwZGF0ZUxpY2Vuc2VTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgICAgVVBEQVRFIGxpY2Vuc2Vfa2V5cyBcbiAgICAgICAgU0VUIGN1cnJlbnRfdXNlcyA9IGN1cnJlbnRfdXNlcyArIDEgXG4gICAgICAgIFdIRVJFIGlkID0gP1xuICAgICAgYCk7XG4gICAgICB1cGRhdGVMaWNlbnNlU3RtdC5ydW4obGljZW5zZS5pZCk7XG5cbiAgICAgIHJldHVybiByZXN1bHQubGFzdEluc2VydFJvd2lkO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIHRyYW5zYWN0aW9uKCk7XG4gIH1cblxuICBhdXRoZW50aWNhdGVVc2VyKHVzZXJuYW1lLCBwYXNzd29yZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIHUuKiwgbGsuZXhwaXJlc19hdCBhcyBsaWNlbnNlX2V4cGlyZXMsIGxrLmlzX2FjdGl2ZSBhcyBsaWNlbnNlX2FjdGl2ZVxuICAgICAgRlJPTSB1c2VycyB1XG4gICAgICBMRUZUIEpPSU4gbGljZW5zZV9rZXlzIGxrIE9OIHUubGljZW5zZV9rZXlfaWQgPSBsay5pZFxuICAgICAgV0hFUkUgdS51c2VybmFtZSA9ID8gQU5EIHUuaXNfYWN0aXZlID0gMVxuICAgIGApO1xuICAgIFxuICAgIGNvbnN0IHVzZXIgPSBzdG10LmdldCh1c2VybmFtZSk7XG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgY3JlZGVudGlhbHMnKTtcbiAgICB9XG5cbiAgICBjb25zdCBpc1ZhbGlkUGFzc3dvcmQgPSBiY3J5cHQuY29tcGFyZVN5bmMocGFzc3dvcmQsIHVzZXIucGFzc3dvcmRfaGFzaCk7XG4gICAgaWYgKCFpc1ZhbGlkUGFzc3dvcmQpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBjcmVkZW50aWFscycpO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGxpY2Vuc2UgdmFsaWRpdHkgZm9yIG5vbi1hZG1pbiB1c2Vyc1xuICAgIGlmICh1c2VyLnJvbGUgIT09ICdhZG1pbicpIHtcbiAgICAgIGlmICghdXNlci5saWNlbnNlX2FjdGl2ZSB8fCBuZXcgRGF0ZSh1c2VyLmxpY2Vuc2VfZXhwaXJlcykgPCBuZXcgRGF0ZSgpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignTGljZW5zZSBoYXMgZXhwaXJlZCcpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBsYXN0IGxvZ2luXG4gICAgY29uc3QgdXBkYXRlU3RtdCA9IHRoaXMuZGIucHJlcGFyZSgnVVBEQVRFIHVzZXJzIFNFVCBsYXN0X2xvZ2luID0gQ1VSUkVOVF9USU1FU1RBTVAgV0hFUkUgaWQgPSA/Jyk7XG4gICAgdXBkYXRlU3RtdC5ydW4odXNlci5pZCk7XG5cbiAgICAvLyBSZW1vdmUgc2Vuc2l0aXZlIGRhdGFcbiAgICBkZWxldGUgdXNlci5wYXNzd29yZF9oYXNoO1xuICAgIHJldHVybiB1c2VyO1xuICB9XG5cbiAgLy8gTGljZW5zZSBrZXkgbWFuYWdlbWVudCBtZXRob2RzXG4gIGNyZWF0ZUxpY2Vuc2VLZXkoZHVyYXRpb25EYXlzLCBtYXhVc2VzID0gMSwgZmVhdHVyZXMgPSBbXSwgY3JlYXRlZEJ5ID0gbnVsbCkge1xuICAgIGNvbnN0IGtleUNvZGUgPSB0aGlzLmdlbmVyYXRlTGljZW5zZUtleSgpO1xuICAgIGNvbnN0IGV4cGlyZXNBdCA9IG5ldyBEYXRlKCk7XG4gICAgZXhwaXJlc0F0LnNldERhdGUoZXhwaXJlc0F0LmdldERhdGUoKSArIGR1cmF0aW9uRGF5cyk7XG5cbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIElOU0VSVCBJTlRPIGxpY2Vuc2Vfa2V5cyAoa2V5X2NvZGUsIGR1cmF0aW9uX2RheXMsIG1heF91c2VzLCBleHBpcmVzX2F0LCBjcmVhdGVkX2J5LCBmZWF0dXJlcylcbiAgICAgIFZBTFVFUyAoPywgPywgPywgPywgPywgPylcbiAgICBgKTtcblxuICAgIGNvbnN0IHJlc3VsdCA9IHN0bXQucnVuKFxuICAgICAga2V5Q29kZSxcbiAgICAgIGR1cmF0aW9uRGF5cyxcbiAgICAgIG1heFVzZXMsXG4gICAgICBleHBpcmVzQXQudG9JU09TdHJpbmcoKSxcbiAgICAgIGNyZWF0ZWRCeSxcbiAgICAgIEpTT04uc3RyaW5naWZ5KGZlYXR1cmVzKVxuICAgICk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgaWQ6IHJlc3VsdC5sYXN0SW5zZXJ0Um93aWQsXG4gICAgICBrZXlDb2RlLFxuICAgICAgZHVyYXRpb25EYXlzLFxuICAgICAgbWF4VXNlcyxcbiAgICAgIGV4cGlyZXNBdDogZXhwaXJlc0F0LnRvSVNPU3RyaW5nKCksXG4gICAgICBmZWF0dXJlc1xuICAgIH07XG4gIH1cblxuICBnZW5lcmF0ZUxpY2Vuc2VLZXkoKSB7XG4gICAgY29uc3Qgc2VnbWVudHMgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IDQ7IGkrKykge1xuICAgICAgc2VnbWVudHMucHVzaCh1dWlkdjQoKS5yZXBsYWNlKC8tL2csICcnKS5zdWJzdHJpbmcoMCwgOCkudG9VcHBlckNhc2UoKSk7XG4gICAgfVxuICAgIHJldHVybiBgU1JYLSR7c2VnbWVudHMuam9pbignLScpfWA7XG4gIH1cblxuICBnZXRMaWNlbnNlS2V5cyhsaW1pdCA9IDUwLCBvZmZzZXQgPSAwKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgXG4gICAgICAgIGxrLiosXG4gICAgICAgIHUudXNlcm5hbWUgYXMgY3JlYXRlZF9ieV91c2VybmFtZSxcbiAgICAgICAgQ09VTlQodXNlcnMuaWQpIGFzIHVzZXJzX2NvdW50XG4gICAgICBGUk9NIGxpY2Vuc2Vfa2V5cyBsa1xuICAgICAgTEVGVCBKT0lOIHVzZXJzIHUgT04gbGsuY3JlYXRlZF9ieSA9IHUuaWRcbiAgICAgIExFRlQgSk9JTiB1c2VycyBPTiB1c2Vycy5saWNlbnNlX2tleV9pZCA9IGxrLmlkXG4gICAgICBHUk9VUCBCWSBsay5pZFxuICAgICAgT1JERVIgQlkgbGsuY3JlYXRlZF9hdCBERVNDXG4gICAgICBMSU1JVCA/IE9GRlNFVCA/XG4gICAgYCk7XG5cbiAgICByZXR1cm4gc3RtdC5hbGwobGltaXQsIG9mZnNldCk7XG4gIH1cblxuICBkZWFjdGl2YXRlTGljZW5zZUtleShrZXlJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ1VQREFURSBsaWNlbnNlX2tleXMgU0VUIGlzX2FjdGl2ZSA9IDAgV0hFUkUgaWQgPSA/Jyk7XG4gICAgcmV0dXJuIHN0bXQucnVuKGtleUlkKTtcbiAgfVxuXG4gIC8vIEdldCBkZXRhaWxlZCBsaWNlbnNlIGluZm9ybWF0aW9uIGZvciBhIHVzZXJcbiAgZ2V0VXNlckxpY2Vuc2VTdGF0dXModXNlcklkKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgXG4gICAgICAgIHUuaWQgYXMgdXNlcl9pZCxcbiAgICAgICAgdS51c2VybmFtZSxcbiAgICAgICAgbGsuaWQgYXMgbGljZW5zZV9pZCxcbiAgICAgICAgbGsua2V5X2NvZGUsXG4gICAgICAgIGxrLm1heF91c2VzLFxuICAgICAgICBsay5jdXJyZW50X3VzZXMsXG4gICAgICAgIGxrLmV4cGlyZXNfYXQsXG4gICAgICAgIGxrLmlzX2FjdGl2ZSxcbiAgICAgICAgQ0FTRSBcbiAgICAgICAgICBXSEVOIGxrLmV4cGlyZXNfYXQgPD0gZGF0ZXRpbWUoJ25vdycpIFRIRU4gJ2V4cGlyZWQnXG4gICAgICAgICAgV0hFTiBsay5jdXJyZW50X3VzZXMgPj0gbGsubWF4X3VzZXMgVEhFTiAnbWF4ZWRfb3V0J1xuICAgICAgICAgIFdIRU4gbGsuaXNfYWN0aXZlID0gMCBUSEVOICdpbmFjdGl2ZSdcbiAgICAgICAgICBFTFNFICd2YWxpZCdcbiAgICAgICAgRU5EIGFzIGxpY2Vuc2Vfc3RhdHVzXG4gICAgICBGUk9NIHVzZXJzIHVcbiAgICAgIExFRlQgSk9JTiBsaWNlbnNlX2tleXMgbGsgT04gdS5saWNlbnNlX2tleV9pZCA9IGxrLmlkXG4gICAgICBXSEVSRSB1LmlkID0gP1xuICAgIGApO1xuICAgIFxuICAgIHJldHVybiBzdG10LmdldCh1c2VySWQpO1xuICB9XG5cbiAgLy8gVmFsaWRhdGUgYSBsaWNlbnNlIGtleSBmb3IgcmVuZXdhbCAoY2hlY2sgaWYgaXQncyB2YWxpZCBhbmQgaGFzIGF2YWlsYWJsZSB1c2VzKVxuICB2YWxpZGF0ZUxpY2Vuc2VGb3JSZW5ld2FsKGxpY2Vuc2VLZXkpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCBcbiAgICAgICAgaWQsXG4gICAgICAgIGtleV9jb2RlLFxuICAgICAgICBtYXhfdXNlcyxcbiAgICAgICAgY3VycmVudF91c2VzLFxuICAgICAgICBleHBpcmVzX2F0LFxuICAgICAgICBpc19hY3RpdmUsXG4gICAgICAgIENBU0UgXG4gICAgICAgICAgV0hFTiBleHBpcmVzX2F0IDw9IGRhdGV0aW1lKCdub3cnKSBUSEVOICdleHBpcmVkJ1xuICAgICAgICAgIFdIRU4gY3VycmVudF91c2VzID49IG1heF91c2VzIFRIRU4gJ21heGVkX291dCdcbiAgICAgICAgICBXSEVOIGlzX2FjdGl2ZSA9IDAgVEhFTiAnaW5hY3RpdmUnXG4gICAgICAgICAgRUxTRSAndmFsaWQnXG4gICAgICAgIEVORCBhcyBzdGF0dXNcbiAgICAgIEZST00gbGljZW5zZV9rZXlzIFxuICAgICAgV0hFUkUga2V5X2NvZGUgPSA/XG4gICAgYCk7XG4gICAgXG4gICAgY29uc3QgbGljZW5zZSA9IHN0bXQuZ2V0KGxpY2Vuc2VLZXkpO1xuICAgIFxuICAgIGlmICghbGljZW5zZSkge1xuICAgICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCBlcnJvcjogJ0xpY2Vuc2Uga2V5IG5vdCBmb3VuZCcgfTtcbiAgICB9XG4gICAgXG4gICAgaWYgKGxpY2Vuc2Uuc3RhdHVzICE9PSAndmFsaWQnKSB7XG4gICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ0xpY2Vuc2Uga2V5IGlzIG5vdCB2YWxpZCc7XG4gICAgICBzd2l0Y2ggKGxpY2Vuc2Uuc3RhdHVzKSB7XG4gICAgICAgIGNhc2UgJ2V4cGlyZWQnOlxuICAgICAgICAgIGVycm9yTWVzc2FnZSA9ICdMaWNlbnNlIGtleSBoYXMgZXhwaXJlZCc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ21heGVkX291dCc6XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gJ0xpY2Vuc2Uga2V5IGhhcyByZWFjaGVkIG1heGltdW0gdXNlcyc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2luYWN0aXZlJzpcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnTGljZW5zZSBrZXkgaXMgaW5hY3RpdmUnO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICAgIFxuICAgIHJldHVybiB7IHZhbGlkOiB0cnVlLCBsaWNlbnNlIH07XG4gIH1cblxuICAvLyBSZW5ldyB1c2VyJ3MgbGljZW5zZSB3aXRoIGEgbmV3IGxpY2Vuc2Uga2V5XG4gIHJlbmV3VXNlckxpY2Vuc2UodXNlcklkLCBuZXdMaWNlbnNlS2V5KSB7XG4gICAgY29uc3QgdHJhbnNhY3Rpb24gPSB0aGlzLmRiLnRyYW5zYWN0aW9uKCgpID0+IHtcbiAgICAgIC8vIEZpcnN0IHZhbGlkYXRlIHRoZSBuZXcgbGljZW5zZSBrZXlcbiAgICAgIGNvbnN0IHZhbGlkYXRpb24gPSB0aGlzLnZhbGlkYXRlTGljZW5zZUZvclJlbmV3YWwobmV3TGljZW5zZUtleSk7XG4gICAgICBpZiAoIXZhbGlkYXRpb24udmFsaWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHZhbGlkYXRpb24uZXJyb3IpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCBuZXdMaWNlbnNlID0gdmFsaWRhdGlvbi5saWNlbnNlO1xuICAgICAgXG4gICAgICAvLyBVcGRhdGUgdXNlcidzIGxpY2Vuc2Vfa2V5X2lkIHRvIHRoZSBuZXcgbGljZW5zZVxuICAgICAgY29uc3QgdXBkYXRlVXNlclN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgICBVUERBVEUgdXNlcnMgXG4gICAgICAgIFNFVCBsaWNlbnNlX2tleV9pZCA9ID8sIHVwZGF0ZWRfYXQgPSBDVVJSRU5UX1RJTUVTVEFNUCBcbiAgICAgICAgV0hFUkUgaWQgPSA/XG4gICAgICBgKTtcbiAgICAgIHVwZGF0ZVVzZXJTdG10LnJ1bihuZXdMaWNlbnNlLmlkLCB1c2VySWQpO1xuICAgICAgXG4gICAgICAvLyBJbmNyZW1lbnQgdGhlIG5ldyBsaWNlbnNlJ3MgY3VycmVudF91c2VzXG4gICAgICBjb25zdCB1cGRhdGVMaWNlbnNlU3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICAgIFVQREFURSBsaWNlbnNlX2tleXMgXG4gICAgICAgIFNFVCBjdXJyZW50X3VzZXMgPSBjdXJyZW50X3VzZXMgKyAxIFxuICAgICAgICBXSEVSRSBpZCA9ID9cbiAgICAgIGApO1xuICAgICAgdXBkYXRlTGljZW5zZVN0bXQucnVuKG5ld0xpY2Vuc2UuaWQpO1xuICAgICAgXG4gICAgICAvLyBMb2cgdGhlIHJlbmV3YWwgYWN0aXZpdHlcbiAgICAgIHRoaXMubG9nQWN0aXZpdHkodXNlcklkLCAnTElDRU5TRV9SRU5FV0VEJywgYExpY2Vuc2UgcmVuZXdlZCB3aXRoIGtleTogJHtuZXdMaWNlbnNlS2V5fWApO1xuICAgICAgXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBuZXdMaWNlbnNlSWQ6IG5ld0xpY2Vuc2UuaWQsXG4gICAgICAgIG5ld0xpY2Vuc2VLZXk6IG5ld0xpY2Vuc2VLZXksXG4gICAgICAgIGV4cGlyZXNBdDogbmV3TGljZW5zZS5leHBpcmVzX2F0LFxuICAgICAgICBtYXhVc2VzOiBuZXdMaWNlbnNlLm1heF91c2VzLFxuICAgICAgICBjdXJyZW50VXNlczogbmV3TGljZW5zZS5jdXJyZW50X3VzZXMgKyAxXG4gICAgICB9O1xuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiB0cmFuc2FjdGlvbigpO1xuICB9XG5cbiAgLy8gU2Vzc2lvbiBtYW5hZ2VtZW50XG4gIGNyZWF0ZVNlc3Npb24odXNlcklkLCB0b2tlbkhhc2gsIGV4cGlyZXNBdCwgaXBBZGRyZXNzID0gbnVsbCwgdXNlckFnZW50ID0gbnVsbCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgSU5TRVJUIElOVE8gdXNlcl9zZXNzaW9ucyAodXNlcl9pZCwgdG9rZW5faGFzaCwgZXhwaXJlc19hdCwgaXBfYWRkcmVzcywgdXNlcl9hZ2VudClcbiAgICAgIFZBTFVFUyAoPywgPywgPywgPywgPylcbiAgICBgKTtcblxuICAgIHJldHVybiBzdG10LnJ1bih1c2VySWQsIHRva2VuSGFzaCwgZXhwaXJlc0F0LCBpcEFkZHJlc3MsIHVzZXJBZ2VudCk7XG4gIH1cblxuICB2YWxpZGF0ZVNlc3Npb24odG9rZW5IYXNoKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1Qgcy4qLCB1LnVzZXJuYW1lLCB1LnJvbGUsIHUuaXNfYWN0aXZlIGFzIHVzZXJfYWN0aXZlXG4gICAgICBGUk9NIHVzZXJfc2Vzc2lvbnMgc1xuICAgICAgSk9JTiB1c2VycyB1IE9OIHMudXNlcl9pZCA9IHUuaWRcbiAgICAgIFdIRVJFIHMudG9rZW5faGFzaCA9ID8gQU5EIHMuaXNfYWN0aXZlID0gMSBBTkQgcy5leHBpcmVzX2F0ID4gZGF0ZXRpbWUoJ25vdycpXG4gICAgYCk7XG5cbiAgICByZXR1cm4gc3RtdC5nZXQodG9rZW5IYXNoKTtcbiAgfVxuXG4gIGludmFsaWRhdGVTZXNzaW9uKHRva2VuSGFzaCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ1VQREFURSB1c2VyX3Nlc3Npb25zIFNFVCBpc19hY3RpdmUgPSAwIFdIRVJFIHRva2VuX2hhc2ggPSA/Jyk7XG4gICAgcmV0dXJuIHN0bXQucnVuKHRva2VuSGFzaCk7XG4gIH1cblxuICBpbnZhbGlkYXRlQWxsVXNlclNlc3Npb25zKHVzZXJJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ1VQREFURSB1c2VyX3Nlc3Npb25zIFNFVCBpc19hY3RpdmUgPSAwIFdIRVJFIHVzZXJfaWQgPSA/Jyk7XG4gICAgcmV0dXJuIHN0bXQucnVuKHVzZXJJZCk7XG4gIH1cblxuICAvLyBBY3Rpdml0eSBsb2dnaW5nXG4gIGxvZ0FjdGl2aXR5KHVzZXJJZCwgYWN0aW9uLCBkZXRhaWxzID0gbnVsbCwgaXBBZGRyZXNzID0gbnVsbCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgSU5TRVJUIElOVE8gYWN0aXZpdHlfbG9ncyAodXNlcl9pZCwgYWN0aW9uLCBkZXRhaWxzLCBpcF9hZGRyZXNzKVxuICAgICAgVkFMVUVTICg/LCA/LCA/LCA/KVxuICAgIGApO1xuXG4gICAgcmV0dXJuIHN0bXQucnVuKHVzZXJJZCwgYWN0aW9uLCBkZXRhaWxzLCBpcEFkZHJlc3MpO1xuICB9XG5cbiAgZ2V0QWN0aXZpdHlMb2dzKHVzZXJJZCA9IG51bGwsIGxpbWl0ID0gMTAwLCBvZmZzZXQgPSAwKSB7XG4gICAgbGV0IHF1ZXJ5ID0gYFxuICAgICAgU0VMRUNUIFxuICAgICAgICBhbC4qLFxuICAgICAgICB1LnVzZXJuYW1lXG4gICAgICBGUk9NIGFjdGl2aXR5X2xvZ3MgYWxcbiAgICAgIExFRlQgSk9JTiB1c2VycyB1IE9OIGFsLnVzZXJfaWQgPSB1LmlkXG4gICAgYDtcbiAgICBcbiAgICBjb25zdCBwYXJhbXMgPSBbXTtcbiAgICBcbiAgICBpZiAodXNlcklkKSB7XG4gICAgICBxdWVyeSArPSAnIFdIRVJFIGFsLnVzZXJfaWQgPSA/JztcbiAgICAgIHBhcmFtcy5wdXNoKHVzZXJJZCk7XG4gICAgfVxuICAgIFxuICAgIHF1ZXJ5ICs9ICcgT1JERVIgQlkgYWwuY3JlYXRlZF9hdCBERVNDIExJTUlUID8gT0ZGU0VUID8nO1xuICAgIHBhcmFtcy5wdXNoKGxpbWl0LCBvZmZzZXQpO1xuXG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShxdWVyeSk7XG4gICAgcmV0dXJuIHN0bXQuYWxsKC4uLnBhcmFtcyk7XG4gIH1cblxuICAvLyBBbmFseXRpY3MgbWV0aG9kc1xuICBnZXRTeXN0ZW1TdGF0cygpIHtcbiAgICBjb25zdCBzdGF0cyA9IHt9O1xuXG4gICAgLy8gVG90YWwgdXNlcnNcbiAgICBzdGF0cy50b3RhbFVzZXJzID0gdGhpcy5kYi5wcmVwYXJlKCdTRUxFQ1QgQ09VTlQoKikgYXMgY291bnQgRlJPTSB1c2VycyBXSEVSRSByb2xlID0gXFwndXNlclxcJycpLmdldCgpLmNvdW50O1xuICAgIFxuICAgIC8vIEFjdGl2ZSB1c2VycyAobG9nZ2VkIGluIHdpdGhpbiBsYXN0IDMwIGRheXMpXG4gICAgc3RhdHMuYWN0aXZlVXNlcnMgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIENPVU5UKCopIGFzIGNvdW50IEZST00gdXNlcnMgXG4gICAgICBXSEVSRSByb2xlID0gJ3VzZXInIEFORCBsYXN0X2xvZ2luID4gZGF0ZXRpbWUoJ25vdycsICctMzAgZGF5cycpXG4gICAgYCkuZ2V0KCkuY291bnQ7XG5cbiAgICAvLyBUb3RhbCBsaWNlbnNlIGtleXNcbiAgICBzdGF0cy50b3RhbExpY2Vuc2VLZXlzID0gdGhpcy5kYi5wcmVwYXJlKCdTRUxFQ1QgQ09VTlQoKikgYXMgY291bnQgRlJPTSBsaWNlbnNlX2tleXMnKS5nZXQoKS5jb3VudDtcbiAgICBcbiAgICAvLyBBY3RpdmUgbGljZW5zZSBrZXlzXG4gICAgc3RhdHMuYWN0aXZlTGljZW5zZUtleXMgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIENPVU5UKCopIGFzIGNvdW50IEZST00gbGljZW5zZV9rZXlzIFxuICAgICAgV0hFUkUgaXNfYWN0aXZlID0gMSBBTkQgZXhwaXJlc19hdCA+IGRhdGV0aW1lKCdub3cnKVxuICAgIGApLmdldCgpLmNvdW50O1xuXG4gICAgLy8gRXhwaXJlZCBsaWNlbnNlIGtleXNcbiAgICBzdGF0cy5leHBpcmVkTGljZW5zZUtleXMgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIENPVU5UKCopIGFzIGNvdW50IEZST00gbGljZW5zZV9rZXlzIFxuICAgICAgV0hFUkUgZXhwaXJlc19hdCA8PSBkYXRldGltZSgnbm93JylcbiAgICBgKS5nZXQoKS5jb3VudDtcblxuICAgIC8vIFJlY2VudCBhY3Rpdml0eSAobGFzdCAyNCBob3VycylcbiAgICBzdGF0cy5yZWNlbnRBY3Rpdml0eSA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgQ09VTlQoKikgYXMgY291bnQgRlJPTSBhY3Rpdml0eV9sb2dzIFxuICAgICAgV0hFUkUgY3JlYXRlZF9hdCA+IGRhdGV0aW1lKCdub3cnLCAnLTEgZGF5JylcbiAgICBgKS5nZXQoKS5jb3VudDtcblxuICAgIHJldHVybiBzdGF0cztcbiAgfVxuXG4gIC8vIFVzZXIgbWFuYWdlbWVudCBmb3IgYWRtaW5cbiAgZ2V0VXNlcnMobGltaXQgPSA1MCwgb2Zmc2V0ID0gMCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIFxuICAgICAgICB1LmlkLFxuICAgICAgICB1LnVzZXJuYW1lLFxuICAgICAgICB1LnJvbGUsXG4gICAgICAgIHUuY3JlYXRlZF9hdCxcbiAgICAgICAgdS5sYXN0X2xvZ2luLFxuICAgICAgICB1LmlzX2FjdGl2ZSxcbiAgICAgICAgbGsua2V5X2NvZGUsXG4gICAgICAgIGxrLmV4cGlyZXNfYXQgYXMgbGljZW5zZV9leHBpcmVzXG4gICAgICBGUk9NIHVzZXJzIHVcbiAgICAgIExFRlQgSk9JTiBsaWNlbnNlX2tleXMgbGsgT04gdS5saWNlbnNlX2tleV9pZCA9IGxrLmlkXG4gICAgICBPUkRFUiBCWSB1LmNyZWF0ZWRfYXQgREVTQ1xuICAgICAgTElNSVQgPyBPRkZTRVQgP1xuICAgIGApO1xuXG4gICAgcmV0dXJuIHN0bXQuYWxsKGxpbWl0LCBvZmZzZXQpO1xuICB9XG5cbiAgdG9nZ2xlVXNlclN0YXR1cyh1c2VySWQpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKCdVUERBVEUgdXNlcnMgU0VUIGlzX2FjdGl2ZSA9IE5PVCBpc19hY3RpdmUgV0hFUkUgaWQgPSA/Jyk7XG4gICAgcmV0dXJuIHN0bXQucnVuKHVzZXJJZCk7XG4gIH1cblxuICAvLyBDbGVhbnVwIG1ldGhvZHNcbiAgY2xlYW51cEV4cGlyZWRTZXNzaW9ucygpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKCdERUxFVEUgRlJPTSB1c2VyX3Nlc3Npb25zIFdIRVJFIGV4cGlyZXNfYXQgPD0gZGF0ZXRpbWUoXCJub3dcIiknKTtcbiAgICByZXR1cm4gc3RtdC5ydW4oKTtcbiAgfVxuXG4gIGNsZWFudXBPbGRMb2dzKGRheXNUb0tlZXAgPSA5MCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgREVMRVRFIEZST00gYWN0aXZpdHlfbG9ncyBcbiAgICAgIFdIRVJFIGNyZWF0ZWRfYXQgPD0gZGF0ZXRpbWUoJ25vdycsICctJHtkYXlzVG9LZWVwfSBkYXlzJylcbiAgICBgKTtcbiAgICByZXR1cm4gc3RtdC5ydW4oKTtcbiAgfVxuXG4gIC8vIEVuY3J5cHRlZCBjcmVkZW50aWFscyBtZXRob2RzXG4gIHNhdmVFbmNyeXB0ZWRDcmVkZW50aWFscyh1c2VySWQsIGxvZ2luTWV0aG9kLCBzY2hvb2wsIGVtYWlsLCBwYXNzd29yZCwgZW5jcnlwdGlvbktleSkge1xuICAgIGNvbnN0IGNyeXB0byA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuICAgIFxuICAgIC8vIEdlbmVyYXRlIGEgdW5pcXVlIGxvZ2luIGtleVxuICAgIGNvbnN0IGxvZ2luS2V5ID0gJ1NMSy0nICsgY3J5cHRvLnJhbmRvbUJ5dGVzKDgpLnRvU3RyaW5nKCdoZXgnKS50b1VwcGVyQ2FzZSgpO1xuICAgIFxuICAgIC8vIENyZWF0ZSBlbmNyeXB0aW9uIElWXG4gICAgY29uc3QgaXYgPSBjcnlwdG8ucmFuZG9tQnl0ZXMoMTYpO1xuICAgIGNvbnN0IGtleSA9IGNyeXB0by5zY3J5cHRTeW5jKGVuY3J5cHRpb25LZXksICdzYWx0JywgMzIpO1xuICAgIFxuICAgIC8vIEVuY3J5cHQgc2Nob29sLCBlbWFpbCBhbmQgcGFzc3dvcmRcbiAgICBjb25zdCBjaXBoZXIxID0gY3J5cHRvLmNyZWF0ZUNpcGhlcml2KCdhZXMtMjU2LWNiYycsIGtleSwgaXYpO1xuICAgIGNvbnN0IGVuY3J5cHRlZFNjaG9vbCA9IGNpcGhlcjEudXBkYXRlKHNjaG9vbCwgJ3V0ZjgnLCAnaGV4JykgKyBjaXBoZXIxLmZpbmFsKCdoZXgnKTtcbiAgICBcbiAgICBjb25zdCBjaXBoZXIyID0gY3J5cHRvLmNyZWF0ZUNpcGhlcml2KCdhZXMtMjU2LWNiYycsIGtleSwgaXYpO1xuICAgIGNvbnN0IGVuY3J5cHRlZEVtYWlsID0gY2lwaGVyMi51cGRhdGUoZW1haWwsICd1dGY4JywgJ2hleCcpICsgY2lwaGVyMi5maW5hbCgnaGV4Jyk7XG4gICAgXG4gICAgY29uc3QgY2lwaGVyMyA9IGNyeXB0by5jcmVhdGVDaXBoZXJpdignYWVzLTI1Ni1jYmMnLCBrZXksIGl2KTtcbiAgICBjb25zdCBlbmNyeXB0ZWRQYXNzd29yZCA9IGNpcGhlcjMudXBkYXRlKHBhc3N3b3JkLCAndXRmOCcsICdoZXgnKSArIGNpcGhlcjMuZmluYWwoJ2hleCcpO1xuICAgIFxuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgSU5TRVJUIElOVE8gZW5jcnlwdGVkX2NyZWRlbnRpYWxzICh1c2VyX2lkLCBsb2dpbl9rZXksIGxvZ2luX21ldGhvZCwgZW5jcnlwdGVkX3NjaG9vbCwgZW5jcnlwdGVkX2VtYWlsLCBlbmNyeXB0ZWRfcGFzc3dvcmQsIGVuY3J5cHRpb25faXYpXG4gICAgICBWQUxVRVMgKD8sID8sID8sID8sID8sID8sID8pXG4gICAgYCk7XG4gICAgXG4gICAgc3RtdC5ydW4odXNlcklkLCBsb2dpbktleSwgbG9naW5NZXRob2QsIGVuY3J5cHRlZFNjaG9vbCwgZW5jcnlwdGVkRW1haWwsIGVuY3J5cHRlZFBhc3N3b3JkLCBpdi50b1N0cmluZygnaGV4JykpO1xuICAgIFxuICAgIHJldHVybiBsb2dpbktleTtcbiAgfVxuXG4gIGdldEVuY3J5cHRlZENyZWRlbnRpYWxzKGxvZ2luS2V5LCBlbmNyeXB0aW9uS2V5KSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgKiBGUk9NIGVuY3J5cHRlZF9jcmVkZW50aWFscyBcbiAgICAgIFdIRVJFIGxvZ2luX2tleSA9ID8gQU5EIGlzX2FjdGl2ZSA9IDFcbiAgICBgKTtcbiAgICBcbiAgICBjb25zdCByZXN1bHQgPSBzdG10LmdldChsb2dpbktleSk7XG4gICAgaWYgKCFyZXN1bHQpIHJldHVybiBudWxsO1xuICAgIFxuICAgIGNvbnN0IGNyeXB0byA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICBjb25zdCBrZXkgPSBjcnlwdG8uc2NyeXB0U3luYyhlbmNyeXB0aW9uS2V5LCAnc2FsdCcsIDMyKTtcbiAgICAgIGNvbnN0IGl2ID0gQnVmZmVyLmZyb20ocmVzdWx0LmVuY3J5cHRpb25faXYsICdoZXgnKTtcblxuICAgICAgLy8gRGVjcnlwdCBzY2hvb2wsIGVtYWlsIGFuZCBwYXNzd29yZFxuICAgICAgY29uc3QgZGVjaXBoZXIxID0gY3J5cHRvLmNyZWF0ZURlY2lwaGVyaXYoJ2Flcy0yNTYtY2JjJywga2V5LCBpdik7XG4gICAgICBjb25zdCBzY2hvb2wgPSByZXN1bHQuZW5jcnlwdGVkX3NjaG9vbCA/IGRlY2lwaGVyMS51cGRhdGUocmVzdWx0LmVuY3J5cHRlZF9zY2hvb2wsICdoZXgnLCAndXRmOCcpICsgZGVjaXBoZXIxLmZpbmFsKCd1dGY4JykgOiBudWxsO1xuICAgICAgXG4gICAgICBjb25zdCBkZWNpcGhlcjIgPSBjcnlwdG8uY3JlYXRlRGVjaXBoZXJpdignYWVzLTI1Ni1jYmMnLCBrZXksIGl2KTtcbiAgICAgIGNvbnN0IGVtYWlsID0gcmVzdWx0LmVuY3J5cHRlZF9lbWFpbCA/IGRlY2lwaGVyMi51cGRhdGUocmVzdWx0LmVuY3J5cHRlZF9lbWFpbCwgJ2hleCcsICd1dGY4JykgKyBkZWNpcGhlcjIuZmluYWwoJ3V0ZjgnKSA6IG51bGw7XG4gICAgICBcbiAgICAgIGNvbnN0IGRlY2lwaGVyMyA9IGNyeXB0by5jcmVhdGVEZWNpcGhlcml2KCdhZXMtMjU2LWNiYycsIGtleSwgaXYpO1xuICAgICAgY29uc3QgcGFzc3dvcmQgPSByZXN1bHQuZW5jcnlwdGVkX3Bhc3N3b3JkID8gZGVjaXBoZXIzLnVwZGF0ZShyZXN1bHQuZW5jcnlwdGVkX3Bhc3N3b3JkLCAnaGV4JywgJ3V0ZjgnKSArIGRlY2lwaGVyMy5maW5hbCgndXRmOCcpIDogbnVsbDtcbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbG9naW5NZXRob2Q6IHJlc3VsdC5sb2dpbl9tZXRob2QsXG4gICAgICAgIHNjaG9vbCxcbiAgICAgICAgZW1haWwsXG4gICAgICAgIHBhc3N3b3JkLFxuICAgICAgICB1c2VySWQ6IHJlc3VsdC51c2VyX2lkXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZGVjcnlwdCBjcmVkZW50aWFsczonLCBlcnJvcik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH1cblxuICBnZXRVc2VyQ3JlZGVudGlhbHModXNlcklkKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgbG9naW5fa2V5LCBsb2dpbl9tZXRob2QsIGNyZWF0ZWRfYXQgRlJPTSBlbmNyeXB0ZWRfY3JlZGVudGlhbHMgXG4gICAgICBXSEVSRSB1c2VyX2lkID0gPyBBTkQgaXNfYWN0aXZlID0gMVxuICAgICAgT1JERVIgQlkgY3JlYXRlZF9hdCBERVNDXG4gICAgYCk7XG4gICAgXG4gICAgcmV0dXJuIHN0bXQuYWxsKHVzZXJJZCk7XG4gIH1cblxuICBkZWFjdGl2YXRlQ3JlZGVudGlhbHMobG9naW5LZXkpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKCdVUERBVEUgZW5jcnlwdGVkX2NyZWRlbnRpYWxzIFNFVCBpc19hY3RpdmUgPSAwIFdIRVJFIGxvZ2luX2tleSA9ID8nKTtcbiAgICByZXR1cm4gc3RtdC5ydW4obG9naW5LZXkpO1xuICB9XG5cbiAgLy8gTGljZW5zZSBGZWF0dXJlIFNldHRpbmdzIE1ldGhvZHNcbiAgc2V0TGljZW5zZUZlYXR1cmVzKGxpY2Vuc2VLZXlJZCwgZmVhdHVyZXMpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIElOU0VSVCBPUiBSRVBMQUNFIElOVE8gbGljZW5zZV9mZWF0dXJlX3NldHRpbmdzIFxuICAgICAgKGxpY2Vuc2Vfa2V5X2lkLCBtYXhfYWNjb3VudHNfcGVyX2JhdGNoLCBwcmlvcml0eV9sZXZlbCwgc2NoZWR1bGluZ19hY2Nlc3MsIHVwZGF0ZWRfYXQpXG4gICAgICBWQUxVRVMgKD8sID8sID8sID8sIENVUlJFTlRfVElNRVNUQU1QKVxuICAgIGApO1xuICAgIFxuICAgIHJldHVybiBzdG10LnJ1bihcbiAgICAgIGxpY2Vuc2VLZXlJZCxcbiAgICAgIGZlYXR1cmVzLm1heF9hY2NvdW50c19wZXJfYmF0Y2ggfHwgMCxcbiAgICAgIGZlYXR1cmVzLnByaW9yaXR5X2xldmVsIHx8IDAsXG4gICAgICBmZWF0dXJlcy5zY2hlZHVsaW5nX2FjY2VzcyA/IDEgOiAwXG4gICAgKTtcbiAgfVxuXG4gIGdldExpY2Vuc2VGZWF0dXJlcyhsaWNlbnNlS2V5SWQpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCAqIEZST00gbGljZW5zZV9mZWF0dXJlX3NldHRpbmdzIFdIRVJFIGxpY2Vuc2Vfa2V5X2lkID0gP1xuICAgIGApO1xuICAgIFxuICAgIGNvbnN0IHJlc3VsdCA9IHN0bXQuZ2V0KGxpY2Vuc2VLZXlJZCk7XG4gICAgaWYgKCFyZXN1bHQpIHtcbiAgICAgIC8vIFJldHVybiBkZWZhdWx0IGZlYXR1cmVzIGlmIG5vbmUgc2V0XG4gICAgICByZXR1cm4ge1xuICAgICAgICBtYXhfYWNjb3VudHNfcGVyX2JhdGNoOiAwLFxuICAgICAgICBwcmlvcml0eV9sZXZlbDogMCxcbiAgICAgICAgc2NoZWR1bGluZ19hY2Nlc3M6IGZhbHNlXG4gICAgICB9O1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgbWF4X2FjY291bnRzX3Blcl9iYXRjaDogcmVzdWx0Lm1heF9hY2NvdW50c19wZXJfYmF0Y2gsXG4gICAgICBwcmlvcml0eV9sZXZlbDogcmVzdWx0LnByaW9yaXR5X2xldmVsLFxuICAgICAgc2NoZWR1bGluZ19hY2Nlc3M6IEJvb2xlYW4ocmVzdWx0LnNjaGVkdWxpbmdfYWNjZXNzKVxuICAgIH07XG4gIH1cblxuICBnZXRVc2VyTGljZW5zZUZlYXR1cmVzKHVzZXJJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIGxmcy4qIEZST00gbGljZW5zZV9mZWF0dXJlX3NldHRpbmdzIGxmc1xuICAgICAgSk9JTiB1c2VycyB1IE9OIHUubGljZW5zZV9rZXlfaWQgPSBsZnMubGljZW5zZV9rZXlfaWRcbiAgICAgIFdIRVJFIHUuaWQgPSA/XG4gICAgYCk7XG4gICAgXG4gICAgY29uc3QgcmVzdWx0ID0gc3RtdC5nZXQodXNlcklkKTtcbiAgICBpZiAoIXJlc3VsdCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbWF4X2FjY291bnRzX3Blcl9iYXRjaDogMCxcbiAgICAgICAgcHJpb3JpdHlfbGV2ZWw6IDAsXG4gICAgICAgIHNjaGVkdWxpbmdfYWNjZXNzOiBmYWxzZVxuICAgICAgfTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIG1heF9hY2NvdW50c19wZXJfYmF0Y2g6IHJlc3VsdC5tYXhfYWNjb3VudHNfcGVyX2JhdGNoLFxuICAgICAgcHJpb3JpdHlfbGV2ZWw6IHJlc3VsdC5wcmlvcml0eV9sZXZlbCxcbiAgICAgIHNjaGVkdWxpbmdfYWNjZXNzOiBCb29sZWFuKHJlc3VsdC5zY2hlZHVsaW5nX2FjY2VzcylcbiAgICB9O1xuICB9XG5cbiAgLy8gUXVldWUgQmF0Y2ggTWV0aG9kc1xuICBjcmVhdGVRdWV1ZUJhdGNoKHVzZXJJZCwgYmF0Y2hOYW1lLCBhY2NvdW50cywgc2NoZWR1bGVkVGltZSA9IG51bGwpIHtcbiAgICBjb25zdCB0cmFuc2FjdGlvbiA9IHRoaXMuZGIudHJhbnNhY3Rpb24oKCkgPT4ge1xuICAgICAgLy8gR2V0IHVzZXIncyBsaWNlbnNlIGZlYXR1cmVzXG4gICAgICBjb25zdCBmZWF0dXJlcyA9IHRoaXMuZ2V0VXNlckxpY2Vuc2VGZWF0dXJlcyh1c2VySWQpO1xuICAgICAgXG4gICAgICAvLyBWYWxpZGF0ZSBiYXRjaCBzaXplIGFnYWluc3QgbGljZW5zZSBsaW1pdHNcbiAgICAgIGlmIChmZWF0dXJlcy5tYXhfYWNjb3VudHNfcGVyX2JhdGNoID4gMCAmJiBhY2NvdW50cy5sZW5ndGggPiBmZWF0dXJlcy5tYXhfYWNjb3VudHNfcGVyX2JhdGNoKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgQmF0Y2ggc2l6ZSAoJHthY2NvdW50cy5sZW5ndGh9KSBleGNlZWRzIGxpY2Vuc2UgbGltaXQgKCR7ZmVhdHVyZXMubWF4X2FjY291bnRzX3Blcl9iYXRjaH0pYCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIFZhbGlkYXRlIHNjaGVkdWxpbmcgYWNjZXNzXG4gICAgICBpZiAoc2NoZWR1bGVkVGltZSAmJiAhZmVhdHVyZXMuc2NoZWR1bGluZ19hY2Nlc3MpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTY2hlZHVsaW5nIGFjY2VzcyBub3QgYXZhaWxhYmxlIGZvciB0aGlzIGxpY2Vuc2UnKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gQ3JlYXRlIGJhdGNoXG4gICAgICBjb25zdCBiYXRjaFN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgICBJTlNFUlQgSU5UTyBxdWV1ZV9iYXRjaGVzICh1c2VyX2lkLCBiYXRjaF9uYW1lLCB0b3RhbF9hY2NvdW50cywgcHJpb3JpdHlfbGV2ZWwsIHNjaGVkdWxlZF90aW1lKVxuICAgICAgICBWQUxVRVMgKD8sID8sID8sID8sID8pXG4gICAgICBgKTtcbiAgICAgIFxuICAgICAgY29uc3QgYmF0Y2hSZXN1bHQgPSBiYXRjaFN0bXQucnVuKFxuICAgICAgICB1c2VySWQsXG4gICAgICAgIGJhdGNoTmFtZSxcbiAgICAgICAgYWNjb3VudHMubGVuZ3RoLFxuICAgICAgICBmZWF0dXJlcy5wcmlvcml0eV9sZXZlbCxcbiAgICAgICAgc2NoZWR1bGVkVGltZVxuICAgICAgKTtcbiAgICAgIFxuICAgICAgY29uc3QgYmF0Y2hJZCA9IGJhdGNoUmVzdWx0Lmxhc3RJbnNlcnRSb3dpZDtcbiAgICAgIFxuICAgICAgLy8gQ3JlYXRlIGluZGl2aWR1YWwgam9icyBmb3IgZWFjaCBhY2NvdW50XG4gICAgICBjb25zdCBqb2JTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgICAgSU5TRVJUIElOVE8gcXVldWVfam9icyAoYmF0Y2hfaWQsIHVzZXJfaWQsIGpvYl9kYXRhLCBwcmlvcml0eV9sZXZlbCwgZWZmZWN0aXZlX3ByaW9yaXR5LCBzY2hlZHVsZWRfdGltZSlcbiAgICAgICAgVkFMVUVTICg/LCA/LCA/LCA/LCA/LCA/KVxuICAgICAgYCk7XG4gICAgICBcbiAgICAgIGFjY291bnRzLmZvckVhY2goYWNjb3VudCA9PiB7XG4gICAgICAgIGNvbnN0IGVmZmVjdGl2ZVByaW9yaXR5ID0gdGhpcy5jYWxjdWxhdGVFZmZlY3RpdmVQcmlvcml0eShmZWF0dXJlcy5wcmlvcml0eV9sZXZlbCwgc2NoZWR1bGVkVGltZSk7XG4gICAgICAgIGpvYlN0bXQucnVuKFxuICAgICAgICAgIGJhdGNoSWQsXG4gICAgICAgICAgdXNlcklkLFxuICAgICAgICAgIEpTT04uc3RyaW5naWZ5KGFjY291bnQpLFxuICAgICAgICAgIGZlYXR1cmVzLnByaW9yaXR5X2xldmVsLFxuICAgICAgICAgIGVmZmVjdGl2ZVByaW9yaXR5LFxuICAgICAgICAgIHNjaGVkdWxlZFRpbWVcbiAgICAgICAgKTtcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICAvLyBDcmVhdGUgc2NoZWR1bGUgZW50cnkgaWYgc2NoZWR1bGVkXG4gICAgICBpZiAoc2NoZWR1bGVkVGltZSkge1xuICAgICAgICB0aGlzLmNyZWF0ZVNjaGVkdWxlRW50cnkodXNlcklkLCBzY2hlZHVsZWRUaW1lLCBudWxsLCBiYXRjaElkKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gTG9nIGFjdGl2aXR5XG4gICAgICB0aGlzLmxvZ0FjdGl2aXR5KHVzZXJJZCwgJ0JBVENIX0NSRUFURUQnLCBgQ3JlYXRlZCBiYXRjaDogJHtiYXRjaE5hbWV9IHdpdGggJHthY2NvdW50cy5sZW5ndGh9IGFjY291bnRzYCk7XG4gICAgICBcbiAgICAgIHJldHVybiBiYXRjaElkO1xuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiB0cmFuc2FjdGlvbigpO1xuICB9XG5cbiAgY2FsY3VsYXRlRWZmZWN0aXZlUHJpb3JpdHkoYmFzZVByaW9yaXR5LCBzY2hlZHVsZWRUaW1lKSB7XG4gICAgbGV0IGVmZmVjdGl2ZVByaW9yaXR5ID0gYmFzZVByaW9yaXR5O1xuICAgIFxuICAgIC8vIEJvb3N0IHByaW9yaXR5IGZvciBzY2hlZHVsZWQgam9icyBhcHByb2FjaGluZyB0aGVpciB0aW1lXG4gICAgaWYgKHNjaGVkdWxlZFRpbWUpIHtcbiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgICBjb25zdCBzY2hlZHVsZWQgPSBuZXcgRGF0ZShzY2hlZHVsZWRUaW1lKTtcbiAgICAgIGNvbnN0IHRpbWVEaWZmID0gc2NoZWR1bGVkLmdldFRpbWUoKSAtIG5vdy5nZXRUaW1lKCk7XG4gICAgICBjb25zdCBob3Vyc1VudGlsID0gdGltZURpZmYgLyAoMTAwMCAqIDYwICogNjApO1xuICAgICAgXG4gICAgICBpZiAoaG91cnNVbnRpbCA8PSAxKSB7XG4gICAgICAgIGVmZmVjdGl2ZVByaW9yaXR5ICs9IDU7IC8vIEhpZ2ggYm9vc3QgZm9yIGpvYnMgZHVlIHdpdGhpbiBhbiBob3VyXG4gICAgICB9IGVsc2UgaWYgKGhvdXJzVW50aWwgPD0gNikge1xuICAgICAgICBlZmZlY3RpdmVQcmlvcml0eSArPSAyOyAvLyBNZWRpdW0gYm9vc3QgZm9yIGpvYnMgZHVlIHdpdGhpbiA2IGhvdXJzXG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIC8vIEFwcGx5IHN0YXJ2YXRpb24gcHJldmVudGlvbiAoYm9vc3QgcHJpb3JpdHkgZm9yIG9sZCBqb2JzKVxuICAgIC8vIFRoaXMgd291bGQgYmUgaW1wbGVtZW50ZWQgaW4gYSBiYWNrZ3JvdW5kIHByb2Nlc3NcbiAgICBcbiAgICByZXR1cm4gTWF0aC5taW4oZWZmZWN0aXZlUHJpb3JpdHksIDEwKTsgLy8gQ2FwIGF0IG1heGltdW0gcHJpb3JpdHlcbiAgfVxuXG4gIGdldFF1ZXVlQmF0Y2hlcyh1c2VySWQgPSBudWxsLCBzdGF0dXMgPSBudWxsLCBsaW1pdCA9IDUwLCBvZmZzZXQgPSAwKSB7XG4gICAgbGV0IHF1ZXJ5ID0gYFxuICAgICAgU0VMRUNUIHFiLiosIHUudXNlcm5hbWVcbiAgICAgIEZST00gcXVldWVfYmF0Y2hlcyBxYlxuICAgICAgSk9JTiB1c2VycyB1IE9OIHFiLnVzZXJfaWQgPSB1LmlkXG4gICAgICBXSEVSRSAxPTFcbiAgICBgO1xuICAgIGNvbnN0IHBhcmFtcyA9IFtdO1xuICAgIFxuICAgIGlmICh1c2VySWQpIHtcbiAgICAgIHF1ZXJ5ICs9ICcgQU5EIHFiLnVzZXJfaWQgPSA/JztcbiAgICAgIHBhcmFtcy5wdXNoKHVzZXJJZCk7XG4gICAgfVxuICAgIFxuICAgIGlmIChzdGF0dXMpIHtcbiAgICAgIHF1ZXJ5ICs9ICcgQU5EIHFiLnN0YXR1cyA9ID8nO1xuICAgICAgcGFyYW1zLnB1c2goc3RhdHVzKTtcbiAgICB9XG4gICAgXG4gICAgcXVlcnkgKz0gJyBPUkRFUiBCWSBxYi5jcmVhdGVkX2F0IERFU0MgTElNSVQgPyBPRkZTRVQgPyc7XG4gICAgcGFyYW1zLnB1c2gobGltaXQsIG9mZnNldCk7XG4gICAgXG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShxdWVyeSk7XG4gICAgcmV0dXJuIHN0bXQuYWxsKC4uLnBhcmFtcyk7XG4gIH1cblxuICBnZXRCYXRjaEpvYnMoYmF0Y2hJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUICogRlJPTSBxdWV1ZV9qb2JzIFxuICAgICAgV0hFUkUgYmF0Y2hfaWQgPSA/IFxuICAgICAgT1JERVIgQlkgZWZmZWN0aXZlX3ByaW9yaXR5IERFU0MsIGNyZWF0ZWRfYXQgQVNDXG4gICAgYCk7XG4gICAgXG4gICAgcmV0dXJuIHN0bXQuYWxsKGJhdGNoSWQpO1xuICB9XG5cbiAgdXBkYXRlQmF0Y2hTdGF0dXMoYmF0Y2hJZCwgc3RhdHVzLCBjb21wbGV0ZWRBdCA9IG51bGwpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFVQREFURSBxdWV1ZV9iYXRjaGVzIFxuICAgICAgU0VUIHN0YXR1cyA9ID8sIGNvbXBsZXRlZF9hdCA9ID8sIHVwZGF0ZWRfYXQgPSBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgV0hFUkUgaWQgPSA/XG4gICAgYCk7XG4gICAgXG4gICAgcmV0dXJuIHN0bXQucnVuKHN0YXR1cywgY29tcGxldGVkQXQsIGJhdGNoSWQpO1xuICB9XG5cbiAgLy8gUXVldWUgSm9iIE1ldGhvZHNcbiAgZ2V0TmV4dFF1ZXVlSm9iKCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIHFqLiosIHFiLmJhdGNoX25hbWUsIHUudXNlcm5hbWVcbiAgICAgIEZST00gcXVldWVfam9icyBxalxuICAgICAgTEVGVCBKT0lOIHF1ZXVlX2JhdGNoZXMgcWIgT04gcWouYmF0Y2hfaWQgPSBxYi5pZFxuICAgICAgSk9JTiB1c2VycyB1IE9OIHFqLnVzZXJfaWQgPSB1LmlkXG4gICAgICBXSEVSRSBxai5zdGF0dXMgPSAncXVldWVkJyBcbiAgICAgIEFORCAocWouc2NoZWR1bGVkX3RpbWUgSVMgTlVMTCBPUiBxai5zY2hlZHVsZWRfdGltZSA8PSBkYXRldGltZSgnbm93JykpXG4gICAgICBPUkRFUiBCWSBxai5lZmZlY3RpdmVfcHJpb3JpdHkgREVTQywgcWouY3JlYXRlZF9hdCBBU0NcbiAgICAgIExJTUlUIDFcbiAgICBgKTtcbiAgICBcbiAgICByZXR1cm4gc3RtdC5nZXQoKTtcbiAgfVxuXG4gIHVwZGF0ZUpvYlN0YXR1cyhqb2JJZCwgc3RhdHVzLCBlcnJvck1lc3NhZ2UgPSBudWxsLCBzdGFydGVkQXQgPSBudWxsLCBjb21wbGV0ZWRBdCA9IG51bGwpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFVQREFURSBxdWV1ZV9qb2JzIFxuICAgICAgU0VUIHN0YXR1cyA9ID8sIGVycm9yX21lc3NhZ2UgPSA/LCBzdGFydGVkX2F0ID0gPywgY29tcGxldGVkX2F0ID0gP1xuICAgICAgV0hFUkUgaWQgPSA/XG4gICAgYCk7XG4gICAgXG4gICAgcmV0dXJuIHN0bXQucnVuKHN0YXR1cywgZXJyb3JNZXNzYWdlLCBzdGFydGVkQXQsIGNvbXBsZXRlZEF0LCBqb2JJZCk7XG4gIH1cblxuICBpbmNyZW1lbnRKb2JSZXRyeShqb2JJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgVVBEQVRFIHF1ZXVlX2pvYnMgXG4gICAgICBTRVQgcmV0cnlfY291bnQgPSByZXRyeV9jb3VudCArIDEsIHN0YXR1cyA9ICdxdWV1ZWQnXG4gICAgICBXSEVSRSBpZCA9ID8gQU5EIHJldHJ5X2NvdW50IDwgbWF4X3JldHJpZXNcbiAgICBgKTtcbiAgICBcbiAgICByZXR1cm4gc3RtdC5ydW4oam9iSWQpO1xuICB9XG5cbiAgLy8gU2NoZWR1bGluZyBNZXRob2RzXG4gIGNyZWF0ZVNjaGVkdWxlRW50cnkodXNlcklkLCBzY2hlZHVsZWRUaW1lLCBqb2JJZCA9IG51bGwsIGJhdGNoSWQgPSBudWxsLCBkdXJhdGlvbk1pbnV0ZXMgPSAzMCkge1xuICAgIC8vIENoZWNrIGZvciBjb25mbGljdHNcbiAgICBjb25zdCBjb25mbGljdHMgPSB0aGlzLmNoZWNrU2NoZWR1bGVDb25mbGljdHModXNlcklkLCBzY2hlZHVsZWRUaW1lLCBkdXJhdGlvbk1pbnV0ZXMpO1xuICAgIGlmIChjb25mbGljdHMubGVuZ3RoID4gMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBTY2hlZHVsZSBjb25mbGljdCBkZXRlY3RlZCBhdCAke3NjaGVkdWxlZFRpbWV9YCk7XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgSU5TRVJUIElOVE8gcXVldWVfc2NoZWR1bGVzICh1c2VyX2lkLCBzY2hlZHVsZWRfdGltZSwgZHVyYXRpb25fbWludXRlcywgam9iX2lkLCBiYXRjaF9pZClcbiAgICAgIFZBTFVFUyAoPywgPywgPywgPywgPylcbiAgICBgKTtcbiAgICBcbiAgICByZXR1cm4gc3RtdC5ydW4odXNlcklkLCBzY2hlZHVsZWRUaW1lLCBkdXJhdGlvbk1pbnV0ZXMsIGpvYklkLCBiYXRjaElkKTtcbiAgfVxuXG4gIGNoZWNrU2NoZWR1bGVDb25mbGljdHModXNlcklkLCBzY2hlZHVsZWRUaW1lLCBkdXJhdGlvbk1pbnV0ZXMpIHtcbiAgICBjb25zdCBzdGFydFRpbWUgPSBuZXcgRGF0ZShzY2hlZHVsZWRUaW1lKTtcbiAgICBjb25zdCBlbmRUaW1lID0gbmV3IERhdGUoc3RhcnRUaW1lLmdldFRpbWUoKSArIChkdXJhdGlvbk1pbnV0ZXMgKiA2MCAqIDEwMDApKTtcbiAgICBcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCAqIEZST00gcXVldWVfc2NoZWR1bGVzXG4gICAgICBXSEVSRSB1c2VyX2lkID0gPyBcbiAgICAgIEFORCBzdGF0dXMgSU4gKCdzY2hlZHVsZWQnLCAnYWN0aXZlJylcbiAgICAgIEFORCAoXG4gICAgICAgIChzY2hlZHVsZWRfdGltZSA8PSA/IEFORCBkYXRldGltZShzY2hlZHVsZWRfdGltZSwgJysnIHx8IGR1cmF0aW9uX21pbnV0ZXMgfHwgJyBtaW51dGVzJykgPiA/KSBPUlxuICAgICAgICAoc2NoZWR1bGVkX3RpbWUgPCA/IEFORCBkYXRldGltZShzY2hlZHVsZWRfdGltZSwgJysnIHx8IGR1cmF0aW9uX21pbnV0ZXMgfHwgJyBtaW51dGVzJykgPj0gPylcbiAgICAgIClcbiAgICBgKTtcbiAgICBcbiAgICByZXR1cm4gc3RtdC5hbGwodXNlcklkLCBzdGFydFRpbWUudG9JU09TdHJpbmcoKSwgc3RhcnRUaW1lLnRvSVNPU3RyaW5nKCksIFxuICAgICAgICAgICAgICAgICAgIGVuZFRpbWUudG9JU09TdHJpbmcoKSwgZW5kVGltZS50b0lTT1N0cmluZygpKTtcbiAgfVxuXG4gIGdldFVzZXJTY2hlZHVsZXModXNlcklkLCBzdGFydERhdGUgPSBudWxsLCBlbmREYXRlID0gbnVsbCkge1xuICAgIGxldCBxdWVyeSA9IGBcbiAgICAgIFNFTEVDVCBxcy4qLCBxai5qb2JfdHlwZSwgcWIuYmF0Y2hfbmFtZVxuICAgICAgRlJPTSBxdWV1ZV9zY2hlZHVsZXMgcXNcbiAgICAgIExFRlQgSk9JTiBxdWV1ZV9qb2JzIHFqIE9OIHFzLmpvYl9pZCA9IHFqLmlkXG4gICAgICBMRUZUIEpPSU4gcXVldWVfYmF0Y2hlcyBxYiBPTiBxcy5iYXRjaF9pZCA9IHFiLmlkXG4gICAgICBXSEVSRSBxcy51c2VyX2lkID0gP1xuICAgIGA7XG4gICAgY29uc3QgcGFyYW1zID0gW3VzZXJJZF07XG4gICAgXG4gICAgaWYgKHN0YXJ0RGF0ZSkge1xuICAgICAgcXVlcnkgKz0gJyBBTkQgcXMuc2NoZWR1bGVkX3RpbWUgPj0gPyc7XG4gICAgICBwYXJhbXMucHVzaChzdGFydERhdGUpO1xuICAgIH1cbiAgICBcbiAgICBpZiAoZW5kRGF0ZSkge1xuICAgICAgcXVlcnkgKz0gJyBBTkQgcXMuc2NoZWR1bGVkX3RpbWUgPD0gPyc7XG4gICAgICBwYXJhbXMucHVzaChlbmREYXRlKTtcbiAgICB9XG4gICAgXG4gICAgcXVlcnkgKz0gJyBPUkRFUiBCWSBxcy5zY2hlZHVsZWRfdGltZSBBU0MnO1xuICAgIFxuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUocXVlcnkpO1xuICAgIHJldHVybiBzdG10LmFsbCguLi5wYXJhbXMpO1xuICB9XG5cbiAgLy8gUHJpb3JpdHkgTWFuYWdlbWVudCBNZXRob2RzXG4gIHVwZGF0ZUpvYlByaW9yaXR5KGpvYklkLCBuZXdQcmlvcml0eSwgYWRtaW5PdmVycmlkZSA9IGZhbHNlKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBVUERBVEUgcXVldWVfam9icyBcbiAgICAgIFNFVCBlZmZlY3RpdmVfcHJpb3JpdHkgPSA/LCBwcmlvcml0eV9sZXZlbCA9ID9cbiAgICAgIFdIRVJFIGlkID0gP1xuICAgIGApO1xuICAgIFxuICAgIGNvbnN0IHJlc3VsdCA9IHN0bXQucnVuKG5ld1ByaW9yaXR5LCBhZG1pbk92ZXJyaWRlID8gbmV3UHJpb3JpdHkgOiBudWxsLCBqb2JJZCk7XG4gICAgXG4gICAgaWYgKGFkbWluT3ZlcnJpZGUpIHtcbiAgICAgIHRoaXMubG9nQWN0aXZpdHkobnVsbCwgJ0FETUlOX1BSSU9SSVRZX09WRVJSSURFJywgYEpvYiAke2pvYklkfSBwcmlvcml0eSBzZXQgdG8gJHtuZXdQcmlvcml0eX1gKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuXG4gIGFwcGx5U3RhcnZhdGlvblByZXZlbnRpb24oKSB7XG4gICAgLy8gQm9vc3QgcHJpb3JpdHkgZm9yIGpvYnMgdGhhdCBoYXZlIGJlZW4gd2FpdGluZyB0b28gbG9uZ1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgVVBEQVRFIHF1ZXVlX2pvYnMgXG4gICAgICBTRVQgZWZmZWN0aXZlX3ByaW9yaXR5ID0gQ0FTRSBcbiAgICAgICAgV0hFTiBkYXRldGltZSgnbm93JykgPiBkYXRldGltZShjcmVhdGVkX2F0LCAnKzIgaG91cnMnKSBUSEVOIE1JTihlZmZlY3RpdmVfcHJpb3JpdHkgKyAyLCAxMClcbiAgICAgICAgV0hFTiBkYXRldGltZSgnbm93JykgPiBkYXRldGltZShjcmVhdGVkX2F0LCAnKzEgaG91cicpIFRIRU4gTUlOKGVmZmVjdGl2ZV9wcmlvcml0eSArIDEsIDEwKVxuICAgICAgICBFTFNFIGVmZmVjdGl2ZV9wcmlvcml0eVxuICAgICAgRU5EXG4gICAgICBXSEVSRSBzdGF0dXMgPSAncXVldWVkJ1xuICAgIGApO1xuICAgIFxuICAgIHJldHVybiBzdG10LnJ1bigpO1xuICB9XG5cbiAgZ2V0UXVldWVTdGF0cygpIHtcbiAgICBjb25zdCBzdGF0cyA9IHt9O1xuICAgIFxuICAgIC8vIFRvdGFsIGpvYnMgYnkgc3RhdHVzXG4gICAgY29uc3Qgc3RhdHVzU3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1Qgc3RhdHVzLCBDT1VOVCgqKSBhcyBjb3VudCBcbiAgICAgIEZST00gcXVldWVfam9icyBcbiAgICAgIEdST1VQIEJZIHN0YXR1c1xuICAgIGApO1xuICAgIHN0YXRzLmpvYnNCeVN0YXR1cyA9IHN0YXR1c1N0bXQuYWxsKCk7XG4gICAgXG4gICAgLy8gSm9icyBieSBwcmlvcml0eSBsZXZlbFxuICAgIGNvbnN0IHByaW9yaXR5U3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgZWZmZWN0aXZlX3ByaW9yaXR5LCBDT1VOVCgqKSBhcyBjb3VudCBcbiAgICAgIEZST00gcXVldWVfam9icyBcbiAgICAgIFdIRVJFIHN0YXR1cyA9ICdxdWV1ZWQnXG4gICAgICBHUk9VUCBCWSBlZmZlY3RpdmVfcHJpb3JpdHlcbiAgICAgIE9SREVSIEJZIGVmZmVjdGl2ZV9wcmlvcml0eSBERVNDXG4gICAgYCk7XG4gICAgc3RhdHMuam9ic0J5UHJpb3JpdHkgPSBwcmlvcml0eVN0bXQuYWxsKCk7XG4gICAgXG4gICAgLy8gQXZlcmFnZSB3YWl0IHRpbWVcbiAgICBjb25zdCB3YWl0VGltZVN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIEFWRyhqdWxpYW5kYXkoc3RhcnRlZF9hdCkgLSBqdWxpYW5kYXkoY3JlYXRlZF9hdCkpICogMjQgKiA2MCBhcyBhdmdfd2FpdF9taW51dGVzXG4gICAgICBGUk9NIHF1ZXVlX2pvYnMgXG4gICAgICBXSEVSRSBzdGFydGVkX2F0IElTIE5PVCBOVUxMXG4gICAgYCk7XG4gICAgc3RhdHMuYXZlcmFnZVdhaXRUaW1lID0gd2FpdFRpbWVTdG10LmdldCgpPy5hdmdfd2FpdF9taW51dGVzIHx8IDA7XG4gICAgXG4gICAgcmV0dXJuIHN0YXRzO1xuICB9XG5cbiAgY2xvc2UoKSB7XG4gICAgdGhpcy5kYi5jbG9zZSgpO1xuICB9XG59XG5cbi8vIENyZWF0ZSBkYXRhIGRpcmVjdG9yeSBpZiBpdCBkb2Vzbid0IGV4aXN0XG5jb25zdCBmcyA9IHJlcXVpcmUoJ2ZzJyk7XG5jb25zdCBkYXRhRGlyID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdkYXRhJyk7XG5pZiAoIWZzLmV4aXN0c1N5bmMoZGF0YURpcikpIHtcbiAgZnMubWtkaXJTeW5jKGRhdGFEaXIsIHsgcmVjdXJzaXZlOiB0cnVlIH0pO1xufVxuXG4vLyBFeHBvcnQgc2luZ2xldG9uIGluc3RhbmNlXG5sZXQgZGJJbnN0YW5jZSA9IG51bGw7XG5cbmZ1bmN0aW9uIGdldERhdGFiYXNlKCkge1xuICBpZiAoIWRiSW5zdGFuY2UpIHtcbiAgICBkYkluc3RhbmNlID0gbmV3IERhdGFiYXNlTWFuYWdlcigpO1xuICB9XG4gIHJldHVybiBkYkluc3RhbmNlO1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHsgZ2V0RGF0YWJhc2UsIERhdGFiYXNlTWFuYWdlciB9O1xuIl0sIm5hbWVzIjpbIkRhdGFiYXNlIiwicmVxdWlyZSIsImJjcnlwdCIsInY0IiwidXVpZHY0IiwicGF0aCIsIkRhdGFiYXNlTWFuYWdlciIsImNvbnN0cnVjdG9yIiwiZGJQYXRoIiwiam9pbiIsInByb2Nlc3MiLCJjd2QiLCJkYiIsImluaXRpYWxpemVUYWJsZXMiLCJjcmVhdGVEZWZhdWx0QWRtaW4iLCJleGVjIiwiYWRtaW5FeGlzdHMiLCJwcmVwYXJlIiwiZ2V0IiwiaGFzaGVkUGFzc3dvcmQiLCJoYXNoU3luYyIsImVudiIsIkRFRkFVTFRfQURNSU5fUEFTU1dPUkQiLCJzdG10IiwicnVuIiwiREVGQVVMVF9BRE1JTl9VU0VSTkFNRSIsImNvbnNvbGUiLCJsb2ciLCJjcmVhdGVVc2VyIiwidXNlcm5hbWUiLCJwYXNzd29yZCIsImxpY2Vuc2VLZXkiLCJ0cmFuc2FjdGlvbiIsImxpY2Vuc2VTdG10IiwibGljZW5zZSIsIkVycm9yIiwiRGF0ZSIsImV4cGlyZXNfYXQiLCJjdXJyZW50X3VzZXMiLCJtYXhfdXNlcyIsInVzZXJFeGlzdHMiLCJ1c2VyU3RtdCIsInJlc3VsdCIsImlkIiwidXBkYXRlTGljZW5zZVN0bXQiLCJsYXN0SW5zZXJ0Um93aWQiLCJhdXRoZW50aWNhdGVVc2VyIiwidXNlciIsImlzVmFsaWRQYXNzd29yZCIsImNvbXBhcmVTeW5jIiwicGFzc3dvcmRfaGFzaCIsInJvbGUiLCJsaWNlbnNlX2FjdGl2ZSIsImxpY2Vuc2VfZXhwaXJlcyIsInVwZGF0ZVN0bXQiLCJjcmVhdGVMaWNlbnNlS2V5IiwiZHVyYXRpb25EYXlzIiwibWF4VXNlcyIsImZlYXR1cmVzIiwiY3JlYXRlZEJ5Iiwia2V5Q29kZSIsImdlbmVyYXRlTGljZW5zZUtleSIsImV4cGlyZXNBdCIsInNldERhdGUiLCJnZXREYXRlIiwidG9JU09TdHJpbmciLCJKU09OIiwic3RyaW5naWZ5Iiwic2VnbWVudHMiLCJpIiwicHVzaCIsInJlcGxhY2UiLCJzdWJzdHJpbmciLCJ0b1VwcGVyQ2FzZSIsImdldExpY2Vuc2VLZXlzIiwibGltaXQiLCJvZmZzZXQiLCJhbGwiLCJkZWFjdGl2YXRlTGljZW5zZUtleSIsImtleUlkIiwiZ2V0VXNlckxpY2Vuc2VTdGF0dXMiLCJ1c2VySWQiLCJ2YWxpZGF0ZUxpY2Vuc2VGb3JSZW5ld2FsIiwidmFsaWQiLCJlcnJvciIsInN0YXR1cyIsImVycm9yTWVzc2FnZSIsInJlbmV3VXNlckxpY2Vuc2UiLCJuZXdMaWNlbnNlS2V5IiwidmFsaWRhdGlvbiIsIm5ld0xpY2Vuc2UiLCJ1cGRhdGVVc2VyU3RtdCIsImxvZ0FjdGl2aXR5Iiwic3VjY2VzcyIsIm5ld0xpY2Vuc2VJZCIsImN1cnJlbnRVc2VzIiwiY3JlYXRlU2Vzc2lvbiIsInRva2VuSGFzaCIsImlwQWRkcmVzcyIsInVzZXJBZ2VudCIsInZhbGlkYXRlU2Vzc2lvbiIsImludmFsaWRhdGVTZXNzaW9uIiwiaW52YWxpZGF0ZUFsbFVzZXJTZXNzaW9ucyIsImFjdGlvbiIsImRldGFpbHMiLCJnZXRBY3Rpdml0eUxvZ3MiLCJxdWVyeSIsInBhcmFtcyIsImdldFN5c3RlbVN0YXRzIiwic3RhdHMiLCJ0b3RhbFVzZXJzIiwiY291bnQiLCJhY3RpdmVVc2VycyIsInRvdGFsTGljZW5zZUtleXMiLCJhY3RpdmVMaWNlbnNlS2V5cyIsImV4cGlyZWRMaWNlbnNlS2V5cyIsInJlY2VudEFjdGl2aXR5IiwiZ2V0VXNlcnMiLCJ0b2dnbGVVc2VyU3RhdHVzIiwiY2xlYW51cEV4cGlyZWRTZXNzaW9ucyIsImNsZWFudXBPbGRMb2dzIiwiZGF5c1RvS2VlcCIsInNhdmVFbmNyeXB0ZWRDcmVkZW50aWFscyIsImxvZ2luTWV0aG9kIiwic2Nob29sIiwiZW1haWwiLCJlbmNyeXB0aW9uS2V5IiwiY3J5cHRvIiwibG9naW5LZXkiLCJyYW5kb21CeXRlcyIsInRvU3RyaW5nIiwiaXYiLCJrZXkiLCJzY3J5cHRTeW5jIiwiY2lwaGVyMSIsImNyZWF0ZUNpcGhlcml2IiwiZW5jcnlwdGVkU2Nob29sIiwidXBkYXRlIiwiZmluYWwiLCJjaXBoZXIyIiwiZW5jcnlwdGVkRW1haWwiLCJjaXBoZXIzIiwiZW5jcnlwdGVkUGFzc3dvcmQiLCJnZXRFbmNyeXB0ZWRDcmVkZW50aWFscyIsIkJ1ZmZlciIsImZyb20iLCJlbmNyeXB0aW9uX2l2IiwiZGVjaXBoZXIxIiwiY3JlYXRlRGVjaXBoZXJpdiIsImVuY3J5cHRlZF9zY2hvb2wiLCJkZWNpcGhlcjIiLCJlbmNyeXB0ZWRfZW1haWwiLCJkZWNpcGhlcjMiLCJlbmNyeXB0ZWRfcGFzc3dvcmQiLCJsb2dpbl9tZXRob2QiLCJ1c2VyX2lkIiwiZ2V0VXNlckNyZWRlbnRpYWxzIiwiZGVhY3RpdmF0ZUNyZWRlbnRpYWxzIiwic2V0TGljZW5zZUZlYXR1cmVzIiwibGljZW5zZUtleUlkIiwibWF4X2FjY291bnRzX3Blcl9iYXRjaCIsInByaW9yaXR5X2xldmVsIiwic2NoZWR1bGluZ19hY2Nlc3MiLCJnZXRMaWNlbnNlRmVhdHVyZXMiLCJCb29sZWFuIiwiZ2V0VXNlckxpY2Vuc2VGZWF0dXJlcyIsImNyZWF0ZVF1ZXVlQmF0Y2giLCJiYXRjaE5hbWUiLCJhY2NvdW50cyIsInNjaGVkdWxlZFRpbWUiLCJsZW5ndGgiLCJiYXRjaFN0bXQiLCJiYXRjaFJlc3VsdCIsImJhdGNoSWQiLCJqb2JTdG10IiwiZm9yRWFjaCIsImFjY291bnQiLCJlZmZlY3RpdmVQcmlvcml0eSIsImNhbGN1bGF0ZUVmZmVjdGl2ZVByaW9yaXR5IiwiY3JlYXRlU2NoZWR1bGVFbnRyeSIsImJhc2VQcmlvcml0eSIsIm5vdyIsInNjaGVkdWxlZCIsInRpbWVEaWZmIiwiZ2V0VGltZSIsImhvdXJzVW50aWwiLCJNYXRoIiwibWluIiwiZ2V0UXVldWVCYXRjaGVzIiwiZ2V0QmF0Y2hKb2JzIiwidXBkYXRlQmF0Y2hTdGF0dXMiLCJjb21wbGV0ZWRBdCIsImdldE5leHRRdWV1ZUpvYiIsInVwZGF0ZUpvYlN0YXR1cyIsImpvYklkIiwic3RhcnRlZEF0IiwiaW5jcmVtZW50Sm9iUmV0cnkiLCJkdXJhdGlvbk1pbnV0ZXMiLCJjb25mbGljdHMiLCJjaGVja1NjaGVkdWxlQ29uZmxpY3RzIiwic3RhcnRUaW1lIiwiZW5kVGltZSIsImdldFVzZXJTY2hlZHVsZXMiLCJzdGFydERhdGUiLCJlbmREYXRlIiwidXBkYXRlSm9iUHJpb3JpdHkiLCJuZXdQcmlvcml0eSIsImFkbWluT3ZlcnJpZGUiLCJhcHBseVN0YXJ2YXRpb25QcmV2ZW50aW9uIiwiZ2V0UXVldWVTdGF0cyIsInN0YXR1c1N0bXQiLCJqb2JzQnlTdGF0dXMiLCJwcmlvcml0eVN0bXQiLCJqb2JzQnlQcmlvcml0eSIsIndhaXRUaW1lU3RtdCIsImF2ZXJhZ2VXYWl0VGltZSIsImF2Z193YWl0X21pbnV0ZXMiLCJjbG9zZSIsImZzIiwiZGF0YURpciIsImV4aXN0c1N5bmMiLCJta2RpclN5bmMiLCJyZWN1cnNpdmUiLCJkYkluc3RhbmNlIiwiZ2V0RGF0YWJhc2UiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.js\n");

/***/ }),

/***/ "(rsc)/./lib/queueMiddleware.js":
/*!********************************!*\
  !*** ./lib/queueMiddleware.js ***!
  \********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst { getWebhookManager } = __webpack_require__(/*! ./webhook */ \"(rsc)/./lib/webhook.js\");\nclass QueueMiddleware {\n    static async validateLicenseFeatures(req, res, next) {\n        try {\n            const db = getDatabase();\n            const webhook = getWebhookManager();\n            const userId = req.user?.id;\n            if (!userId) {\n                return res.status(401).json({\n                    error: \"Authentication required\"\n                });\n            }\n            // Get user's license features\n            const features = db.getUserLicenseFeatures(userId);\n            req.licenseFeatures = features;\n            // Check specific feature requirements based on the endpoint\n            const endpoint = req.route?.path || req.path;\n            if (endpoint.includes(\"/batch\")) {\n                // Validate batch processing access\n                if (features.max_accounts_per_batch === 0) {\n                    await webhook.sendLicenseViolation(req.user.username, \"Batch Processing Access Denied\", \"User attempted to access batch processing without proper license\");\n                    return res.status(403).json({\n                        error: \"Batch processing not available for your license\",\n                        feature: \"max_accounts_per_batch\",\n                        current_limit: 0\n                    });\n                }\n            }\n            if (endpoint.includes(\"/schedule\")) {\n                // Validate scheduling access\n                if (!features.scheduling_access) {\n                    await webhook.sendLicenseViolation(req.user.username, \"Scheduling Access Denied\", \"User attempted to access scheduling without proper license\");\n                    return res.status(403).json({\n                        error: \"Scheduling not available for your license\",\n                        feature: \"scheduling_access\",\n                        current_access: false\n                    });\n                }\n            }\n            next();\n        } catch (error) {\n            console.error(\"License validation error:\", error);\n            res.status(500).json({\n                error: \"License validation failed\"\n            });\n        }\n    }\n    static async validateBatchSize(req, res, next) {\n        try {\n            const accounts = req.body.accounts || [];\n            const maxAccounts = req.licenseFeatures?.max_accounts_per_batch || 0;\n            if (maxAccounts > 0 && accounts.length > maxAccounts) {\n                const webhook = getWebhookManager();\n                await webhook.sendLicenseViolation(req.user.username, \"Batch Size Limit Exceeded\", `Attempted to submit ${accounts.length} accounts, limit is ${maxAccounts}`);\n                return res.status(403).json({\n                    error: \"Batch size exceeds license limit\",\n                    submitted_count: accounts.length,\n                    max_allowed: maxAccounts\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Batch size validation error:\", error);\n            res.status(500).json({\n                error: \"Batch size validation failed\"\n            });\n        }\n    }\n    static async validatePriorityLevel(req, res, next) {\n        try {\n            const requestedPriority = req.body.priority_level;\n            const maxPriority = req.licenseFeatures?.priority_level || 0;\n            if (requestedPriority !== undefined && requestedPriority > maxPriority) {\n                const webhook = getWebhookManager();\n                await webhook.sendLicenseViolation(req.user.username, \"Priority Level Exceeded\", `Attempted to set priority ${requestedPriority}, max allowed is ${maxPriority}`);\n                return res.status(403).json({\n                    error: \"Priority level exceeds license limit\",\n                    requested_priority: requestedPriority,\n                    max_allowed: maxPriority\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Priority validation error:\", error);\n            res.status(500).json({\n                error: \"Priority validation failed\"\n            });\n        }\n    }\n    static async checkScheduleConflicts(req, res, next) {\n        try {\n            const db = getDatabase();\n            const webhook = getWebhookManager();\n            const { scheduled_time, duration_minutes = 30 } = req.body;\n            const userId = req.user.id;\n            if (scheduled_time) {\n                const conflicts = db.checkScheduleConflicts(userId, scheduled_time, duration_minutes);\n                if (conflicts.length > 0) {\n                    await webhook.sendScheduleConflict(req.user.username, scheduled_time, `${conflicts.length} conflicting schedule(s) found`);\n                    return res.status(409).json({\n                        error: \"Schedule conflict detected\",\n                        requested_time: scheduled_time,\n                        conflicts: conflicts.map((c)=>({\n                                id: c.id,\n                                scheduled_time: c.scheduled_time,\n                                duration_minutes: c.duration_minutes\n                            }))\n                    });\n                }\n            }\n            next();\n        } catch (error) {\n            console.error(\"Schedule conflict check error:\", error);\n            res.status(500).json({\n                error: \"Schedule conflict check failed\"\n            });\n        }\n    }\n    static async logQueueActivity(req, res, next) {\n        try {\n            const db = getDatabase();\n            const originalSend = res.send;\n            res.send = function(data) {\n                // Log successful queue operations\n                if (res.statusCode >= 200 && res.statusCode < 300) {\n                    const action = req.method + \"_\" + req.route?.path?.replace(/[\\/\\:]/g, \"_\").toUpperCase();\n                    const details = {\n                        endpoint: req.originalUrl,\n                        method: req.method,\n                        status: res.statusCode\n                    };\n                    db.logActivity(req.user?.id, action, JSON.stringify(details));\n                }\n                originalSend.call(this, data);\n            };\n            next();\n        } catch (error) {\n            console.error(\"Activity logging error:\", error);\n            next(); // Don't block the request for logging errors\n        }\n    }\n    static gracefulDegradation(featureCheck) {\n        return (req, res, next)=>{\n            try {\n                const hasFeature = featureCheck(req.licenseFeatures);\n                if (!hasFeature) {\n                    // Instead of blocking, provide limited functionality\n                    req.degradedMode = true;\n                    req.degradationReason = \"License feature not available\";\n                }\n                next();\n            } catch (error) {\n                console.error(\"Graceful degradation error:\", error);\n                next();\n            }\n        };\n    }\n    static async rateLimitByLicense(req, res, next) {\n        try {\n            const db = getDatabase();\n            const userId = req.user.id;\n            const features = req.licenseFeatures;\n            // Implement rate limiting based on license features\n            const recentBatches = db.getQueueBatches(userId, null, 10, 0);\n            const recentBatchCount = recentBatches.filter((batch)=>{\n                const batchTime = new Date(batch.created_at);\n                const hourAgo = new Date(Date.now() - 60 * 60 * 1000);\n                return batchTime > hourAgo;\n            }).length;\n            // Basic rate limiting: higher priority licenses get more requests\n            const maxBatchesPerHour = Math.max(1, features.priority_level);\n            if (recentBatchCount >= maxBatchesPerHour) {\n                return res.status(429).json({\n                    error: \"Rate limit exceeded\",\n                    limit: maxBatchesPerHour,\n                    current: recentBatchCount,\n                    reset_time: new Date(Date.now() + 60 * 60 * 1000).toISOString()\n                });\n            }\n            next();\n        } catch (error) {\n            console.error(\"Rate limiting error:\", error);\n            next(); // Don't block for rate limiting errors\n        }\n    }\n}\nmodule.exports = QueueMiddleware;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/queueMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./lib/webhook.js":
/*!************************!*\
  !*** ./lib/webhook.js ***!
  \************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\nclass WebhookManager {\n    constructor(){\n        this.discordWebhookUrl = \"https://discord.com/api/webhooks/1391133719351394314/VEY8LIMPErwXKx8ZGgsJvhLwbjfqHEtzhsbiZfwHb3aSUp9htUtWDy9mrW4N2LhuD6c9\";\n    }\n    async sendDiscordNotification(title, description, color = 0x3498db, fields = []) {\n        const embed = {\n            title,\n            description,\n            color,\n            timestamp: new Date().toISOString(),\n            fields,\n            footer: {\n                text: \"SparxReader Queue System\"\n            }\n        };\n        const payload = {\n            embeds: [\n                embed\n            ]\n        };\n        try {\n            await this.sendWebhook(this.discordWebhookUrl, payload);\n            console.log(\"Discord notification sent:\", title);\n        } catch (error) {\n            console.error(\"Failed to send Discord notification:\", error.message);\n        }\n    }\n    async sendLicenseViolation(username, violation, details) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDEAB License Violation Detected\", `User **${username}** attempted an unauthorized action`, 0xe74c3c, [\n            {\n                name: \"Violation Type\",\n                value: violation,\n                inline: true\n            },\n            {\n                name: \"Details\",\n                value: details,\n                inline: false\n            },\n            {\n                name: \"Timestamp\",\n                value: new Date().toLocaleString(),\n                inline: true\n            }\n        ]);\n    }\n    async sendScheduleConflict(username, scheduledTime, conflictDetails) {\n        await this.sendDiscordNotification(\"⚠️ Schedule Conflict Detected\", `Schedule conflict for user **${username}**`, 0xf39c12, [\n            {\n                name: \"Requested Time\",\n                value: new Date(scheduledTime).toLocaleString(),\n                inline: true\n            },\n            {\n                name: \"Conflict Details\",\n                value: conflictDetails,\n                inline: false\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            }\n        ]);\n    }\n    async sendPriorityAdjustment(jobId, oldPriority, newPriority, reason, adminUser = null) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDD04 Priority Adjustment\", `Job priority has been adjusted`, 0x9b59b6, [\n            {\n                name: \"Job ID\",\n                value: jobId.toString(),\n                inline: true\n            },\n            {\n                name: \"Old Priority\",\n                value: oldPriority.toString(),\n                inline: true\n            },\n            {\n                name: \"New Priority\",\n                value: newPriority.toString(),\n                inline: true\n            },\n            {\n                name: \"Reason\",\n                value: reason,\n                inline: false\n            },\n            ...adminUser ? [\n                {\n                    name: \"Admin User\",\n                    value: adminUser,\n                    inline: true\n                }\n            ] : []\n        ]);\n    }\n    async sendBatchCreated(username, batchName, accountCount, scheduledTime = null) {\n        await this.sendDiscordNotification(\"\\uD83D\\uDCE6 New Batch Created\", `User **${username}** created a new batch`, 0x2ecc71, [\n            {\n                name: \"Batch Name\",\n                value: batchName,\n                inline: true\n            },\n            {\n                name: \"Account Count\",\n                value: accountCount.toString(),\n                inline: true\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            },\n            ...scheduledTime ? [\n                {\n                    name: \"Scheduled Time\",\n                    value: new Date(scheduledTime).toLocaleString(),\n                    inline: false\n                }\n            ] : []\n        ]);\n    }\n    async sendBatchCompleted(username, batchName, processedCount, failedCount, duration) {\n        await this.sendDiscordNotification(\"✅ Batch Completed\", `Batch processing completed for **${username}**`, 0x27ae60, [\n            {\n                name: \"Batch Name\",\n                value: batchName,\n                inline: true\n            },\n            {\n                name: \"Processed\",\n                value: processedCount.toString(),\n                inline: true\n            },\n            {\n                name: \"Failed\",\n                value: failedCount.toString(),\n                inline: true\n            },\n            {\n                name: \"Duration\",\n                value: `${Math.round(duration / 60)} minutes`,\n                inline: true\n            },\n            {\n                name: \"User\",\n                value: username,\n                inline: true\n            }\n        ]);\n    }\n    async sendQueueAlert(alertType, message, details = {}) {\n        const colors = {\n            \"high_load\": 0xe67e22,\n            \"system_error\": 0xe74c3c,\n            \"maintenance\": 0x3498db,\n            \"info\": 0x95a5a6 // Gray\n        };\n        const icons = {\n            \"high_load\": \"\\uD83D\\uDD25\",\n            \"system_error\": \"\\uD83D\\uDCA5\",\n            \"maintenance\": \"\\uD83D\\uDD27\",\n            \"info\": \"ℹ️\"\n        };\n        const fields = Object.entries(details).map(([key, value])=>({\n                name: key.replace(/_/g, \" \").replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                value: value.toString(),\n                inline: true\n            }));\n        await this.sendDiscordNotification(`${icons[alertType] || \"ℹ️\"} Queue System Alert`, message, colors[alertType] || 0x95a5a6, fields);\n    }\n    sendWebhook(webhookUrl, payload) {\n        return new Promise((resolve, reject)=>{\n            const url = new URL(webhookUrl);\n            const data = JSON.stringify(payload);\n            const options = {\n                hostname: url.hostname,\n                port: url.port || 443,\n                path: url.pathname + url.search,\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Content-Length\": Buffer.byteLength(data),\n                    \"User-Agent\": \"SparxReader-Queue-System/1.0\"\n                }\n            };\n            const req = https.request(options, (res)=>{\n                let responseData = \"\";\n                res.on(\"data\", (chunk)=>{\n                    responseData += chunk;\n                });\n                res.on(\"end\", ()=>{\n                    if (res.statusCode >= 200 && res.statusCode < 300) {\n                        resolve(responseData);\n                    } else {\n                        reject(new Error(`Webhook request failed with status ${res.statusCode}: ${responseData}`));\n                    }\n                });\n            });\n            req.on(\"error\", (error)=>{\n                reject(error);\n            });\n            req.on(\"timeout\", ()=>{\n                req.destroy();\n                reject(new Error(\"Webhook request timed out\"));\n            });\n            req.setTimeout(10000); // 10 second timeout\n            req.write(data);\n            req.end();\n        });\n    }\n}\n// Export singleton instance\nlet webhookInstance = null;\nfunction getWebhookManager() {\n    if (!webhookInstance) {\n        webhookInstance = new WebhookManager();\n    }\n    return webhookInstance;\n}\nmodule.exports = {\n    getWebhookManager,\n    WebhookManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/webhook.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs","vendor-chunks/uuid"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fschedule%2Froute&page=%2Fapi%2Fqueue%2Fschedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fschedule%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();