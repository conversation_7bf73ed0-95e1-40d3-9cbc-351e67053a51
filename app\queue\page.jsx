'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import ScheduleCalendar from './components/ScheduleCalendar';

export default function QueueDashboard() {
  const [queueStatus, setQueueStatus] = useState(null);
  const [batches, setBatches] = useState([]);
  const [schedules, setSchedules] = useState([]);
  const [priorityLevels, setPriorityLevels] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const router = useRouter();

  // Form states
  const [batchForm, setBatchForm] = useState({
    batch_name: '',
    login_type: '',
    accounts: [{ school: '', email: '', password: '', login_type: 'regular' }],
    scheduled_time: '',
    priority_override: ''
  });

  useEffect(() => {
    loadQueueData();
    
    // Set up auto-refresh for real-time updates
    const interval = setInterval(() => {
      loadQueueData();
    }, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  const loadQueueData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      if (!token) {
        router.push('/login');
        return;
      }

      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // Load queue status
      const statusResponse = await fetch('/api/queue/status?detailed=true', { headers });
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        setQueueStatus(statusData);
      }

      // Load batches
      const batchResponse = await fetch('/api/queue/batch', { headers });
      if (batchResponse.ok) {
        const batchData = await batchResponse.json();
        setBatches(batchData.batches || []);
      }

      // Load schedules
      const scheduleResponse = await fetch('/api/queue/schedule', { headers });
      if (scheduleResponse.ok) {
        const scheduleData = await scheduleResponse.json();
        setSchedules(scheduleData.schedules || []);
      }

      // Load priority levels
      const priorityResponse = await fetch('/api/queue/priority-levels', { headers });
      if (priorityResponse.ok) {
        const priorityData = await priorityResponse.json();
        setPriorityLevels(priorityData);
      }

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBatchSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/queue/batch', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(batchForm)
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Batch "${result.batch.name}" created successfully!`);
        setBatchForm({
          batch_name: '',
          login_type: '',
          accounts: [{ school: '', email: '', password: '' }],
          scheduled_time: '',
          priority_override: ''
        });
        loadQueueData(); // Refresh data
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (err) {
      alert(`Error: ${err.message}`);
    }
  };

  const addAccount = () => {
    setBatchForm(prev => ({
      ...prev,
      accounts: [...prev.accounts, { school: '', email: '', password: '' }]
    }));
  };

  const removeAccount = (index) => {
    setBatchForm(prev => ({
      ...prev,
      accounts: prev.accounts.filter((_, i) => i !== index)
    }));
  };

  const updateAccount = (index, field, value) => {
    setBatchForm(prev => ({
      ...prev,
      accounts: prev.accounts.map((account, i) => 
        i === index ? { ...account, [field]: value } : account
      )
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading queue dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-xl mb-4">⚠️ Error</div>
          <p className="text-gray-300">{error}</p>
          <button
            onClick={loadQueueData}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white">Queue Dashboard</h1>
          <p className="mt-2 text-gray-300">Manage your batch processing and scheduling</p>
        </div>

        {/* License Features Banner */}
        {queueStatus?.license_features && (
          <div className="mb-6 bg-gray-900 border border-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-400 mb-2">Your License Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {queueStatus.license_features.max_accounts_per_batch || 'Unlimited'}
                </div>
                <div className="text-sm text-gray-300">Max Accounts per Batch</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {queueStatus.license_features.priority_level}
                </div>
                <div className="text-sm text-gray-300">Priority Level</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {queueStatus.license_features.scheduling_access ? '✅' : '❌'}
                </div>
                <div className="text-sm text-gray-300">Scheduling Access</div>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', name: 'Overview' },
              { id: 'batches', name: 'Batches' },
              { id: 'schedule', name: 'Schedule' },
              { id: 'create', name: 'Create Batch' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-400 text-blue-400'
                    : 'border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-600'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Queue Status Cards */}
            {queueStatus && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg p-6">
                  <div className="text-sm font-medium text-gray-400">Total Batches</div>
                  <div className="text-2xl font-bold text-white">
                    {queueStatus.user_queue_status.total_batches}
                  </div>
                </div>
                <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg p-6">
                  <div className="text-sm font-medium text-gray-400">Active Jobs</div>
                  <div className="text-2xl font-bold text-blue-400">
                    {queueStatus.user_queue_status.queued_jobs + queueStatus.user_queue_status.processing_jobs}
                  </div>
                </div>
                <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg p-6">
                  <div className="text-sm font-medium text-gray-400">Completed Jobs</div>
                  <div className="text-2xl font-bold text-green-400">
                    {queueStatus.user_queue_status.completed_jobs}
                  </div>
                </div>
                <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg p-6">
                  <div className="text-sm font-medium text-gray-400">Estimated Wait</div>
                  <div className="text-2xl font-bold text-orange-400">
                    {queueStatus.estimated_wait_time.formatted}
                  </div>
                </div>
              </div>
            )}

            {/* Queue Positions */}
            {queueStatus?.queue_positions && queueStatus.queue_positions.length > 0 && (
              <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg">
                <div className="px-6 py-4 border-b border-gray-700">
                  <h3 className="text-lg font-medium text-white">Your Queue Positions</h3>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    {queueStatus.queue_positions.map((position, index) => (
                      <div key={position.id} className="flex justify-between items-center p-3 bg-gray-800 border border-gray-700 rounded">
                        <div>
                          <div className="font-medium text-white">Job #{position.id}</div>
                          <div className="text-sm text-gray-400">Priority: {position.effective_priority}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-blue-400">#{position.queue_position}</div>
                          <div className="text-sm text-gray-400">in queue</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'batches' && (
          <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg">
            <div className="px-6 py-4 border-b border-gray-700">
              <h3 className="text-lg font-medium text-white">Your Batches</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-800">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Batch Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Created
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-900 divide-y divide-gray-700">
                  {batches.map((batch) => (
                    <tr key={batch.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                        {batch.batch_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          batch.status === 'completed' ? 'bg-green-900 text-green-300 border border-green-700' :
                          batch.status === 'processing' ? 'bg-blue-900 text-blue-300 border border-blue-700' :
                          batch.status === 'failed' ? 'bg-red-900 text-red-300 border border-red-700' :
                          'bg-yellow-900 text-yellow-300 border border-yellow-700'
                        }`}>
                          {batch.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                        {batch.processed_accounts}/{batch.total_accounts}
                        {batch.failed_accounts > 0 && (
                          <span className="text-red-400 ml-1">({batch.failed_accounts} failed)</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                        {batch.priority_level}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        {new Date(batch.created_at).toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'schedule' && (
          <div>
            {!queueStatus?.license_features?.scheduling_access ? (
              <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg">
                <div className="text-center py-12">
                  <div className="text-gray-500 text-6xl mb-4">📅</div>
                  <h3 className="text-lg font-medium text-white mb-2">Scheduling Not Available</h3>
                  <p className="text-gray-400">Your current license doesn't include scheduling access.</p>
                  <p className="text-gray-400">Please upgrade your license to use this feature.</p>
                </div>
              </div>
            ) : (
              <ScheduleCalendar
                schedules={schedules}
                onScheduleSelect={(schedule) => {
                  console.log('Selected schedule:', schedule);
                  // Handle schedule selection (e.g., show details modal)
                }}
                onCreateSchedule={async (scheduleData) => {
                  try {
                    const token = localStorage.getItem('token');
                    const response = await fetch('/api/queue/schedule', {
                      method: 'POST',
                      headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify(scheduleData)
                    });

                    if (response.ok) {
                      const result = await response.json();
                      alert('Schedule created successfully!');
                      loadQueueData(); // Refresh data
                    } else {
                      const error = await response.json();
                      alert(`Error: ${error.error}`);
                    }
                  } catch (err) {
                    alert(`Error: ${err.message}`);
                  }
                }}
              />
            )}
          </div>
        )}

        {activeTab === 'create' && (
          <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-lg">
            <div className="px-6 py-4 border-b border-gray-700">
              <h3 className="text-lg font-medium text-white">Create New Batch</h3>
            </div>
            <form onSubmit={handleBatchSubmit} className="p-6 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300">Batch Name</label>
                <input
                  type="text"
                  value={batchForm.batch_name}
                  onChange={(e) => setBatchForm(prev => ({ ...prev, batch_name: e.target.value }))}
                  className="mt-1 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Login Type</label>
                <select
                  value={batchForm.login_type}
                  onChange={(e) => setBatchForm(prev => ({ ...prev, login_type: e.target.value }))}
                  className="mb-4 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select Login Type</option>
                  <option value="normal">👤 Normal Login</option>
                  <option value="google">🔍 Google Login</option>
                  <option value="microsoft">🏢 Microsoft Login</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Accounts</label>
                {batchForm.accounts.map((account, index) => (
                  <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border border-gray-600 bg-gray-800 rounded">
                    <input
                      type="text"
                      placeholder="School"
                      value={account.school}
                      onChange={(e) => updateAccount(index, 'school', e.target.value)}
                      className="bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400"
                      required
                    />
                    <input
                      type="email"
                      placeholder="Email"
                      value={account.email}
                      onChange={(e) => updateAccount(index, 'email', e.target.value)}
                      className="bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400"
                      required
                    />
                    <input
                      type="password"
                      placeholder="Password"
                      value={account.password}
                      onChange={(e) => updateAccount(index, 'password', e.target.value)}
                      className="bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => removeAccount(index)}
                      className="px-3 py-2 text-red-400 border border-red-600 rounded-md hover:bg-red-900 transition-colors"
                      disabled={batchForm.accounts.length === 1}
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addAccount}
                  className="px-4 py-2 text-blue-400 border border-blue-600 rounded-md hover:bg-blue-900 transition-colors"
                  disabled={
                    queueStatus?.license_features?.max_accounts_per_batch > 0 &&
                    batchForm.accounts.length >= queueStatus.license_features.max_accounts_per_batch
                  }
                >
                  Add Account
                </button>
                {queueStatus?.license_features?.max_accounts_per_batch > 0 && (
                  <p className="text-sm text-gray-400 mt-1">
                    Maximum {queueStatus.license_features.max_accounts_per_batch} accounts per batch
                  </p>
                )}
              </div>

              {queueStatus?.license_features?.scheduling_access && (
                <div>
                  <label className="block text-sm font-medium text-gray-300">Schedule Time (Optional)</label>
                  <input
                    type="datetime-local"
                    value={batchForm.scheduled_time}
                    onChange={(e) => setBatchForm(prev => ({ ...prev, scheduled_time: e.target.value }))}
                    className="mt-1 block w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              )}

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setBatchForm({
                    batch_name: '',
                    login_type: '',
                    accounts: [{ school: '', email: '', password: '' }],
                    scheduled_time: '',
                    priority_override: ''
                  })}
                  className="px-4 py-2 text-gray-300 border border-gray-600 rounded-md hover:bg-gray-800 transition-colors"
                >
                  Reset
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                >
                  Create Batch
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}