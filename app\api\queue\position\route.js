import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { withMiddleware } from '../../../../lib/queueMiddleware';

// GET - Get queue position for a job
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const url = new URL(request.url);
      const jobId = url.searchParams.get('job_id');

      if (!jobId) {
        return NextResponse.json({
          error: 'job_id parameter is required'
        }, { status: 400 });
      }

      const db = getDatabase();
      
      // Get the specific job
      const jobStmt = db.db.prepare('SELECT * FROM queue_jobs WHERE id = ?');
      const job = jobStmt.get(parseInt(jobId));

      if (!job) {
        return NextResponse.json({
          error: 'Job not found'
        }, { status: 404 });
      }

      // Verify ownership (user can only check their own jobs unless admin)
      if (job.user_id !== req.user.id && req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Access denied'
        }, { status: 403 });
      }

      // If job is completed or failed, return status
      if (job.status === 'completed' || job.status === 'failed') {
        return NextResponse.json({
          success: true,
          position: 0,
          status: job.status,
          result: job.result ? JSON.parse(job.result) : null,
          error: job.error
        });
      }

      // If job is processing, return position 1
      if (job.status === 'processing') {
        return NextResponse.json({
          success: true,
          position: 1,
          status: 'processing'
        });
      }

      // Calculate queue position for pending jobs
      const positionStmt = db.db.prepare(`
        SELECT COUNT(*) as position 
        FROM queue_jobs 
        WHERE status = 'pending' 
        AND (
          priority > ? OR 
          (priority = ? AND created_at < ?)
        )
      `);
      
      const positionResult = positionStmt.get(job.priority, job.priority, job.created_at);
      const position = positionResult.position + 1; // Add 1 because position is 1-based

      return NextResponse.json({
        success: true,
        position: position,
        status: job.status,
        estimated_wait_minutes: Math.max(1, Math.ceil(position * 2)) // Rough estimate: 2 minutes per job
      });

    } catch (error) {
      console.error('Queue position error:', error);
      return NextResponse.json({
        error: 'Failed to get queue position',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}
